android {
    signingConfigs {
        release {
            def properties
            def propertiesFile = rootProject.file('signing.properties')
            if (propertiesFile.exists()) {
                properties = new Properties()
                properties.load(propertiesFile.newDataInputStream())
            }
            def getString = { String propertyName, String environmentName, String prompt ->
                return properties?.get(propertyName) ?: System.getenv(environmentName) ?:
                        System.console()?.readLine('\n' + prompt + ': ')
            }

            storeFile getString('storeFile', 'STORE_FILE', 'Store file')?.with(rootProject.&file)
            storePassword getString('storePassword', 'STORE_PASSWORD', 'Store password')
            keyAlias getString('keyAlias', 'KEY_ALIAS', 'Key alias')
            keyPassword getString('keyPassword', 'KEY_PASSWORD', 'Key password')
        }
    }
}
