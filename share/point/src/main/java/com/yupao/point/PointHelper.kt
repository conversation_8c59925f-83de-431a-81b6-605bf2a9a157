package com.yupao.point

import com.yupao.point.api.PointApi
import com.yupao.point.model.EventParamsModel
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 埋点帮助类
 *
 * <p>创建时间：2025/4/16/016</p>
 *
 * <AUTHOR>
 */
object PointHelper {
    private var createPointApi: (() -> PointApi)? = null

    fun initPoint(createPointApi: (() -> PointApi)) {
        this.createPointApi = createPointApi
    }

    private val pointApi: PointApi by lazy {
        createPointApi?.invoke() ?: throw IllegalStateException("请先调用initPoint")
    }

    /**
     * 埋点数据上报
     *
     * @param eventName 事件名
     * @param params 参数
     */
    fun doPoint(eventName: String, params: Map<String, Any?>) {
        GlobalScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val result =
                    <EMAIL>(EventParamsModel(eventName, params))
                if (!result.isOK()) {
                    YLog.w("PointHelper", "do point failed, name: ${eventName}, params: $params")
                }
            }.onFailure {
                YLog.printException("PointHelper", it, "name: ${eventName}, params: $params")
            }.getOrNull()
        }
    }
}