package com.yupao.point.api

import com.google.gson.JsonObject
import com.yupao.data.net.yupao.JavaNetEntity
import com.yupao.point.model.EventParamsModel
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2025/4/16/016</p>
 *
 * <AUTHOR>
 */
interface PointApi {

    /**
     * 上报数据
     * http://yapi.3pvr.com/project/1396/interface/api/61337
     * @param model [ReportNetModel] 上传参数
     */
    @POST("/eventreport/v1/event/report")
    suspend fun eventReport(
        @Body model: EventParamsModel
    ): JavaNetEntity<Any>
}