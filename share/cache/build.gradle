plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
}

android {
    namespace 'com.yupao.share.cache'
    compileSdk 34

    defaultConfig {
        minSdk 24
        targetSdk 30
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            crunchPngs false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        pre {
            initWith(debug)
            crunchPngs false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation rootProject.ext.dependencies.appcompat
    implementation rootProject.ext.dependencies.coreKtx
    implementation rootProject.ext.dependencies.material
    implementation rootProject.ext.dependencies.yapm
    implementation rootProject.ext.dependencies.ylog
    implementation rootProject.ext.dependencies.utils_system
    implementation rootProject.ext.dependencies.gson

    //room
    kapt rootProject.ext.dependencies.roomCompiler
    implementation rootProject.ext.dependencies.roomRuntime
    implementation rootProject.ext.dependencies.roomKtx
    //    implementation rootProject.ext.dependencies.hilt
    // room需要引用，否则会报错
    kapt rootProject.ext.dependencies.hiltCompiler
//    kapt rootProject.ext.dependencies.hiltHiltCompiler
}