package com.yupao.share.cache.db.kv

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/11/011</p>
 *
 * <AUTHOR>
 */
object KvDbHelper {

    private val dao = CacheDatabaseModule.getDataBase().getPairDao()

    suspend fun save(key: String, value: String, invalidTime: Long? = null) {
        val model = dao.query(key)
        if (model == null) {
            // 最多缓存时间
            dao.insert(PairLocalModel.create(key, value, invalidTime = invalidTime))
        } else {
            model.update(value, invalidTime = invalidTime)
            dao.update(model)
        }
    }

    suspend fun get(key: String): PairLocalModel? {
       return dao.query(key)
    }
}