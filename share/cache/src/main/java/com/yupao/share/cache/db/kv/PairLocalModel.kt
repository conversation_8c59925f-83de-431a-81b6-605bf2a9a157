package com.yupao.share.cache.db.kv

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * kv键值对实体
 *
 * <p>创建时间：2024/10/26/026</p>
 *
 * <AUTHOR>
 */
@Keep
@Entity(tableName = "kv")
data class PairLocalModel(
    // 键
    val key: String,
    // 值
    var value: String?,
    // 扩展信息
    var ext: String?,
    // 添加时间
    val addTime: Long,
    // 更新时间
    var updateTime: Long,
    // 失效时间
    var invalidTime: Long?,
    @PrimaryKey(autoGenerate = true)
    var id: Int? = null,
) {
    companion object {
        fun create(key: String, value: String?, ext: String? = null, invalidTime: Long? = null): PairLocalModel {
            val now = System.currentTimeMillis()
            return PairLocalModel(
                key = key,
                value = value,
                ext = ext,
                addTime = now,
                updateTime = now,
                invalidTime = invalidTime?.let { it  + now },
            )
        }
    }

    fun update(value: String?, ext: String? = null, invalidTime: Long? = null) {
        val now = System.currentTimeMillis()
        this.value = value
        this.ext = ext
        this.updateTime = now
        if (invalidTime != null) {
            this.invalidTime = invalidTime + now
        }
    }

    fun fetchValidValue(): String? {
        val invalidTime = this.invalidTime
        if (invalidTime != null && invalidTime < System.currentTimeMillis()) {
            return null
        }
        return value
    }
}
