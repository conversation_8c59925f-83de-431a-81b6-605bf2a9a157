package com.yupao.share.cache.db.kv

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.yupao.share.cache.db.kv.PairLocalModel

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/10/26/026</p>
 *
 * <AUTHOR>
 */
@Dao
interface PairDao {

    /**
     * 根据key查询
     */
    @Query("select * from kv where key = :key LIMIT 1")
    suspend fun query(key: String?): PairLocalModel?

    /**
     * 更新
     */
    @Update
    suspend fun update(model: PairLocalModel?)

    /**
     * 更新
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(model: PairLocalModel?)
}