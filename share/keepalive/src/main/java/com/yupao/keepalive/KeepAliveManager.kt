package com.yupao.keepalive

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.app.Service
import com.lzf.easyfloat.interfaces.OnPermissionResult
import com.lzf.easyfloat.permission.PermissionUtils
import com.yupao.keepalive.wakeup.AlarmWakeup
import com.yupao.yapm.utils.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 保活
 *
 * <p>创建时间：2024/12/11/011</p>
 *
 * <AUTHOR>
 */
object KeepAliveManager {

    /**
     * 唤醒间隔
     */
    private const val wakeupInterval = 60 * 1000L

    /**
     * 屏幕关闭限制时间
     */
    private const val screenOffLimitTime = 10 * 60 * 1000L

    /**
     * 锁屏限制时间
     */
    private const val screenLockLimitTime = 10 * 60 * 1000L

    @SuppressLint("StaticFieldLeak")
    private var wakeupHandler: AlarmWakeup? = null

    private var cmdExecute: (suspend (String) -> List<String>)? = null

    private val activityList = mutableListOf<String>()

    private val lock = AtomicBoolean(false)

    fun init(context: Application, serviceClass: Class<out Service>, activityList: List<String>,cmdExecute: (suspend (String) -> List<String>)) {
        this.wakeupHandler = AlarmWakeup(context, serviceClass)
        this.cmdExecute = cmdExecute
        this.activityList.clear()
        this.activityList.addAll(activityList)
        ScreenManager.init(cmdExecute)
        disabledFreezer()
    }

    /**
     * 应用进程冻结禁用
     */
    fun disabledFreezer() {
        GlobalScope.launch(Dispatchers.IO) {
            cmdExecute?.invoke("settings put global cached_apps_freezer disabled")
        }
    }

    suspend fun wakeupDelay(isOpenWakeup: Boolean) {
        if (lock.get()) {
            return
        }
        lock.set(true)
        try {
            withContext(Dispatchers.Main) {
                kotlin.runCatching {
                    KeepAliveFloatWindow.showFloatWindow()
                }.onFailure {
                    YLog.printException("KeepAliveManager showFloatWindow", it)
                }
            }
            if (!isOpenWakeup) return
            val wakeupHandler = this.wakeupHandler ?: return
            kotlin.runCatching {
                val currTime = System.currentTimeMillis()
                val wakeupTime = currTime + wakeupInterval
                YLog.i("wakeupTime: ${TimeUtil.formatCurrTime(wakeupTime)}")
                wakeupHandler.setWakeupTime(wakeupTime)
                ScreenManager.updateScreenState()
                checkScreenState()
            }.onFailure {
                YLog.printException("KeepAliveManager wakeupDelay", it)
            }
        } finally {
            lock.set(false)
        }
    }

    private suspend fun checkScreenState() {
        val screenOffTime = ScreenManager.getScreenOffTime()
        val screenLockTime = ScreenManager.getScreenLockTime()
        val currTime = TimeUtil.getCurrTime()
        YLog.i("KeepAliveManager screenOffTime: $screenOffTime, screenLockTime: $screenLockTime")
        val isScreenOver = screenOffTime > 0 && (currTime - screenOffTime > screenOffLimitTime)
        val isLockOver = screenLockTime > 0 && (currTime - screenLockTime > screenLockLimitTime)
        if (isScreenOver || isLockOver) {
            // 解锁
            if (isLockOver) {
                ScreenManager.performUnlock()
                YLog.i("KeepAliveManager performUnlock")
                delay(1000)
            } else {
                // 点亮屏幕
                ScreenManager.performScreenOn()
                YLog.i("KeepAliveManager performScreenOn")
                delay(1000)
            }
            // 启动页面
            // adb shell am start -n com.example.myapp/.MainActivity
            activityList.forEach {
                YLog.i("KeepAliveManager perform launch: $it")
                delay(3000)
                this.cmdExecute?.invoke("am start -n $it")
            }
        }
    }

    fun checkPermission(activity: Activity) {
        if (PermissionUtils.checkPermission(activity)) {
            YLog.i("KeepAliveManager checkPermission")
            return
        }
        PermissionUtils.requestPermission(activity, object : OnPermissionResult {
            override fun permissionResult(isOpen: Boolean) {
                YLog.i("KeepAliveManager checkPermission isOpen: $isOpen")
            }
        })
    }
}