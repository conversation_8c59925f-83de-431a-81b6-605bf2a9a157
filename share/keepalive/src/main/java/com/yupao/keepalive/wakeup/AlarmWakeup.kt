package com.yupao.keepalive.wakeup

import android.app.AlarmManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Context.ALARM_SERVICE
import android.content.Intent
import android.os.Build
import com.yupao.ylog.YLog

/**
 * 精确闹钟唤醒
 *
 * <p>创建时间：2024/12/11/011</p>
 *
 * <AUTHOR>
 */
class AlarmWakeup(
    private val context: Context,
    private val serviceClass: Class<out Service>,
) {

    fun setWakeupTime(time: Long): Boolean {
        val alarmManager = context.getSystemService(ALARM_SERVICE) as AlarmManager
        val intent = Intent(context, serviceClass)
        intent.putExtra("from", "AlarmManager")
        val pendingIntent = PendingIntent.getService(
            context,
            119,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT
        )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (!alarmManager.canScheduleExactAlarms()) {
                YLog.w("AlarmWakeup: can not schedule exact alarms")
                return false
            }
        }
        alarmManager.setAlarmClock(AlarmManager.AlarmClockInfo(time, pendingIntent), pendingIntent)
        alarmManager.setExactAndAllowWhileIdle(
            AlarmManager.RTC_WAKEUP,
            time,
            pendingIntent
        )
        return true
    }
}