package com.yupao.keepalive

import com.yupao.yapm.utils.TimeUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/12/11/011</p>
 *
 * <AUTHOR>
 */
object ScreenManager {

    /**
     * 熄屏时间
     */
    private var screenOffTime: Long = 0

    /**
     * 屏幕锁屏时间
     */
    private var screenLockTime: Long = 0

    private var cmdExecute: (suspend (String) -> List<String>)? = null

    fun init(cmdExecute: suspend (String) -> List<String>) {
        this.cmdExecute = cmdExecute
    }

    fun screenOff() {
        if (screenOffTime > 0) {
            return
        }
        screenOffTime = TimeUtil.getCurrTime()
    }

    fun screenOn() {
        screenOffTime = 0
    }

    fun screenLock() {
        if (screenLockTime > 0) {
            return
        }
        screenLockTime = TimeUtil.getCurrTime()
    }

    fun screenUnlock() {
        screenLockTime = 0
    }

    suspend fun updateScreenState() {
        val execute = this.cmdExecute ?: return
        val result = withContext(Dispatchers.IO) {
            execute("dumpsys window policy | grep -E \"screenState|showing\"")
        }
        if (result.isEmpty()) {
            return
        }
        val map = mutableMapOf<String, String?>()
        result.forEach {
            val list = it.trim().split("=")
            if (list.isEmpty()) {
                return@forEach
            }
            map[list[0]] = list.getOrNull(1)
        }
        val showing = map["showing"]
        if (showing == "true") {
            screenLock()
        } else if (showing == "false") {
            screenUnlock()
        }
        val screenState = map["screenState"]
        if (screenState == "SCREEN_STATE_OFF") {
            screenOff()
        } else if (screenState == "SCREEN_STATE_ON") {
            screenOn()
        }
    }

    fun getScreenOffTime(): Long {
        return screenOffTime
    }

    fun getScreenLockTime(): Long {
        return screenLockTime
    }

    suspend fun performUnlock() {
        withContext(Dispatchers.IO) {
            if (screenOffTime > 0) {
                <EMAIL>?.invoke("input keyevent 82 ; input swipe 500 800 500 300 500")
            } else {
                <EMAIL>?.invoke("input swipe 500 800 500 300 500")
            }
        }
        this.screenUnlock()
    }

    suspend fun performScreenOn() {
        withContext(Dispatchers.IO) {
            <EMAIL>?.invoke("input keyevent 26")
        }
        this.screenOn()
    }
}