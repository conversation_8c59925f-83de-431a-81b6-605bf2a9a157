package com.yupao.keepalive.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.yupao.keepalive.ScreenManager
import com.yupao.ylog.YLog

/**
 * 亮屏、熄屏广播
 *
 * <p>创建时间：2024/5/28/028</p>
 *
 * <AUTHOR>
 */
class ScreenStateReceiver: BroadcastReceiver() {
    companion object {
        private const val TAG = "ScreenStateReceiver"
    }

    override fun onReceive(p0: Context?, intent: Intent?) {
        if (intent?.action == Intent.ACTION_SCREEN_ON) {
            YLog.i(TAG, "screen on")
            // 屏幕已打开
            ScreenManager.screenOn()
        } else if (intent?.action == Intent.ACTION_SCREEN_OFF) {
            YLog.i(TAG, "screen off")
            // 屏幕已关闭
            ScreenManager.screenOff()
        }
    }

    fun register(context: Context) {
        kotlin.runCatching {
            val intentFilter = IntentFilter()
            intentFilter.addAction(Intent.ACTION_SCREEN_ON)
            intentFilter.addAction(Intent.ACTION_SCREEN_OFF)
            context.registerReceiver(this, intentFilter)
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun unregister(context: Context) {
        kotlin.runCatching {
            context.unregisterReceiver(this)
        }.onFailure {
            it.printStackTrace()
        }
    }
}