<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#F0F0F0" />
            <corners android:radius="6dp" />
            <stroke
                android:width="1dp"
                android:color="@color/colorPrimary" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="6dp" />
            <stroke
                android:width="1dp"
                android:color="@color/colorPrimary" />
        </shape>
    </item>
    
</selector>
