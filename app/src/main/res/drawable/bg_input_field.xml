<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="4dp" />
            <stroke
                android:width="1dp"
                android:color="@color/colorPrimary" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="4dp" />
            <stroke
                android:width="1dp"
                android:color="#E0E0E0" />
        </shape>
    </item>
    
</selector>
