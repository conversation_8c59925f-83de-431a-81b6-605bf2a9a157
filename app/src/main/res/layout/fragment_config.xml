<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="vm"
            type="com.yupao.wxcollect.ui.config.viewmodel.ConfigViewModel" />

        <variable
            name="click"
            type="com.yupao.wxcollect.ui.config.view.MainConfigFragment.ClickProxy" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View style="@style/space" />

                <FrameLayout style="@style/config_item_container">

                    <TextView
                        style="@style/config_key"
                        android:text="微信版本是否高于7.0.20" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:checked="@={vm.isAtMostHi}" />

                </FrameLayout>

                <View style="@style/space" />

                <LinearLayout style="@style/config_item_container">

                    <TextView
                        style="@style/config_key"
                        android:text="手机编号" />

                    <TextView
                        style="@style/config_value_et"
                        android:gravity="end"
                        android:inputType="number"
                        android:maxLength="8"
                        android:text="@{vm.telNo}" />

                </LinearLayout>

                <View style="@style/space" />

                <LinearLayout
                    style="@style/config_item_container"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/config_key"
                        android:text="IMEI码" />

                    <EditText
                        style="@style/config_value_et"
                        android:gravity="end"
                        android:hint="请输入"
                        android:text="@={vm.imei}" />

                    <TextView
                        android:id="@+id/create_password"
                        style="@style/config_item_btn"
                        doClick="@{() -> click.createPassword()}"
                        android:text="生成密码"
                        app:cornersRadius="@{@dimen/margin4}"
                        app:solidColor="@{@color/colorPrimary}" />

                </LinearLayout>

                <View style="@style/space" />

                <LinearLayout
                    style="@style/config_item_container"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/config_key"
                        android:text="数据库密码" />

                    <EditText
                        style="@style/config_value_et"
                        android:gravity="end"
                        android:hint="请输入"
                        android:text="@={vm.password}" />

                    <TextView
                        style="@style/config_item_btn"
                        doClick="@{() -> click.switchType()}"
                        android:text="切换"
                        app:cornersRadius="@{@dimen/margin4}"
                        app:solidColor="@{@color/colorPrimary}" />

                </LinearLayout>

                <View style="@style/space" />

                <LinearLayout style="@style/config_item_container">

                    <TextView
                        style="@style/config_key"
                        android:text="微信数据库路径" />

                    <EditText
                        style="@style/config_value_et"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/margin8"
                        android:breakStrategy="balanced"
                        android:hint="请输入"
                        android:singleLine="false"
                        android:text="@={vm.databasePath}" />

                    <TextView
                        style="@style/config_item_btn"
                        doClick="@{() -> click.switchDatabase()}"
                        android:text="切换"
                        app:cornersRadius="@{@dimen/margin4}"
                        app:solidColor="@{@color/colorPrimary}" />

                </LinearLayout>

                <View style="@style/space" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="@color/line" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:elevation="@dimen/margin4">

                    <TextView
                        doClick="@{() -> click.submit()}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/margin32"
                        android:layout_marginVertical="@dimen/margin8"
                        android:gravity="center"
                        android:padding="@dimen/margin8"
                        android:text="提交"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text16"
                        app:cornersRadius="@{@dimen/margin4}"
                        app:solidColor="@{@color/colorPrimary}" />

                </FrameLayout>
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.yupao.scafold.baseui.LoadingView
            android:id="@+id/loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>
</layout>