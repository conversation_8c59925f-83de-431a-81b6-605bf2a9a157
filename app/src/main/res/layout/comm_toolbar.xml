<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    tools:showIn="@layout/activity_list">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/ic_back"
        android:adjustViewBounds="true"
        android:padding="@dimen/margin8" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/text16"
        android:textColor="@color/core"
        android:layout_gravity="center" />

    <View style="@style/line"
        android:layout_gravity="bottom"
        />
</FrameLayout>