<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:background="@drawable/shape_background_gray_r12"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:src="@drawable/ic_task_manager"
        android:scaleType="centerInside"
        android:adjustViewBounds="true"
        />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="15dp"
        android:textColor="@color/black85"
        android:text="上传配置"
        android:layout_marginTop="@dimen/margin10"
        />

</LinearLayout>