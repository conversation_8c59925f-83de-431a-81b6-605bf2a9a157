<?xml version="1.0" encoding="utf-8"?>
<layout>
    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white">

        <FrameLayout
            xmlns:tools="http://schemas.android.com/tools"
            android:id="@+id/item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin16"
            android:layout_marginHorizontal="@dimen/margin16"
            tools:showIn="@layout/dialog_choose_system">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text14"
                android:textColor="@color/core"
                android:layout_gravity="center_vertical"
                android:text="初始化主系统" />

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:clickable="false"
                />

        </FrameLayout>

        <View style="@style/line"
            android:layout_marginTop="@dimen/margin16"/>

    </LinearLayout>
</layout>