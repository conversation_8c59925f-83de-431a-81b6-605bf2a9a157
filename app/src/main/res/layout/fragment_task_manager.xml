<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/executeAllTask"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/margin14"
            android:textColor="@color/white"
            android:text="一键执行全部任务"
            android:background="@drawable/widget_shape_bg_color_primary_4dp"
            android:paddingVertical="@dimen/margin6"
            android:paddingHorizontal="@dimen/margin20"
            android:layout_marginTop="@dimen/margin8"
            android:layout_marginHorizontal="@dimen/margin16"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/line"
            />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/margin12"
                />

            <include
                android:id="@+id/emptyView"
                layout="@layout/layout_empty_view" />
        </FrameLayout>
    </LinearLayout>
</layout>