<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/failedTaskRootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/margin16"
    android:paddingVertical="@dimen/margin8">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="200dp"
        android:orientation="vertical"
        android:padding="@dimen/margin10"
        android:background="@drawable/shape_background_task_failed">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请注意！"
            android:drawableStart="@mipmap/icon_node_failed_task"
            android:textSize="17sp"
            android:textColor="#FFF92727"
            android:textStyle="bold"
            android:drawablePadding="@dimen/margin6"
            android:gravity="center_vertical"
            />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:id="@+id/itemContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                />

        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/reboot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重启导演APP"
            android:textSize="@dimen/text14"
            android:textColor="@color/colorPrimary"
            android:layout_marginTop="@dimen/margin10"
            />
    </LinearLayout>
</FrameLayout>