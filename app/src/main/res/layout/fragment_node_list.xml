<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:background="@color/white">

            <LinearLayout
                android:id="@+id/typeContainer"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/type"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:textColor="@color/black65"
                    android:textSize="@dimen/margin14"
                    android:gravity="center"
                    android:text="全部"
                    />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/message_rectangle_icon"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="@dimen/margin12"
                    />

            </LinearLayout>


            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/line"
                />

            <LinearLayout
                android:id="@+id/statusContainer"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/status"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:textColor="@color/black65"
                    android:textSize="@dimen/margin14"
                    android:gravity="center"
                    android:text="全部"
                    />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/message_rectangle_icon"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="@dimen/margin12"
                    />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/line"
            />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.yupao.wxcollect.ui.upload_node.UploadFailedTaskView
                    android:id="@+id/failedTaskView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/list"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    />
            </LinearLayout>

            <include
                android:id="@+id/emptyView"
                layout="@layout/layout_empty_view" />

            <com.yupao.scafold.baseui.LoadingView
                android:id="@+id/loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>
    </LinearLayout>
</layout>