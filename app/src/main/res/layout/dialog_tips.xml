<?xml version="1.0" encoding="utf-8"?>
<layout>
    <FrameLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:layout_margin="@dimen/margin32"
            android:background="@drawable/shape_background_white_r8">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text18"
                android:textColor="@color/black85"
                android:textStyle="bold"
                android:text="温馨提示"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="@dimen/margin12"
                android:layout_marginTop="@dimen/margin20"
                />

            <TextView
                android:id="@+id/content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text16"
                android:textColor="@color/black65"
                android:text="温馨提示"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="@dimen/margin12"
                android:layout_marginTop="@dimen/margin20"
                />

            <View style="@style/line"
                android:layout_marginTop="@dimen/margin20"/>

            <TextView
                android:id="@+id/sure"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:textSize="@dimen/text16"
                android:textColor="@color/core"
                android:text="确定"
                android:gravity="center"
                android:layout_gravity="center"
                android:paddingVertical="@dimen/margin12"
                />
        </LinearLayout>
    </FrameLayout>
</layout>