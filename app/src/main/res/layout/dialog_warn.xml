<?xml version="1.0" encoding="utf-8"?>
<layout>
    <FrameLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:layout_margin="@dimen/margin32"
            android:background="@drawable/shape_background_white_r8">

            <TextView
                android:id="@+id/content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="17sp"
                android:textColor="@color/black85"
                android:textStyle="bold"
                android:text="确定执行时不关闭微信网络吗？"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="@dimen/margin16"
                android:layout_marginTop="@dimen/margin24"
                />

            <TextView
                android:id="@+id/warn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text14"
                android:textColor="#FFFFB014"
                android:drawableStart="@mipmap/icon_warn"
                android:drawablePadding="@dimen/margin6"
                android:gravity="center_vertical"
                android:text=""
                android:background="@drawable/shape_background_warn_dialog"
                android:padding="@dimen/margin10"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="@dimen/margin16"
                android:layout_marginTop="@dimen/margin12"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_margin="@dimen/margin16"
                >

                <TextView
                    android:id="@+id/cancel"
                    android:layout_height="wrap_content"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:textSize="15sp"
                    android:textColor="@color/black65"
                    android:textStyle="bold"
                    android:text="取消"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:paddingVertical="@dimen/margin10"
                    android:background="@drawable/shape_btn_cancel_half_circle"
                    />

                <TextView
                    android:id="@+id/sure"
                    android:layout_height="wrap_content"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:textSize="15sp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:text="确定"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:paddingVertical="@dimen/margin10"
                    android:background="@drawable/shape_btn_sure_half_circle"
                    android:layout_marginStart="11dp"
                    />

            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</layout>