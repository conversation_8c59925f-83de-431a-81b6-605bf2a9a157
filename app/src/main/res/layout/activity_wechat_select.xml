<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000">

    <!-- 选择模式布局 -->
    <LinearLayout
        android:id="@+id/selectLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/margin32"
        android:background="@drawable/bg_dialog_white"
        android:orientation="vertical"
        android:padding="@dimen/margin24">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/margin24"
            android:text="请选择"
            android:textColor="@color/black"
            android:textSize="@dimen/text18"
            android:textStyle="bold" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="300dp" />

    </LinearLayout>

    <!-- 确认模式布局 -->
    <LinearLayout
        android:id="@+id/confirmLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/margin32"
        android:background="@drawable/bg_dialog_white"
        android:orientation="vertical"
        android:padding="@dimen/margin24"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/margin24"
            android:text="请确认"
            android:textColor="@color/black"
            android:textSize="@dimen/text18"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/margin16"
            android:text="请确认微信昵称和微信号是否正确？"
            android:textColor="@color/core"
            android:textSize="@dimen/text14" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/margin8"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="微信昵称："
                android:textColor="@color/core"
                android:textSize="@dimen/text14" />

            <TextView
                android:id="@+id/tvNickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="XXXXX"
                android:textColor="@color/black"
                android:textSize="@dimen/text14"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/margin24"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="微信号："
                android:textColor="@color/core"
                android:textSize="@dimen/text14" />

            <TextView
                android:id="@+id/tvWechatId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="XXXXX"
                android:textColor="@color/black"
                android:textSize="@dimen/text14"
                android:textStyle="bold" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btnModify"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginEnd="@dimen/margin8"
                android:layout_weight="1"
                android:background="@drawable/bg_button_outline"
                android:gravity="center"
                android:text="修改"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/text16" />

            <TextView
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginStart="@dimen/margin8"
                android:layout_weight="1"
                android:background="@drawable/bg_button_primary"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="@dimen/text16" />

        </LinearLayout>

    </LinearLayout>

    <!-- 手动输入模式布局 -->
    <LinearLayout
        android:id="@+id/inputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/margin32"
        android:background="@drawable/bg_dialog_white"
        android:orientation="vertical"
        android:padding="@dimen/margin24"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/margin24"
            android:text="手动输入"
            android:textColor="@color/black"
            android:textSize="@dimen/text18"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/margin24"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin8"
                android:text="微信号："
                android:textColor="@color/black"
                android:textSize="@dimen/text16" />

            <EditText
                android:id="@+id/etWechatId"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:background="@drawable/bg_input_field"
                android:hint="请输入微信号"
                android:paddingHorizontal="@dimen/margin12"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/hint"
                android:textSize="@dimen/text14" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginEnd="@dimen/margin8"
                android:layout_weight="1"
                android:background="@drawable/bg_button_outline"
                android:gravity="center"
                android:text="取消"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/text16" />

            <TextView
                android:id="@+id/btnInputConfirm"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginStart="@dimen/margin8"
                android:layout_weight="1"
                android:background="@drawable/bg_button_primary"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="@dimen/text16" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>