<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_network_float_window"
    android:padding="12dp">

    <TextView
        android:id="@+id/tv_network_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="⚠️ 网络已断开"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        android:gravity="center"
        android:minWidth="120dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
