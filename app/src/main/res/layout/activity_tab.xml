<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="vm"
            type="com.yupao.wxcollect.ui.task_manager.TabViewModel" />

        <variable
            name="click"
            type="com.yupao.wxcollect.ui.task_manager.TabActivity.ClickProxy" />

        <variable
            name="companion"
            type="com.yupao.wxcollect.service.procedure.entity.ReportConfig.Companion" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <include
                layout="@layout/comm_toolbar" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/all"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/text14"
                    android:textColor="@{vm.appType != companion.APP_MAIN &amp;&amp; vm.appType != companion.APP_SUB ? @color/colorPrimary : @color/core}"
                    android:text="全部"
                    android:paddingVertical="@dimen/margin16"
                    android:layout_marginStart="@dimen/margin32"
                    android:drawablePadding="@dimen/margin4"
                    android:gravity="center_horizontal"
                    app:doClick="@{() -> click.onTabClick(``)}"
                    android:drawableBottom="@{vm.appType != companion.APP_MAIN &amp;&amp; vm.appType != companion.APP_SUB ? @drawable/shape_bottom_line : null}" />


                <TextView
                    android:id="@+id/main"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/text14"
                    android:textColor="@{vm.appType == companion.APP_MAIN ? @color/colorPrimary : @color/core}"
                    android:text="主号"
                    android:paddingVertical="@dimen/margin16"
                    android:drawablePadding="@dimen/margin4"
                    android:gravity="center_horizontal"
                    app:doClick="@{() -> click.onTabClick(companion.APP_MAIN)}"
                    android:drawableBottom="@{vm.appType == companion.APP_MAIN ? @drawable/shape_bottom_line : null}" />

                <TextView
                    android:id="@+id/sub"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/text14"
                    android:textColor="@{vm.appType == companion.APP_SUB ? @color/colorPrimary : @color/core}"
                    android:text="副号"
                    android:paddingVertical="@dimen/margin16"
                    android:layout_marginEnd="@dimen/margin32"
                    android:drawablePadding="@dimen/margin4"
                    android:gravity="center_horizontal"
                    app:doClick="@{() -> click.onTabClick(companion.APP_SUB)}"
                    android:drawableBottom="@{vm.appType == companion.APP_SUB ? @drawable/shape_bottom_line : null}" />

            </LinearLayout>

            <View
                android:id="@+id/line"
                style="@style/line"
                />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

        </LinearLayout>

    </FrameLayout>
</layout>