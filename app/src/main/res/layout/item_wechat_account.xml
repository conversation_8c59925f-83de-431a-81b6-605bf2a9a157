<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:orientation="vertical"
    android:padding="@dimen/margin16">

    <TextView
        android:id="@+id/tvNickname"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin8"
        android:text="微信昵称：XXXXX"
        android:textColor="@color/black"
        android:textSize="@dimen/text16" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvWechatId"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="微信号：XXXXX"
            android:textColor="@color/core"
            android:textSize="@dimen/text14" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=">"
            android:textColor="@color/core"
            android:textSize="@dimen/text16" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/margin16"
        android:background="@color/line" />

</LinearLayout>