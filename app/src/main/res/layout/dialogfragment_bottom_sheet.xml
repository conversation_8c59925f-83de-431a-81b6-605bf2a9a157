<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.yupao.widget.recyclerview.xrecyclerview.LayoutType" />

        <variable
            name="adapter"
            type="com.yupao.wxcollect.ui.config.view.BottomSheetAdapter" />

        <!-- 点击触发的回调 -->
        <variable
            name="clickProxy"
            type="com.yupao.wxcollect.ui.config.view.BottomSheetDialogFragment.ClickProxy" />

    </data>

    <LinearLayout
        doClick="@{() -> clickProxy.clickEmptyArea()}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <com.yupao.widget.recyclerview.xrecyclerview.XRecyclerView
            android:id="@+id/xRv"
            adapter="@{adapter}"
            layoutType="@{LayoutType.LINEAR_LAYOUT_V}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="300dp"
            tools:background="@color/black"
            tools:layout_height="300dp" />

        <View style="@style/space" />

        <TextView
            android:id="@+id/cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/margin12"
            android:textSize="@dimen/text16"
            android:textColor="@color/core"
            android:gravity="center"
            android:text="取消"
            doClick="@{() -> clickProxy.clickEmptyArea()}"
            />

    </LinearLayout>
</layout>