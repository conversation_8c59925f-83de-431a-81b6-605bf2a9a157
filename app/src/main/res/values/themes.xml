<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.WxCollect" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:windowBackground">@color/color_background</item>
        <item name="actionMenuTextColor">@color/white</item>
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>

    <!-- 透明弹窗主题 - 用于 WechatSelectActivity -->
    <style name="Theme.Transparent" parent="AppTheme">
        <!-- 移除标题栏和状态栏 -->
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>

        <!-- 设置透明背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>

        <!-- 移除窗口的内容边框 -->
        <item name="android:windowContentOverlay">@null</item>

        <!-- 设置窗口动画 -->
        <item name="android:windowAnimationStyle">@style/DialogAnimation</item>

        <!-- 设置状态栏透明 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>

        <!-- 设置导航栏透明 -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentNavigation">true</item>

        <!-- 全屏显示，不裁剪布局 -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <!-- 弹窗动画样式 -->
    <style name="DialogAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>
</resources>