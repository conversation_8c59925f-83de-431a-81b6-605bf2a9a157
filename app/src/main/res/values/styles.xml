<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/line</item>
    </style>

    <style name="config_item_container">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/white</item>
        <item name="android:paddingStart">@dimen/margin16</item>
        <item name="android:paddingEnd">@dimen/margin16</item>
        <item name="android:paddingTop">@dimen/margin12</item>
        <item name="android:paddingBottom">@dimen/margin12</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="config_key">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/core</item>
        <item name="android:textSize">@dimen/text14</item>
    </style>

    <style name="config_value_et">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">end|center_vertical</item>
        <item name="android:textColor">@color/core</item>
        <item name="android:textColorHint">@color/hint</item>
        <item name="android:textSize">@dimen/text14</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@null</item>
        <item name="android:singleLine">true</item>
        <item name="android:paddingTop">@dimen/margin4</item>
        <item name="android:paddingBottom">@dimen/margin4</item>
    </style>

    <style name="space">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/space_height</item>
        <item name="android:background">@color/line</item>
    </style>

    <style name="config_item_btn">
        <item name="android:layout_width">@dimen/margin80</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text12</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginStart">@dimen/margin6</item>
        <item name="android:paddingTop">@dimen/margin4</item>
        <item name="android:paddingBottom">@dimen/margin4</item>
    </style>

    <!-- 弹框动画 - 底部开始，往上弹出 -->
    <style name="AnimDownToTop" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/bottom_pop_in_anim</item>
        <item name="android:windowExitAnimation">@anim/bottom_pop_out_anim</item>
    </style>

    <style name="item_main">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:drawablePadding">@dimen/margin4</item>
        <item name="android:textSize">@dimen/text16</item>
        <item name="android:textColor">@color/core</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_margin">@dimen/margin16</item>
    </style>

</resources>