package com.yupao.wxcollect.constant

import com.yupao.utils.system.asm.AndroidSystemUtil
import java.io.File

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/19/019</p>
 *
 * <AUTHOR>
 */
sealed interface PathEnv {
    // 内部存储
    object Inner : PathEnv
    // 外部存储
    object Outer : PathEnv
    // 自动选择
    object Auto : PathEnv
}

sealed interface PathType {
    // 文件目录
    object File : PathType
    // 缓存目录
    object Cache : PathType
}

object AppPath {
    // 文件分隔符
    private val Separator = File.separator

    // 通用父目录
    private const val baseDirName = "log"

    object RootPath {
        private fun getContext() = AndroidSystemUtil.getContext()

        fun getInnerRootFilePath(): String {
            return getContext().filesDir.absolutePath
        }

        fun getOuterRootFilePath(): String? {
            return getContext().getExternalFilesDir(null)?.absolutePath
        }

        fun getInnerRootCachePath(): String {
            return getContext().cacheDir.absolutePath
        }

        fun getOuterRootCachePath(): String? {
            return getContext().externalCacheDir?.absolutePath
        }
    }

    fun getRootPath(env: PathEnv, type: PathType): String? {
        return when (type) {
            PathType.File -> {
                when (env) {
                    PathEnv.Inner -> RootPath.getInnerRootFilePath()
                    PathEnv.Outer -> RootPath.getOuterRootFilePath()
                    PathEnv.Auto -> RootPath.getOuterRootFilePath() ?: RootPath.getInnerRootFilePath()
                }
            }
            PathType.Cache -> {
                when (env) {
                    PathEnv.Inner -> RootPath.getInnerRootCachePath()
                    PathEnv.Outer -> RootPath.getOuterRootCachePath()
                    PathEnv.Auto -> RootPath.getOuterRootCachePath() ?: RootPath.getInnerRootCachePath()
                }
            }
        }
    }

    fun contact(baseDir: String, vararg paths: String): String {
        if (baseDir.isEmpty()) {
            return ""
        }
        return baseDir.plus(Separator).plus(paths.filter {
            it.isNotBlank()
        }.joinToString(Separator))
    }

    val baseFileDir by lazy {
        getRootPath(PathEnv.Auto, PathType.File)?.let { rootPath ->
            contact(rootPath, baseDirName)
        } ?: ""
    }

    val baseCacheDir by lazy {
        getRootPath(PathEnv.Auto, PathType.Cache)?.let { rootPath ->
            contact(rootPath, baseDirName)
        } ?: ""
    }
}