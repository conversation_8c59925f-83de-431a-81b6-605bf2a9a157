package com.yupao.wxcollect.constant

import com.google.gson.Gson
import com.yupao.wxcollect.App
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/5/18/018 16:19
 * @Description
 */
object AppPathCache {
    @Volatile
    private var isUseInnerDir: Boolean? = null

    // 根目录，部分手机数据库放在内置存储中不能读取数据库（sqlcipher error code 14: Could not open database）
    private fun baseDir(isInnerDir: Boolean): String? {
        val absolutePath = App.getContext()?.filesDir?.absolutePath
        if (isInnerDir) {
            return absolutePath
        }
        return (App.getContext()?.getExternalFilesDir(null)?.absolutePath) ?: absolutePath
    }

    // 存储wechat相关数据文件目录
    fun appWechatDir(isInnerDir: Boolean? = isUseInnerDir): String {
        val isInner = if (isInnerDir == null) {
            runBlocking {
                initRootDirSync()
            }
            this.isUseInnerDir
        } else isInnerDir
        return "${baseDir(isInner ?: true)}${SEPARATOR}wechat"
    }

    fun isInnerDir() = isUseInnerDir

    /**
     * 强制为Android/data/……目录
     */
    fun setUseInnerDir(isInnerDir: Boolean) {
        isUseInnerDir = isInnerDir
        saveConfig()
    }

    private val configFilePath = "${baseDir(false)}${SEPARATOR}app-config.data"

    fun initRootDir() {
        GlobalScope.launch(Dispatchers.IO) {
            initRootDirSync()
        }
    }

    suspend fun initRootDirSync() {
        val file = File(configFilePath)
        val parentFile = file.parentFile ?: return
        if (!parentFile.exists()) {
            parentFile.mkdirs()
        } else if(file.exists()) {
            val text = file.readText()
            if (text.isBlank()) {
                return
            }
            val config: AppConfig? = kotlin.runCatching {
                Gson().fromJson(text, AppConfig::class.java)
            }.getOrNull()
            isUseInnerDir = config?.isInnerDir
            YLog.i("AppPath", "initRootDir: isUseInnerDir = $isUseInnerDir")
        }
    }

    private fun saveConfig() {
        GlobalScope.launch(Dispatchers.IO) {
            val file = File(configFilePath)
            val parentFile = file.parentFile ?: return@launch
            if (!parentFile.exists()) {
                parentFile.mkdirs()
            }
            val json = Gson().toJson(AppConfig(isUseInnerDir))
            file.writeText(json)
            YLog.i("AppPath", "saveConfig: isUseInnerDir = $isUseInnerDir")
        }
    }
}