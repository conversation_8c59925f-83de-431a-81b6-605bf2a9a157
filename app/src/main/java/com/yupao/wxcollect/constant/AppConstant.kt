package com.yupao.wxcollect.constant

import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.wxcollect.App
import com.yupao.wxcollect.util.SpUtil
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 11:12
 * @Description App常量
 */
val isClearENDebug = false && BuildConfig.DEBUG

// 是否控制微信网络
val isWechatNetworkCtrl = true

val SEPARATOR = File.separator

// 微信根目录
val WECHAT_BASE_DIR = "${SEPARATOR}data${SEPARATOR}user${SEPARATOR}%s${SEPARATOR}com.tencent.mm"

// wechat数据库base路径
val WECHAT_DATABASE_BASE_PATH = "$WECHAT_BASE_DIR${SEPARATOR}MicroMsg"

// wechat数据库名称
val WECHAT_DATABASE_NAME = "EnMicroMsg.db"

// wechat数据库目录：eg. /data/user/0/com.tencent.mm/MicroMsg/b63c013a3c9f086616dad27f89211fc4
val WECHAT_DATABASE_DIR = "$WECHAT_DATABASE_BASE_PATH${SEPARATOR}%s"

// auth_info_key_prefs名称
val WECHAT_AUTH_INFO_NAME = "auth_info_key_prefs.xml"

// wechat auth_info_key_prefs目录
val WECHAT_AUTH_INFO_PATH = "$WECHAT_BASE_DIR${SEPARATOR}shared_prefs${SEPARATOR}${WECHAT_AUTH_INFO_NAME}"

// 开发环境
val DEV_URL = BuildConfig.devUrl
val DEV_URL_B="https://yupao-develop-backend-intranet.yupaowang.com/"

// 测试站
val TEST_URL = BuildConfig.testUrl
val TEST_URL_B = "https://yupao-test-backend.yupaowang.com/"

// 预发布
val MASTER_URL = BuildConfig.masterUrl
val MASTER_URL_B = "https://yupao-master-backend.yupaowang.com/"

// 生产
val PROD_URL = BuildConfig.prodUrl
val PROD_URL_B = "https://yupao-prod-backend.yupaowang.com/"

private val BASE_URL = BuildConfig.currUrl

// 空数据是否上报，只有在测试站才会
val isUploadIfEmptyData = BASE_URL == TEST_URL

// 是否支持分发
val isSupportDispatch = true || !BuildConfig.DEBUG

// 上传EntryId
val UPLOAD_ENTRY_ID = 555693

// 上传AppId
val UPLOAD_APP_ID = "153"

// 是否过滤公众号文章
val isOfficialArticleFilter = true || !BuildConfig.DEBUG

// 命令执行超时时间
val CMD_TIMEOUT = 30_000L

object AppConstant {

    object URL {
        fun baseUrl() = kotlin.run {
            if (BuildConfig.DEBUG) {
//                SpUtil.getString(SpUtil.KEY_CURR_URL) ?: BASE_URL
                BASE_URL
            } else {
                BASE_URL
            }
        }
    }

    object Host {
        val wxCollectReportHost = "${App.getContext()?.packageName}:report"

        val wxCollectUI = App.getContext()?.packageName ?: ""
    }

    object IntervalTime {
        // 上传时间最低要求
        const val uploadTime = 1000L
    }

    // 性能相关
    object Performance {
        // 是否开启内存检测
        val isMemoryEnable = BuildConfig.DEBUG
    }
}