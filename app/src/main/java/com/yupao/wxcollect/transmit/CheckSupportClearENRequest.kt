package com.yupao.wxcollect.transmit

import com.yupao.wxcollect.transmit.entity.ReportResponseData
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/28/028 15:46
 * @Description 检查是否支持开启EN
 */
class CheckSupportClearENRequest(
    private val callback: ((Boolean) -> Unit)? = null
): ITransmitRequest<Boolean> {
    override fun type(): Int {
        return ITransmitRequest.CHECK_SUPPORT_CLEAR_EN
    }

    override fun requestData(): Boolean? {
        return true
    }

    override fun reply(resp: ReportResponseData?) {
        YLog.i("CheckSupportClearENRequest", "reply: resp = $resp")
        val result = kotlin.runCatching {
            resp?.response?.toBoolean()
        }.getOrNull() ?: false
        callback?.invoke(result)
    }
}