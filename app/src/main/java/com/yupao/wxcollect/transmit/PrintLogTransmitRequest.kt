package com.yupao.wxcollect.transmit

import android.os.Parcelable
import androidx.annotation.Keep
import com.yupao.wxcollect.transmit.entity.ITransmitResponseEntity
import com.yupao.wxcollect.transmit.entity.ReportResponseData
import com.yupao.wxcollect.transmit.resp.PrintLogTransmitResponse
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * @Date 2023/4/14/014 15:09
 * @Description 收集日志
 */
class PrintLogTransmitRequest(
    private val callback: ((Boolean) -> Unit)? = null
): ITransmitRequest<PrintLogEntity> {

    override fun type(): Int = ITransmitRequest.PRINT_LOG

    override fun requestData(): PrintLogEntity {
        return PrintLogEntity()
    }

    override fun reply(resp: ReportResponseData?) {
        callback?.invoke(resp?.isSucceed == true)
    }
}

@Keep
@Parcelize
data class PrintLogEntity(
    override val responseClass: String? = PrintLogTransmitResponse::class.java.name
) : ITransmitResponseEntity, Parcelable