package com.yupao.wxcollect.transmit.director.handle

import com.google.gson.Gson
import com.yupao.point.PointHelper
import com.yupao.point.api.PointApi
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.MessageEntity
import com.yupao.wxcollect.net.AppHttpClient
import com.yupao.wxcollect.service.procedure.entity.director.CollectEntity
import com.yupao.wxcollect.service.procedure.entity.director.ICompletedStatus
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatus
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatusTypeAdapter
import com.yupao.wxcollect.service.procedure.entity.request.ReportNetModel
import com.yupao.wxcollect.service.record.CollectTaskDbHelper
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.util.LogUtil
import com.yupao.ylog.YLog

/**
 * 采集任务状态改变代理
 *
 * <p>创建时间：2024/6/11/011</p>
 *
 * <AUTHOR>
 */
class CollectStatusChangedProxy: ICollectStatusChanged {
    companion object {
        private const val TAG = "DirectorStatusChanged"
    }

    private val gson by lazy { Gson() }

    init {
        PointHelper.initPoint {
            AppHttpClient.instanceForC.createApiService(PointApi::class.java)
        }
    }

    override suspend fun onChanged(entity: CollectEntity) {
        onChanged(listOf(entity))
    }

    private fun isAllowReportStatus(entity: CollectEntity): Boolean {
        val status = entity.status
        return status !is TaskStatus.CopyDb && status !is TaskStatus.Upload && status !is TaskStatus.Compress
    }

    private fun doPoint(list: List<CollectEntity>?) {
        if (list.isNullOrEmpty()) {
            return
        }
        list.forEach { entity ->
            val execStep = when (entity.status) {
                is TaskStatus.CopyDb -> {
                    ReportNetModel.CopyDb
                }

                is TaskStatus.Upload -> {
                    ReportNetModel.Upload
                }

                else -> {
                    return@forEach
                }
            }
            PointHelper.doPoint("EdiTaskStatusReport", mapOf(
                "taskId" to entity.taskId,
                "execStep" to execStep,
                "recordUuid" to entity.unique,
            ))
        }
    }

    override suspend fun onChanged(entityList: List<CollectEntity>) {
        val groups = entityList.groupBy(this::isAllowReportStatus)
        val taskList = groups[true]
        if (taskList.isNullOrEmpty()) {
            this.doPoint(groups[false])
            return
        }
        val list = taskList.map { entity ->
            TaskStatusTypeAdapter.serialize(entity.status)
        }
        YLog.i(TAG, "status changed: $list")
        MessageManager.executor.submit(
            MessageEntity(
                code = MessageCode.TASK_STATUS,
                toHost = CoreClient.serviceHost,
            )
                .put(MessageKey.TaskStatusList, gson.toJson(list))
        )
        CollectTaskDbHelper.updateAllStatus(taskList)
        val completedTask = taskList.find { entity ->
            entity.status is ICompletedStatus
        }
        if (completedTask != null) {
            LogUtil.saveCacheLogForce()
        }
    }
}

interface ICollectStatusChanged {
    suspend fun onChanged(entity: CollectEntity)

    suspend fun onChanged(entityList: List<CollectEntity>)
}