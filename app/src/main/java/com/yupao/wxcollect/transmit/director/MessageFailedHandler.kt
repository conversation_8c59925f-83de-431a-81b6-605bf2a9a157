package com.yupao.wxcollect.transmit.director

import com.yupao.transmit.execute.IExecuteFailedHandler
import com.yupao.transmit.execute.MessageEntity
import com.yupao.wxcollect.util.CmdResult
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog
import java.util.Collections

/**
 * 消息执行失败处理器
 *
 * <p>创建时间：2024/6/13/013</p>
 *
 * <AUTHOR>
 */
class MessageFailedHandler: IExecuteFailedHandler {
    companion object {
        private const val TAG = "MessageFailedHandler"
    }

    // 正在重启等待注册所有包
    private val relaunchSet = Collections.synchronizedSet(mutableSetOf<String>())

    override suspend fun onFailed(messageEntity: MessageEntity) {
        val toHost = messageEntity.toHost
        // 采集App
        if (toHost == CoreClient.serviceHost) {
            if (relaunchSet.contains(toHost)) {
                YLog.i(TAG, "onFailed retry restart: $toHost")
                return
            }
            relaunchSet.add(toHost)
            val serviceResult = restartService()
            if (serviceResult.isOK()) {
                return
            }
            val activityResult = restartActivity()
            if (activityResult.isOK()) {
                return
            }
            YLog.w(TAG, "restart failed: $toHost")
            relaunchSet.remove(toHost)
        } else {
            YLog.w(TAG, "not support host: $toHost")
        }
    }

    private suspend fun restartService(): CmdResult {
        val packageName = CoreClient.servicePackageName
        val serviceName = CoreClient.serviceName
        return CmdUtil.execute("am startservice -n ${packageName}/$serviceName")
    }

    private suspend fun restartActivity(): CmdResult {
        val packageName = CoreClient.servicePackageName
        val activityName = CoreClient.activityName
        return CmdUtil.execute("am start -n ${packageName}/$activityName")
    }

    override fun onHostRegister(host: String) {
        relaunchSet.remove(host)
    }
}