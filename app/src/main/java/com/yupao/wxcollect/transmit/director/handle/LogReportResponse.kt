package com.yupao.wxcollect.transmit.director.handle

import com.google.gson.Gson
import com.yupao.data.net.media.MediaEntity
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.execute.IMessageResponse
import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.UPLOAD_APP_ID
import com.yupao.wxcollect.constant.UPLOAD_ENTRY_ID
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.procedure.entity.request.FileType
import com.yupao.wxcollect.service.procedure.entity.request.FileUploadParam
import com.yupao.wxcollect.service.procedure.entity.request.UploadFileEntity
import com.yupao.wxcollect.service.procedure.entity.request.YuPaoCloudRequestEntity
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.service.procedure.model.ReportRepositoryEntryPoint
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.transmit.entity.LogReportEntity
import com.yupao.wxcollect.util.LogUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.yapm.utils.ZipUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.launch
import java.io.File

/**
 * 日志采集上报
 *
 * <p>创建时间：2024/8/12/012</p>
 *
 * <AUTHOR>
 */
class LogReportResponse: IMessageResponse {

    private val resp: ReportRepository by lazy {
        EntryPointAccessors.fromApplication(
            App.getContext()!!,
            ReportRepositoryEntryPoint::class.java
        ).getReportRepository()
    }

    override fun onRequestReceived(
        fromHost: String?,
        args: ArgsEntity?,
        responseEmit: (ArgsEntity) -> Unit
    ) {
        YLog.i("LogReportResponse", "report log start")
        CoroutinePool.other.launch {
            LogUtil.saveLogSync(null, false)
            val logDir = LogUtil.getBaseFile("log") ?: return@launch
            val fileName = "${TimeUtil.fileNameFormat()}.zip"
            val zipFilePath = ZipUtil.zip(logDir, fileName)
            if (zipFilePath.isNullOrEmpty()) {
                return@launch
            }
            val param = FileUploadParam(
                entryId = UPLOAD_ENTRY_ID,
                type = FileType.FILE,
                path = zipFilePath,
                fileId = zipFilePath,
                uid = null,
            )
            val logReportEntity = kotlin.runCatching {
                val uploadResult = resp.upload(UPLOAD_APP_ID, param)
                var entity: MediaEntity<UploadFileEntity>? = null
                if (uploadResult.isOK()) {
                    entity = resp.uploadFile(YuPaoCloudRequestEntity(param, uploadResult.getData()), emptyMap())
                }
                YLog.i("LogReportResponse", "report log result: ${uploadResult.isOK()}, msg: ${uploadResult.getMsg()}")
                LogReportEntity(
                    entity?.isOK() ?: false,
                    entity?.getMsg() ?: uploadResult.getMsg(),
                    uploadResult.getData()?.url,
                    uploadResult.getData()?.resourceId,
                )
            }.getOrElse {
                YLog.printException("LogReportResponse",  it)
                LogReportEntity(false, it.message ?: "", null, null)
            }
            responseEmit.invoke(
                ArgsEntity().put(MessageKey.Data, Gson().toJson(logReportEntity))
            )
            File(zipFilePath).delete()
        }
    }
}