package com.yupao.wxcollect.transmit.director.handle

import androidx.annotation.Keep
import com.google.gson.Gson
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.handle.IMessageHandler
import com.yupao.wxcollect.constant.DeviceConfig
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.ylog.YLog

/**
 * 配置更新
 *
 * <p>创建时间：2024/11/11/011</p>
 *
 * <AUTHOR>
 */
class UpdateEdiConfigHandler: IMessageHandler {
    private val gson by lazy { Gson() }

    override fun handle(fromHost: String?, args: ArgsEntity?) {
        val data = args?.optString(MessageKey.Data)
        if (data.isNullOrBlank()) return
        runCatching {
            val model = gson.fromJson(data, PhoneDeviceConfigEntity::class.java)
            //数据库指令记录最大缓存数量
            if(!model.wxHeaderUserAgent.isNullOrBlank()){
                DeviceConfig.wxHeaderUserAgent = model.wxHeaderUserAgent
            }
            //日志缓存时间
            if(model.logStorageTime!=null && model.logStorageTime > 0){
                DeviceConfig.logStorageTime = (model.logStorageTime * 3600_000L)
            }

            if(model.logMaxNum!=null && model.logMaxNum > 0){
                DeviceConfig.logMaxNum = model.logMaxNum
            }
            //公众号请求超时时间（分钟）
            if(model.officialAccountTimeout!=null && model.officialAccountTimeout > 0){
                DeviceConfig.officialAccountTimeout = model.officialAccountTimeout * 60_000L
            }
            // 任务上报随机延迟最大秒数
            if(model.reportDelayMaxSeconds != null){
                DeviceConfig.reportDelayMaxSeconds = model.reportDelayMaxSeconds
            }
            YLog.i("UpdateEdiConfigHandler", data)
        }.onFailure {
            YLog.printException("TaskReportResultHandler", it)
        }
    }
}

@Keep
data class PhoneDeviceConfigEntity(
    /** 微信请求头 */
    val wxHeaderUserAgent: String? = null,
    /** 日志缓存时间（小时） */
    val logStorageTime: Int? = null,
    /** 公众号请求超时时间（分钟） */
    val officialAccountTimeout: Int? = null,
    /** 数据库指令记录最大缓存数量 */
    val directiveMaxNum: Int? = null,
    /** 执行app UI日志记录最大数量 */
    val logMaxNum: Int? = null,
    /** 任务上报随机延迟最大秒数 */
    val reportDelayMaxSeconds: Int? = null,
)