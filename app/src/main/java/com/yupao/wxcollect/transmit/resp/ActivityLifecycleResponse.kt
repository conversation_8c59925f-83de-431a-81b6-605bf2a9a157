package com.yupao.wxcollect.transmit.resp

import android.os.Message
import com.google.gson.Gson
import com.yupao.wxcollect.transmit.ITransmitResponse
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.transmit.entity.LifecycleEntity
import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/21/021</p>
 *
 * <AUTHOR>
 */
class ActivityLifecycleResponse: ITransmitResponse {

    private val gson by lazy { Gson() }

    override fun onResponse(message: Message?, request: String?): ReportResponseData? {
        kotlin.runCatching {
            val entity = gson.fromJson(request, LifecycleEntity::class.java)
            if (entity.type == LifecycleEntity.OnResume) {
                // 当页面resume时，检查client是否注册
                CoreClient.checkClientRegister()
            }
        }
        return null
    }
}