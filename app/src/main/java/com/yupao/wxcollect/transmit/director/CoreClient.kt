package com.yupao.wxcollect.transmit.director

import android.app.Service
import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Messenger
import androidx.core.app.ActivityCompat
import com.yupao.transmit.MessageConstant
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.MessageEntity
import com.yupao.transmit.handle.HostEntity
import com.yupao.transmit.handle.IHostRegister
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.procedure.keepalive.HeartBeatManager
import com.yupao.wxcollect.transmit.director.handle.CloseTaskHandler
import com.yupao.wxcollect.transmit.director.handle.UpdateBaseInfoHandler
import com.yupao.wxcollect.transmit.director.handle.CustomizeTaskHandler
import com.yupao.wxcollect.transmit.director.handle.FetchBaseInfoHandler
import com.yupao.wxcollect.transmit.director.handle.LogReportResponse
import com.yupao.wxcollect.transmit.director.handle.WechatEnStorageResponse
import com.yupao.wxcollect.transmit.director.handle.RegisterHandler
import com.yupao.wxcollect.transmit.director.handle.SaveLogHandler
import com.yupao.wxcollect.transmit.director.handle.TaskReportResultHandler
import com.yupao.wxcollect.transmit.director.handle.TestTaskHandler
import com.yupao.wxcollect.transmit.director.handle.UpdateDebugHandler
import com.yupao.wxcollect.transmit.director.handle.UpdateEdiConfigHandler
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/13/013</p>
 *
 * <AUTHOR>
 */
object CoreClient {

    private const val TAG = "CoreClient"

    const val servicePackageName = "com.yupao.director"

    const val serviceHost = "com.yupao.director:core"

    const val serviceName = "com.yupao.director.core.CoreService"

    const val activityName = "com.yupao.director.ui.MainActivity"

    // 服务是否在运行
    private var isRunning = false

    private val replyMessenger = Messenger(Handler(Looper.getMainLooper()) { msg ->
        MessageManager.handler.handleMessage(msg)
        false
    })

    private val registerHandler by lazy { RegisterHandler(serviceHost) }

    private val checkClientRegisterTask by lazy {
        {
            checkClientRegister()
        }
    }

    init {
        // 每隔三分钟检测一次服务是否存活
        HeartBeatManager.addObserver(3, checkClientRegisterTask)
        MessageManager.executor.setFailedHandler(MessageFailedHandler())
    }

    fun unregister() {
        MessageManager.executor.submit(MessageEntity(
            code = MessageConstant.Code.Unregister,
            toHost = serviceHost,
        )) {
            true
        }
    }

    private fun registerMessageHandler() {
        MessageManager.handler.registerHandler(MessageConstant.Code.RegisterSucceed, registerHandler)
        MessageManager.handler.registerHandler(MessageCode.CollectTask, CustomizeTaskHandler())
        MessageManager.handler.registerHandler(MessageConstant.Code.Default, TestTaskHandler())
        MessageManager.handler.registerHandler(MessageCode.UPDATE_BASE_INFO, UpdateBaseInfoHandler())
        MessageManager.handler.registerHandler(MessageCode.FETCH_BASE_INFO, FetchBaseInfoHandler())
        MessageManager.handler.registerHandler(MessageCode.CANCEL_TASK, CloseTaskHandler())
        MessageManager.handler.registerHandler(MessageCode.SAVE_LOG, SaveLogHandler())
        MessageManager.handler.registerHandler(MessageCode.UPDATE_DEBUG_CONFIG, UpdateDebugHandler())
        MessageManager.handler.registerResponse(MessageCode.LOG_REPORT, LogReportResponse())
        MessageManager.handler.registerHandler(MessageCode.TASK_REPORT_RESULT, TaskReportResultHandler())
        MessageManager.handler.registerHandler(MessageCode.UPDATE_EDI_CONFIG, UpdateEdiConfigHandler())
        MessageManager.handler.registerResponse(MessageCode.QUERY_WX_STORAGE, WechatEnStorageResponse())

    }

    private var startServiceTime = 0L

    fun startService() {
        val context = App.getContext() ?: return
        val currTime = TimeUtil.getCurrTime()
        // 3s之内不重试
        if (currTime - startServiceTime < 3000) {
            YLog.i("startService ignore, startServiceTime: $startServiceTime")
            return
        }
        startServiceTime = currTime
        val intent = Intent()
        intent.setClassName(servicePackageName, serviceName)
        ActivityCompat.startForegroundService(context, intent)
        var result = context.bindService(intent, conn, Service.BIND_AUTO_CREATE)
        YLog.i(TAG, "startService result: $result")
        if (!result) {
            CmdUtil.executeAsync("am startservice -n $servicePackageName/${serviceName}") { cmdResult ->
                YLog.i(TAG, "startService by cmd retry: ${cmdResult.isOK()}")
                startServiceTime = 0L
            }
        } else {
            startServiceTime = 0L
        }
    }

    /**
     * 检测client是否注册
     */
    fun checkClientRegister() {
        val registerSucceed = registerHandler.isRegisterSucceed
        YLog.i(TAG, "checkClientRegister isRunning: $isRunning, registerSucceed: $registerSucceed")
        if (!isRunning) {
            startService()
        } else {
            if (!registerSucceed) {
                registerHandler.register()
            } else {
                YLog.d(TAG, "client register succeed ")
            }
        }
    }

    private val conn by lazy { object : ServiceConnection {
        override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
            YLog.i(TAG, "service connected")
            val messenger = Messenger(p1)
            MessageManager.hostRegister.register(
                IHostRegister.defaultHost,
                HostEntity(replyMessenger, AndroidSystemUtil.getPackageName())
            )
            MessageManager.hostRegister.register(
                serviceHost,
                HostEntity(messenger, AndroidSystemUtil.getPackageName())
            )
            registerMessageHandler()
            registerHandler.register()
            isRunning = true
        }

        override fun onServiceDisconnected(p0: ComponentName?) {
            YLog.i(TAG, "service disconnected")
            MessageManager.hostRegister.unregister(IHostRegister.defaultHost)
            MessageManager.hostRegister.unregister(serviceHost)
            registerHandler.destroy()
            isRunning = false
        }
    } }
}