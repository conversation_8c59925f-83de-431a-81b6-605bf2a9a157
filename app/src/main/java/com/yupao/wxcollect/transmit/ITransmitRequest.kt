package com.yupao.wxcollect.transmit

import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * <AUTHOR>
 * @Date 2023/4/14/014 15:10
 * @Description
 */
interface ITransmitRequest<T> {
    companion object {
        const val KEY_UNIQUE = "unique"
        const val KEY_TYPE = "type"
        const val KEY_REQUEST = "request"
        const val KEY_RESPONSE = "response"

        // 停止服务
        const val STOP_SERVICE = 1

        // 更新配置
        const val UPDATE_CONFIG = 2

        // 拷贝文件
        const val COPY_FILE = 3

        // 检查是否支持清除EN
        const val CHECK_SUPPORT_CLEAR_EN = 6

        // 检查文件是否存在
        const val CHECK_FILE_EXIST = 7

        // 获取文件一级子目录
        const val FETCH_CHILD_FILE_PATH = 8

        // 检查数据库
        const val CHECK_DATABASE = 9

        // 收集日志
        const val PRINT_LOG = 10

        const val ACTIVITY_LIFECYCLE = 11

        // 获取手机号
        const val FETCH_PHONE_NUM = 12
    }

    fun type(): Int

    fun callbackCount(): Int = 1

    fun requestData(): T?

    fun reply(resp: ReportResponseData?)
}