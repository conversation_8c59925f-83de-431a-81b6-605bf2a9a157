package com.yupao.wxcollect.transmit

import com.google.gson.Gson
import com.yupao.wxcollect.service.db.DatabaseOpenResultEntity
import com.yupao.wxcollect.transmit.entity.DatabaseEntity
import com.yupao.wxcollect.transmit.entity.ReportResponseData
import com.yupao.wxcollect.transmit.resp.DatabaseCheckTransmitResponse
import kotlin.jvm.java

/**
 * <AUTHOR>
 * @Date 2023/5/18/018 14:58
 * @Description
 */
class DatabaseCheckTransmitRequest(
    private val wechatDatabasePath: String,
    private val destDatabaseDir: String,
    private val password: String,
    private val isUseInnerDir: Boolean,
    // app类型
    private val appType: String,
    // 之前的微信数据库路径
    private val oldWechatDatabasePath: String?,
    // 是否拷贝数据库
    private val isCopyDb: Boolean,
    private val callback: ((DatabaseOpenResultEntity?) -> Unit)? = null
): ITransmitRequest<DatabaseEntity> {

    override fun type(): Int = ITransmitRequest.CHECK_DATABASE

    override fun requestData(): DatabaseEntity {
        return DatabaseEntity(
            wechatDatabasePath,
            destDatabaseDir,
            password,
            isUseInnerDir,
            appType,
            oldWechatDatabasePath,
            isCopyDb,
            DatabaseCheckTransmitResponse::class.java.name
        )
    }

    override fun reply(resp: ReportResponseData?) {
        val entity = resp?.response?.let {
            Gson().fromJson(it, DatabaseOpenResultEntity::class.java)
        } ?: DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, resp?.errorMessage)
        callback?.invoke(entity)
    }
}