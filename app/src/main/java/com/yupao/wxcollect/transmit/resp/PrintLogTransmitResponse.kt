package com.yupao.wxcollect.transmit.resp

import android.os.Message
import com.yupao.wxcollect.service.procedure.dispatch.TaskTrainDispatcher
import com.yupao.wxcollect.transmit.ITransmitResponse
import com.yupao.wxcollect.transmit.entity.ReportResponseData
import com.yupao.wxcollect.util.LogUtil

/**
 * <AUTHOR>
 * @Date 2023/5/19/019 20:46
 * @Description
 */
class PrintLogTransmitResponse: ITransmitResponse {
    override fun onResponse(message: Message?, request: String?): ReportResponseData? {
        kotlin.runCatching {
            TaskTrainDispatcher.instance.print()
            LogUtil.saveToFile()
        }
        return ReportResponseData(true)
    }
}