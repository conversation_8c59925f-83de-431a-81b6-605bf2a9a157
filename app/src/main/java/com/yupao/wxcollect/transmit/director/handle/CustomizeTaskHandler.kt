package com.yupao.wxcollect.transmit.director.handle

import com.google.gson.Gson
import com.rwz.hook.utils.ToastUtil
import com.yupao.scafold.ktx.mapFilterComplete
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.handle.IMessageHandler
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.notice.ReportNotificationManager
import com.yupao.wxcollect.service.procedure.dispatch.ConflictStrategy
import com.yupao.wxcollect.service.procedure.dispatch.TaskTrainDispatcher
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.director.CollectData
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatus
import com.yupao.wxcollect.service.procedure.model.ReportRepositoryEntryPoint
import com.yupao.wxcollect.service.procedure.task.TaskCode
import com.yupao.wxcollect.transmit.director.DebugConfig
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.ylog.YLog
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch

/**
 * 自定义任务处理者
 *
 * <p>创建时间：2024/5/15/015</p>
 *
 * <AUTHOR>
 */
class CustomizeTaskHandler: IMessageHandler {

    companion object {
        private const val TAG = "CustomizeTaskHandler"

        private val configListFlow = EntryPointAccessors.fromApplication(
            App.getContext()!!,
            ReportRepositoryEntryPoint::class.java
        ).getReportRepository().queryAllConfig()
            ?.mapFilterComplete()
    }

    private val gson by lazy { Gson() }

    private val directorStatusChangedProxy = CollectStatusChangedProxy()

    override fun handle(fromHost: String?, args: ArgsEntity?) {
        YLog.d(TAG, "handle args: $args")
        val data = args?.optString(MessageKey.Data)
        if (data.isNullOrEmpty()) {
            YLog.i(TAG, "data is empty.")
            return
        }
        CoroutinePool.coreTask.launch(Dispatchers.IO) {
            YLog.i(TAG, "received data: $data")
            val collectData = kotlin.runCatching {
                gson.fromJson(data, CollectData::class.java)
            }.onFailure {
                YLog.i(TAG, "data is invalid: $data")
            }.getOrNull() ?: return@launch
            val allTaskName = collectData.list.joinToString("、"){
                it.taskName
            }
            YLog.i(TAG, "received task size: ${collectData.list.size}, taskNames: $allTaskName")
            collectData.list.forEach { entity ->
                entity.status = TaskStatus.Ready(entity.unique)
            }
            directorStatusChangedProxy.onChanged(collectData.list)
            val configList = configListFlow?.firstOrNull()
            val taskList = collectData.list.map {
                CollectTaskEntity(it)
            }.groupBy {
                it.entity.condition.appType
            }.mapNotNull {
                val appType = it.key
                val list = it.value
                val config = configList?.find {  config ->
                    config.appType == appType
                } ?: kotlin.run {
                    YLog.w(TAG, "not found config appType: $appType")
                    notifyFailed(list, appType)
                    return@mapNotNull null
                }
                val tempDbConfig = DebugConfig.tempDatabaseConfig
                TaskExecutorConfig(
                    databasePath = tempDbConfig?.path ?: config.databasePath ?: return@mapNotNull null,
                    password = tempDbConfig?.password ?: config.password ?: return@mapNotNull null,
                    appType = appType,
                    configList = list,
                    isCopyForce = list.any { item -> item.entity.database.isCopyForce },
                    timeoutTime = list.maxOf { item -> item.entity.timeoutTime },
                    invalidTime = list.maxOf { item -> item.entity.invalidTime },
                    strategy = ConflictStrategy.Delay
                )
            }
            YLog.i(TAG, "dispatch: ${taskList.size}")
            TaskTrainDispatcher.instance.dispatch(taskList)
        }
    }

    private fun containGroupFriend(list: List<CollectTaskEntity>): Boolean {
        return list.find {
            it.entity.taskCode == TaskCode.WxGroupFriend
        } != null
    }

    private suspend fun notifyFailed(list: List<CollectTaskEntity>, appType: String) {
        val type = when (appType) {
            ReportConfig.APP_MAIN -> {
                "主系统"
            }
            ReportConfig.APP_SUB -> {
                "副系统"
            }
            else -> ""
        }
        val errorMsg = "执行App未配置$type"
        updateNotice(errorMsg)
        val entityList = list.map { entity ->
            entity.entity.apply {
                status = TaskStatus.Failed(entity.unique, TaskStatus.Failed.RetryForever, errorMsg)
            }
        }
        directorStatusChangedProxy.onChanged(entityList)
    }

    private var updateTime = 0L

    private fun updateNotice(errorMsg: String) {
        val currTime = System.currentTimeMillis()
        if (currTime - updateTime < 3000) {
            return
        }
        this.updateTime = currTime
        ToastUtil.showShort(errorMsg)
        ReportNotificationManager.instance.updateNotice(App.getContext(), errorMsg)
    }
}