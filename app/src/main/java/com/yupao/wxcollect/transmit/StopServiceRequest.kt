package com.yupao.wxcollect.transmit

import android.os.Parcelable
import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * <AUTHOR>
 * @Date 2023/4/14/014 15:09
 * @Description 停止服务
 */
class StopServiceRequest(
    private val callback: ((Boolean) -> Unit)? = null
): ITransmitRequest<Parcelable?> {

    override fun type(): Int = ITransmitRequest.STOP_SERVICE

    override fun requestData(): Parcelable? {
        return null
    }

    override fun reply(resp: ReportResponseData?) {
        callback?.invoke(resp?.isSucceed == true)
    }
}