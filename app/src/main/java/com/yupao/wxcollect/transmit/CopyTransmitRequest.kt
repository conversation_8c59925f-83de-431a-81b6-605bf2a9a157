package com.yupao.wxcollect.transmit

import com.yupao.wxcollect.transmit.entity.CopyEntity
import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * <AUTHOR>
 * @Date 2023/4/14/014 15:09
 * @Description 复制
 */
class CopyTransmitRequest(
    private val sourceFile: String,
    private val destDir: String,
    private val callback: ((Boolean) -> Unit)? = null
): ITransmitRequest<CopyEntity> {

    override fun type(): Int = ITransmitRequest.COPY_FILE

    override fun requestData(): CopyEntity {
        return CopyEntity(sourceFile, destDir)
    }

    override fun reply(resp: ReportResponseData?) {
        callback?.invoke(resp?.isSucceed == true)
    }
}