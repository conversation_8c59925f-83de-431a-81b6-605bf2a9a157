package com.yupao.wxcollect.transmit.resp

import android.os.Bundle
import android.os.Message
import android.os.Messenger
import androidx.core.os.bundleOf
import com.google.gson.Gson
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.AppPathCache
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.db.DatabaseHelperImpl
import com.yupao.wxcollect.service.db.DatabaseOpenResultEntity
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity
import com.yupao.wxcollect.service.procedure.entity.request.ChangeWechatRequestModel
import com.yupao.wxcollect.service.procedure.model.ReportRepositoryEntryPoint
import com.yupao.wxcollect.service.procedure.task.WechatAccountNewTask
import com.yupao.wxcollect.transmit.ITransmitRequest
import com.yupao.wxcollect.transmit.ITransmitResponse
import com.yupao.wxcollect.transmit.entity.DatabaseEntity
import com.yupao.wxcollect.transmit.entity.ReportResponseData
import com.yupao.wxcollect.ui.wechat.WechatAccount
import com.yupao.wxcollect.ui.wechat.WechatSelectHelperExtensions
import com.yupao.wxcollect.util.SpUtil
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/5/18/018 15:07
 * @Description
 */
class DatabaseCheckTransmitResponse : ITransmitResponse {

    companion object {
        private const val TAG = "DbCheckResp"
    }

    private val gson by lazy { Gson() }

    private val resp by lazy {
        EntryPointAccessors.fromApplication(
            AndroidSystemUtil.getContext(),
            ReportRepositoryEntryPoint::class.java
        ).getReportRepository()
    }


    override fun onResponse(message: Message?, request: String?): ReportResponseData? {
        YLog.i(TAG, "onResponse: message = $message, request = $request")
        if (request.isNullOrEmpty()) {
            return ReportResponseData(false)
        }
        val entity = kotlin.runCatching {
            gson.fromJson(request, DatabaseEntity::class.java)
        }.getOrNull() ?: return ReportResponseData(false)
        val databasePath = entity.wechatDatabasePath ?: return ReportResponseData(false)
        val origFile = File(databasePath)
        val replyTo = message?.replyTo ?: return ReportResponseData(false)
        val isFileExisted = WechatFileUtil.isFileExisted(databasePath)
        if (!isFileExisted) {
            return ReportResponseData(
                true,
                gson.toJson(
                    DatabaseOpenResultEntity(
                        code = DatabaseOpenResultEntity.CODE_FILE_NOT_EXIST,
                        message = "数据库文件不存在"
                    )
                )
            )
        }
        val destDir = entity.destDatabaseDir ?: let {
            return ReportResponseData(false)
        }
        val destFilePath = destDir + origFile.name
        val bundle = message.data ?: bundleOf()
        val isExists = File(destFilePath).exists()
        YLog.i(TAG, "destFilePath isExisted: $isExists, isCopyDb: ${entity.isCopyDb}")
        if (isExists) {
            if (entity.isCopyDb) {
                YLog.i(TAG, "destFilePath delete file")
                WechatFileUtil.deleteFile(listOf(destFilePath)) {
                    performCopy(databasePath, destDir, destFilePath, entity, replyTo, bundle)
                }
            } else {
                CoroutinePool.coreTask.launch {
                    performOpenDatabase(
                        true,
                        destFilePath,
                        entity,
                        replyTo,
                        bundle,
                        entity.isUseInnerDir
                    )
                }
            }
        } else {
            performCopy(databasePath, destDir, destFilePath, entity, replyTo, bundle)
        }
        return null
    }

    private fun performCopy(
        databasePath: String,
        destDir: String,
        destFilePath: String,
        entity: DatabaseEntity,
        replyTo: Messenger,
        bundle: Bundle,
    ) {
        YLog.i(TAG, "performCopy: $destDir")
        WechatFileUtil.copy(
            databasePath,
            destDir,
        ) {
            GlobalScope.launch(Dispatchers.IO) {
                performOpenDatabase(it, destFilePath, entity, replyTo, bundle, entity.isUseInnerDir)
            }
        }
    }

    private suspend fun performOpenDatabase(
        it: Boolean,
        destFilePath: String,
        entity: DatabaseEntity,
        replyTo: Messenger,
        bundle: Bundle,
        isUseInnerDir: Boolean,
    ) {
        val replayMessage = Message.obtain()
        if (it) {
            YLog.i(TAG, "open database")
            val result = kotlin.runCatching {
                openDatabase(destFilePath, entity)
            }.getOrElse {
                YLog.printException(TAG, it, "openDatabase")
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, it.message)
            }
            YLog.i(TAG, "performOpenDatabase: result = $result")
            if (result.code == DatabaseOpenResultEntity.CODE_OK) {
                AppPathCache.setUseInnerDir(isUseInnerDir)
            }
            bundle.putString(
                ITransmitRequest.KEY_RESPONSE,
                gson.toJson(ReportResponseData(true, gson.toJson(result)))
            )
        } else {
            bundle.putString(
                ITransmitRequest.KEY_RESPONSE,
                gson.toJson(ReportResponseData(false, null, "文件拷贝失败"))
            )
        }
        kotlin.runCatching {
            replayMessage.data = bundle
            replyTo.send(replayMessage)
        }
    }

    private suspend fun openDatabase(
        databasePath: String,
        entity: DatabaseEntity
    ): DatabaseOpenResultEntity {
        // 使用优化后的方法
        return openDatabaseOptimized(databasePath, entity)
    }

    private suspend fun checkUser(
        userEntity: UserEntity,
        entity: DatabaseEntity
    ): DatabaseOpenResultEntity? {
        val rep = EntryPointAccessors.fromApplication(
            App.getContext()!!,
            ReportRepositoryEntryPoint::class.java
        ).getReportRepository()
        return kotlin.runCatching {
            val resp = rep.changeWechat(
                ChangeWechatRequestModel(
                    deviceId = SpUtil.getString(SpUtil.KEY_DEVICE_UNIQUE),
                    wechatAccount = userEntity.alias,
                    systemType = if (entity.appType == ReportConfig.APP_SUB) "4" else "3",
                    originId = userEntity.username
                )
            )
            YLog.i(TAG, "checkUser: $resp")
            if (resp.isOK()) {
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_OK, null)
            } else {
                // 该微信号和手机编号绑定不一致，请到资产管理中进行换绑后再进行提交
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_DIALOG, resp.getMsg())
            }
        }.onFailure {
            YLog.printException(TAG, it, "checkUser")
        }.getOrElse {
            DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, it.message)
        }
    }

    /**
     * 优化后的数据库打开方法
     */
    private suspend fun openDatabaseOptimized(
        databasePath: String,
        entity: DatabaseEntity
    ): DatabaseOpenResultEntity {
        val help = DatabaseHelperImpl()
        val result = help.open(databasePath, entity.password)

        if (result.code != DatabaseOpenResultEntity.CODE_OK) {
            help.close()
            return result
        }

        return try {
            val userEntity = getUserEntity(help, databasePath, entity.appType)
            if (userEntity != null && !userEntity.username.isNullOrEmpty()) {

                //
                checkUser(userEntity, entity) ?: result
            } else {
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, "未获取到用户信息")
            }
        } catch (e: Exception) {
            YLog.printException(TAG, e, "openDatabase error")
            DatabaseOpenResultEntity(
                DatabaseOpenResultEntity.CODE_FAILED,
                "数据库打开异常: ${e.message}"
            )
        } finally {
            help.close()
        }
    }

    /**
     * 获取用户实体信息
     */
    private suspend fun getUserEntity(
        help: DatabaseHelperImpl,
        databasePath: String,
        appType: String
    ): UserEntity? {
        val task = WechatAccountNewTask(databasePath)

        // 首先尝试从别名获取用户信息
        var userEntity = task.performAliasUser(help)
        if (userEntity != null) {
            return userEntity
        }

        // 如果没有获取到，则通过接口查询
        val phoneNumber = SpUtil.getString(SpUtil.KEY_PHONE_NUM) ?: ""
        val queryResult = kotlin.runCatching {
            resp.queryByPhoneNo(phoneNumber)
        }.getOrElse { e ->
            YLog.i(TAG, "查询手机号对应微信信息失败")
            null
        }
        val wechatInfos = if (queryResult?.isOK() == true) {
            queryResult.getData()?.wechatInfos ?: emptyList()
        } else {
            emptyList()
        }

        //需要区分主副系统
        val systemPart = if (appType == ReportConfig.APP_MAIN) "3" else "4"
        val accounts = wechatInfos.filter { systemPart == it.systemPart }
            .map { WechatAccount(it.nickname.toString(), it.wechatAccount.toString()) }
//        val accounts =
//            arrayListOf(WechatAccount("张三", "***********"), WechatAccount("李四", "***********"))
        // 如果通过接口返回数据不为空则进行选择
        return if (accounts.isNotEmpty()) {
            handleAccountSelection(accounts, task, help)
        } else {
            null
        }
    }

    /**
     * 处理账号选择逻辑
     */
    private suspend fun handleAccountSelection(
        accounts: List<WechatAccount>,
        task: WechatAccountNewTask,
        help: DatabaseHelperImpl
    ): UserEntity? {
        while (true) {
            // 选择账号
            val selectedAccount = if (accounts.size == 1) {
                // 如果只有一个账号，直接进入确认流程
                accounts.first()
            } else {
                // 多个账号，进行选择
                WechatSelectHelperExtensions.selectWechatAccount(accounts = accounts)
            }

            if (selectedAccount == null) {
                YLog.i(TAG, "用户取消选择")
                return null
            }

            YLog.i(TAG, "用户选择了账号: ${selectedAccount.alias}")

            // 选择账号后去查询数据
            val userEntity = task.perform(help, selectedAccount.alias)

            // 查询出来后如果为空直接返回
            if (userEntity == null) {
                YLog.w(TAG, "查询用户数据为空，直接返回")
                return null
            }

            YLog.i(TAG, "查询到用户数据: $userEntity")

            // 如果不为空调用确认弹窗
            val confirmedAccount = WechatSelectHelperExtensions.confirmWechatAccount(
                WechatAccount(
                    "${userEntity.nickname}",
                    "${userEntity.alias}",
                )
            )

            when {
                confirmedAccount != null -> {
                    // 如果点击确认则返回确认的账号
                    YLog.i(TAG, "用户确认了账号: ${confirmedAccount.alias}")
                    return userEntity
                }

                accounts.size == 1 -> {
                    // 如果数据返回一个数据调用确认弹窗 点击修改时直接返回
                    YLog.i(TAG, "单个账号确认时点击修改，直接返回")
                    return null
                }

                else -> {
                    // 多个账号时点击修改重新调起选择弹窗
                    YLog.i(TAG, "用户点击修改，重新进行选择")
                    // 继续循环，重新选择
                }
            }
        }
    }

}