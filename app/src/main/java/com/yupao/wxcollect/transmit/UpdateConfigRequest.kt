package com.yupao.wxcollect.transmit

import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * <AUTHOR>
 * @Date 2023/4/14/014 15:09
 * @Description 更新配置
 */
class UpdateConfigRequest(
    private val config: ReportConfig?,
    private val callback: ((Boolean) -> Unit)? = null
): ITransmitRequest<ReportConfig?> {

    override fun type(): Int = ITransmitRequest.UPDATE_CONFIG

    override fun requestData(): ReportConfig? {
        return config
    }

    override fun reply(resp: ReportResponseData?) {
        callback?.invoke(resp?.isSucceed == true)
    }
}