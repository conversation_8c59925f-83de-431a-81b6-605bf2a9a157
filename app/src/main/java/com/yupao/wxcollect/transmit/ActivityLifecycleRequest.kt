package com.yupao.wxcollect.transmit

import com.yupao.wxcollect.transmit.entity.LifecycleEntity
import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/21/021</p>
 *
 * <AUTHOR>
 */
class ActivityLifecycleRequest(
    private val entity: LifecycleEntity,
): ITransmitRequest<LifecycleEntity> {
    override fun type(): Int {
        return ITransmitRequest.ACTIVITY_LIFECYCLE
    }

    override fun requestData(): LifecycleEntity {
        return entity
    }

    override fun reply(resp: ReportResponseData?) {

    }

    override fun callbackCount(): Int {
        return 0
    }
}