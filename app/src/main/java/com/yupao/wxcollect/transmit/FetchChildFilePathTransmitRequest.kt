package com.yupao.wxcollect.transmit

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.yupao.wxcollect.transmit.entity.ReportResponseData

/**
 * <AUTHOR>
 * @Date 2023/4/14/014 15:09
 * @Description 获取文件一级子目录
 */
class FetchChildFilePathTransmitRequest(
    private val destFilePath: String,
    private val callback: ((List<String>?) -> Unit)? = null
): ITransmitRequest<String> {

    override fun type(): Int = ITransmitRequest.FETCH_CHILD_FILE_PATH

    override fun requestData(): String {
        return destFilePath
    }

    override fun reply(resp: ReportResponseData?) {
        val result = kotlin.runCatching {
            if (resp?.isSucceed == true) {
                val list: List<String> = Gson().fromJson(resp.response, object : TypeToken<List<String>>() {}.type)
                list
            } else null
        }.getOrNull()
        callback?.invoke(result)
    }
}