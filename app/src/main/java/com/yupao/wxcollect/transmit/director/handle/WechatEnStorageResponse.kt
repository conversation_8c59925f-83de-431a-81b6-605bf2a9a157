package com.yupao.wxcollect.transmit.director.handle

import com.google.gson.Gson
import com.yupao.data.protocol.doError
import com.yupao.data.protocol.doSuccess
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.execute.IMessageResponse
import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.AppPath
import com.yupao.wxcollect.constant.AppPathCache
import com.yupao.wxcollect.constant.WECHAT_DATABASE_NAME
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.transmit.entity.WechatEnStorageEntity
import com.yupao.wxcollect.ui.config.model.ReportConfigRepoEntryPoint
import com.yupao.wxcollect.ui.config.model.ReportConfigRepository
import com.yupao.wxcollect.util.SpUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.launch
import java.io.File
import com.yupao.execute.wxcollect.BuildConfig

/**
 * 获取微信EN
 *
 * <p>创建时间：2024/11/11/011</p>
 *
 * <AUTHOR>
 */
class WechatEnStorageResponse : IMessageResponse {

    companion object {
        private const val TAG = "WechatEnStorageResponse"
        private const val KEY_DATABASE_BASE_DIR = "database_base_dir"

        // 主系统微信EN存储空间大小
        var mainSize: String = ""

        // 副系统微信EN存储空间大小
        var subSize: String = ""
        private fun getResetDatabaseBaseDirName(): String {
            if (!BuildConfig.DEBUG) {
                return ""
            }
            return SpUtil.getString(KEY_DATABASE_BASE_DIR) ?: ""
        }
    }

    private val resp: ReportConfigRepository by lazy {
        EntryPointAccessors.fromApplication(
            App.getContext()!!,
            ReportConfigRepoEntryPoint::class.java
        ).getRepo()
    }


    override fun onRequestReceived(
        fromHost: String?,
        args: ArgsEntity?,
        responseEmit: (ArgsEntity) -> Unit
    ) {
        YLog.i(TAG, "onRequestReceived start")
        CoroutinePool.other.launch {
            getWechatEnStorageSize(callbackSuc = {
                mainSize = it.mainSystemWechatEnStorageSize ?: ""
                subSize = it.subSystemWechatEnStorageSize ?: ""
                responseEmit.invoke(
                    ArgsEntity().put(
                        MessageKey.Data,
                        Gson().toJson(
                            WechatEnStorageEntity(mainSize, subSize)
                        )
                    )
                )

            }, callbackError = {
                responseEmit.invoke(
                    ArgsEntity().put(
                        MessageKey.Data,
                        Gson().toJson(
                            WechatEnStorageEntity(mainSize, subSize)
                        )
                    )
                )
            })
        }
    }

    private suspend fun getWechatEnStorageSize(
        callbackSuc: (WechatEnStorageEntity) -> Unit,
        callbackError: () -> Unit
    ) {
        val entity = WechatEnStorageEntity()
        //查询主系统数据库大小
        resp.queryAll().collect {
            it.doSuccess {
                val data = it.data
                if (data != null) {
                    data.forEach {
                        val databasePath = it.databasePath
                        if (!databasePath.isNullOrBlank()) {
                            val databaseBaseName =
                                File(databasePath).parentFile?.name ?: (databasePath.hashCode()
                                    .toString())
                            val dirName = getResetDatabaseBaseDirName()
                            val destDir = AppPath.contact(
                                AppPathCache.appWechatDir(),
                                databaseBaseName,
                                dirName
                            ) + File.separator
                            val databaseFile =
                                File(destDir, WECHAT_DATABASE_NAME).takeIf { it.exists() }

                            when (it.appType) {
                                ReportConfig.APP_MAIN -> databaseFile
                                    ?.let {
                                        entity.mainSystemWechatEnStorageSize =
                                            "%.2f".format(getFileSizeInKB(it.absolutePath))
                                    }

                                ReportConfig.APP_SUB -> databaseFile
                                    ?.let {
                                        entity.subSystemWechatEnStorageSize =
                                            "%.2f".format(getFileSizeInKB(it.absolutePath))
                                    }
                            }
                        }
                    }
                    callbackSuc.invoke(entity)
                } else {
                    YLog.i(TAG, "data is null")
                    callbackError.invoke()
                }
            }
            it.doError {
                YLog.i(TAG, "queryAll is error")
                callbackError.invoke()
            }
        }
    }

    private fun getFileSizeInKB(filePath: String): Double {
        val file = File(filePath)
        val sizeInBytes = file.length().toDouble()
        return sizeInBytes / 1024  // 1 KB = 1024 Bytes
    }
}
