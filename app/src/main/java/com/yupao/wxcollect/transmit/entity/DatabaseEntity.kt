package com.yupao.wxcollect.transmit.entity

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * @Date 2023/5/18/018 14:58
 * @Description
 */
@Keep
@Parcelize
data class DatabaseEntity(
    val wechatDatabasePath: String?,
    val destDatabaseDir: String?,
    val password: String?,
    val isUseInnerDir: Boolean,
    // app类型
    val appType: String,
    // 之前的微信数据库路径
    val oldWechatDatabasePath: String?,
    // 是否拷贝数据库
    val isCopyDb: Boolean,
    override val responseClass: String?,
): ITransmitResponseEntity, Parcelable