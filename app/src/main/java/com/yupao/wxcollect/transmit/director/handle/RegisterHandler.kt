package com.yupao.wxcollect.transmit.director.handle

import android.os.Handler
import android.os.Looper
import com.yupao.transmit.MessageConstant
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.execute.MessageEntity
import com.yupao.transmit.handle.IMessageHandler
import com.yupao.utils.system.asm.AndroidSystemUtil

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/15/015</p>
 *
 * <AUTHOR>
 */
class RegisterHandler constructor(
    private val toHost: String,
    private val maxCountRetry: Int = 3,
    private val delayTimeRegister: Long = 1000L,
): IMessageHandler {

    /**
     * 是否注册成功
     */
    var isRegisterSucceed = false
        private set

    private val handler = Handler(Looper.getMainLooper()) {
        return@Handler true
    }

    fun register() {
        handler.postDelayed(registerRunnable, delayTimeRegister)
    }

    private val registerRunnable = Runnable {
        MessageManager.executor.submit(
            MessageEntity(
                code = MessageConstant.Code.Register,
                toHost = toHost,
            ).put(MessageConstant.Key.PackageName, AndroidSystemUtil.getPackageName())
        )
    }


    override fun handle(fromHost: String?, args: ArgsEntity?) {
        this.isRegisterSucceed = true
    }

    fun destroy() {
        this.isRegisterSucceed = false
        this.handler.removeCallbacks(registerRunnable)
    }
}