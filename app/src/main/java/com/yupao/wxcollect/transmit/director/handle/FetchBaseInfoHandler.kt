package com.yupao.wxcollect.transmit.director.handle

import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.execute.MessageEntity
import com.yupao.transmit.handle.IMessageHandler
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.util.SpUtil
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/7/007</p>
 *
 * <AUTHOR>
 */
class FetchBaseInfoHandler: IMessageHandler {
    override fun handle(fromHost: String?, args: ArgsEntity?) {
        val phoneNum = SpUtil.getString(SpUtil.KEY_PHONE_NUM)
        val currUrl = SpUtil.getString(SpUtil.KEY_CURR_URL)
        val deviceUnique = SpUtil.getString(SpUtil.KEY_DEVICE_UNIQUE)
        YLog.i("FetchBaseInfoHandler", "phoneNum: $phoneNum, currUrl: $currUrl")
        MessageManager.executor.submit(
            MessageEntity(
                MessageCode.UPDATE_BASE_INFO,
                AppConstant.Host.wxCollectUI
            )
                .put(MessageKey.PhoneNumber, phoneNum)
                .put(MessageKey.CurrUrl, currUrl)
                .put(MessageKey.DeviceUnique, deviceUnique)
        )
    }
}