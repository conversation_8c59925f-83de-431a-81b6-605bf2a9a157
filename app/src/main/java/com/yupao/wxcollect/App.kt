package com.yupao.wxcollect

import android.annotation.SuppressLint
import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Process
import android.util.Log
import com.rwz.hook.core.app.ContextHelp
import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.keepalive.KeepAliveManager
import com.yupao.net.NetConfig
import com.yupao.transmit.MessageManager
import com.yupao.utils.log.LogUtil
import com.yupao.utils.system.AppRuntimeUtils
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.utils.system.asm.PhoneUtils
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.constant.AppPathCache
import com.yupao.wxcollect.service.IReportServiceManager
import com.yupao.wxcollect.service.NetworkConnectionIntentReceiver
import com.yupao.wxcollect.service.PackageInstalledReceiver
import com.yupao.wxcollect.service.ReportService
import com.yupao.wxcollect.service.SystemStatusManager
import com.yupao.wxcollect.service.monitor.MemoryMonitorManager
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.ui.ActivityLifecycleManager
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.main.MainActivity
import com.yupao.wxcollect.ui.upload_node.UploadNodeActivity
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.wxcollect.util.CrashHandler
import com.yupao.ylog.LogConfig
import com.yupao.ylog.LogManager
import com.yupao.ylog.YLog
import com.yupao.ylog.log.ILogger
import com.yupao.ylog.log.LogLevel
import com.yupao.ylog.log.LogMode
import dagger.hilt.android.HiltAndroidApp
import java.util.Arrays

/**
 * <AUTHOR>
 * @Date 2023/4/6/006 10:50
 * @Description
 */
@HiltAndroidApp
class App: Application() {

    companion object {
        @SuppressLint("StaticFieldLeak")
        private var context: Context? = null

        fun getContext() = context

        fun setContext(context: Context?) {
            Log.d("TAG", "setContext: $context")
            val ctx = context ?: return
            this.context = ctx
            init(ctx)
        }

        private fun init(context: Context?) {
            val app = context as? Application ?: return
            AndroidSystemUtil.init(app)
            AppRuntimeUtils.openDebug(BuildConfig.DEBUG)
            LogUtil.enableLog(BuildConfig.DEBUG)
            CrashHandler.getInstance().init(app)
            ContextHelp.setContext(app)
            LogManager.init(app, LogConfig(
                uid = "",
                logMode = LogMode.LOGCAT
            ))
            NetConfig.addGlobalStaticHeader("version", PhoneUtils.getVersionCode(app).toString())
            NetConfig.addGlobalStaticHeader("business", "1")
            NetConfig.addGlobalStaticHeader("system_type", "android")
            NetConfig.addGlobalStaticHeader("Content-Type", "application/json")
            kotlin.runCatching {
                hookLog(app)
            }.onFailure {
                it.printStackTrace()
            }
        }

        private fun hookLog(context: Context) {
            val logger = LogManager.getLogger()
            LogManager.setLogger(object :ILogger {
                private fun parseString(msg: Any?): String {
                    if (msg == null) {
                        return "null"
                    }
                    if (msg is Array<*>) {
                        return Arrays.toString(msg)
                    }
                    return msg.toString()
                }

                private fun createLocalLog(@LogLevel logLevel: Int, tag: String, msg: String): String {
                    val threadId = Thread.currentThread().id
                    val simpleName = LogLevel.getSimpleName(logLevel)
                    return "$threadId $simpleName/$tag: $msg\n"
                }

                override fun printException(tag: String?, exception: Throwable?, extra: String?) {
                    logger?.printException(tag, exception, extra)
                    val stackTrace = exception?.stackTraceToString()
                    val threadId = Thread.currentThread().id
                    com.yupao.wxcollect.util.LogUtil.addLog("$threadId $tag $extra \n$stackTrace")
                }

                override fun printJson(logLevel: Int, tag: String?, msg: String?) {
                    logger?.printJson(logLevel, tag, msg)
                }

                override fun printMsg(logLevel: Int, tag: String?, msg: Any?) {
                    logger?.printMsg(logLevel, tag, msg)
                    if (logLevel != LogLevel.DEBUG) {
                        val logMsg = createLocalLog(logLevel, tag ?: "Logger", parseString(msg))
                        com.yupao.wxcollect.util.LogUtil.addLog(logMsg)
                    }
                }

                override fun printObject(logLevel: Int, tag: String?, msg: Any?) {
                    logger?.printObject(logLevel, tag, msg)
                }
            })
            YLog.resetLogger()
        }
    }

    private val handler by lazy { Handler(Looper.getMainLooper()) }

    private fun getProcessName(context:Context) : String? {
        if (Build.VERSION.SDK_INT >= 33) {
            return Process.myProcessName()
        } else {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
            val mutableList:List<ActivityManager.RunningAppProcessInfo>? = activityManager?.runningAppProcesses
            mutableList?.forEach {
                if (it.pid == Process.myPid())
                    return it.processName
            }
        }
        return null
    }

    override fun onCreate() {
        super.onCreate()
        setContext(this)
        val processName = getProcessName(this)
        YLog.i("App", "onCreate: processName = $processName")
        // service进程
        startReportService()
        AppPathCache.initRootDir()
        PackageInstalledReceiver.register(this)
        if (processName?.contains(":report") == true) {
            val list = listOf(
                "${CoreClient.servicePackageName}/${CoreClient.activityName}",
                "${packageName}/${UploadNodeActivity::class.java.name}",
            )
            KeepAliveManager.init(this, ReportService::class.java, list) {
                CmdUtil.execute(it).result.split("\n")
            }
            checkServiceDelay()
            checkSysStatus()
        } else if (processName == packageName) {
            ActivityLifecycleManager.initApp(this)
        }
        MessageManager.isDebug = BuildConfig.DEBUG
    }

    private val networkReceiver by lazy { NetworkConnectionIntentReceiver()}

    private fun checkSysStatus(){
        SystemStatusManager.initialize()
        networkReceiver.register(this)
        MemoryMonitorManager.initialize()
        MemoryMonitorManager.startMonitoring()
    }

    private fun startReportService() {
        IReportClientManager.instance.startService(
            this,
            from = IReportServiceManager.KEY_FROM_APP_APPLICATION,
            config = NotificationConfig(
                pendingActivityPath = MainActivity::class.java.name,
                content = "已启动服务"
            )
        )
    }

    private fun checkServiceDelay() {
        handler.postDelayed({
            val running = ReportService.isRunning
            YLog.i("App", "checkServiceDelay: running = $running")
            if (!running) {
                val packageName = context?.packageName ?: return@postDelayed
                val canonicalName = MainActivity::class.java.canonicalName ?: return@postDelayed
                CmdUtil.executeAsync("am start -n $packageName/$canonicalName")
            }
        }, 10_000)
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        YLog.i("onTrimMemory level: $level")
    }

    override fun onLowMemory() {
        super.onLowMemory()
        YLog.i("onLowMemory")
    }

    override fun onTerminate() {
        super.onTerminate()
        YLog.i("onTerminate")
    }
}