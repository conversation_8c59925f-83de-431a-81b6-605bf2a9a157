package com.yupao.wxcollect.util

import android.app.ActivityManager
import android.content.Context
import android.os.Process
import com.rwz.hook.utils.ToastUtil
import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.DeviceConfig
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @Date 2023/5/19/019 20:29
 * @Description 日志工具类
 */
object LogUtil {
    private const val maxLength = 500000

    private const val INVALID_TIME = 48 * 3600_000L

    private val SEPARATOR = File.separator

    private val logCache = StringBuffer()

    private val writeMutex = Mutex()

    private val deleteMutex = Mutex()

    init {
        saveLogLooper()
    }

    fun addLog(_text: String?) {
        kotlin.runCatching {
            val text = _text.toString()
            if (logCache.length > maxLength) {
                saveCacheLog(false)
                logCache.append(TimeUtil.simpleFormat3()).append(" $text")
            } else {
                logCache.append(TimeUtil.simpleFormat3()).append(" $text")
            }
        }
    }

    @Synchronized
    private fun saveCacheLog(isAppend: Boolean) {
        if (logCache.isEmpty()) {
            return
        }
        saveToFile(showToast = false, isAppend = isAppend)
        logCache.setLength(0)
    }

    fun getBaseFile(baseDir: String): String? {
        val baseFile = (App.getContext()?.getExternalFilesDir(null) ?: App.getContext()?.filesDir)
        return baseFile?.absolutePath?.let {
            it + SEPARATOR + "log" + SEPARATOR + baseDir + SEPARATOR
        }
    }

    fun getBaseReportFile(appType: String?, flag: String): File {
        val baseFile = App.getContext()?.getExternalFilesDir(null) ?: App.getContext()?.filesDir
        val path = if (appType == null) {
            "${baseFile?.absolutePath}${SEPARATOR}log${SEPARATOR}report${SEPARATOR}$flag${SEPARATOR}"
        } else {
            "${baseFile?.absolutePath}${SEPARATOR}log${SEPARATOR}report${SEPARATOR}${appType}${SEPARATOR}${flag}${SEPARATOR}"
        }
        return File(path)
    }

    private fun getProcessName(context: Context?): String? {
        context ?: return null
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
        val mutableList:List<ActivityManager.RunningAppProcessInfo>? = activityManager?.runningAppProcesses
        mutableList?.forEach {
            if (it.pid == Process.myPid())
                return it.processName
        }
        return null
    }

    private val simpleProcessName by lazy {
        getProcessName(App.getContext())?.let {
            val index = it.indexOf(":")
            if (index > 0) {
                it.substring(index + 1)
            } else {
                "main"
            }
        }
    }

    // 上一个保存日志的文件
    private var mPrevLogFile: File? = null

    private val isWritingLog = AtomicBoolean(false)

    fun saveToFile(
        baseDir: String = "log",
        extraText: String? = null,
        content: String = logCache.toString(),
        showToast: Boolean = true,
        // 是否追加写入
        isAppend: Boolean = false,
    ) {
        CoroutinePool.coreTask.launch(Dispatchers.IO) {
            writeMutex.withLock {
                kotlin.runCatching {
                    if (saveLog(baseDir, content, extraText, showToast, isAppend)) return@launch
                }
            }
        }
    }

    fun saveLogSync(extraText: String?, showToast: Boolean) {
        saveLog("log", logCache.toString(), extraText, showToast)
    }

    private fun saveLog(
        baseDir: String,
        content: String,
        extraText: String?,
        showToast: Boolean,
        // 是否追加写入
        isAppend: Boolean = false,
    ): Boolean {
        isWritingLog.set(true)
        try {
            val baseFile = getBaseFile(baseDir) ?: return true
            val prevFile = mPrevLogFile
            // 是否追加模式，当前写入文件字节小于10M才支持
            val isAppendMode = isAppend && prevFile != null && prevFile.length() < 10_000_000
            val file = if (isAppendMode) {
                prevFile!!
            } else {
                val filePath = "${baseFile}${simpleProcessName}$SEPARATOR${TimeUtil.fileNameFormat()}.log"
                File(filePath)
            }
            mPrevLogFile = file
            val parentFile = file.parentFile
            if (parentFile != null) {
                if (!parentFile.exists()) {
                    parentFile.mkdirs()
                } else {
                    // 删除过期文件
                    kotlin.runCatching {
                        deleteFile(parentFile, DeviceConfig.logStorageTime ?: INVALID_TIME, Int.MAX_VALUE)
                    }
                }
            }
            if (isAppendMode) {
                file.appendText(content)
            } else {
                file.writeText(content)
            }
            if (!extraText.isNullOrEmpty()) {
                file.appendText("\n" + extraText)
            }
            if (showToast) {
                ToastUtil.showShort("已保存: ${file.absolutePath}")
            }
        } finally {
            isWritingLog.set(false)
        }
        return false
    }

    fun deleteFileSync(dirFile: File, invalidTime: Long = DeviceConfig.logStorageTime ?:INVALID_TIME, maxCount: Int = Int.MAX_VALUE) {
        CoroutinePool.coreTask.launch(Dispatchers.IO) {
            deleteMutex.withLock {
                kotlin.runCatching {
                    deleteFile(dirFile, invalidTime, maxCount)
                }
            }
        }
    }

    private fun deleteFile(dirFile: File, invalidDuration: Long, maxCount: Int) {
        if (!dirFile.exists()) {
            return
        }
        YLog.d("deleteFile: ${dirFile.absolutePath}")
        // 文件已重新按从新到旧排序
        val list = toFileList(dirFile)
        var totalCount = 0L
        val invalidTime = System.currentTimeMillis() - invalidDuration
        var deleteCount = 0
        list.forEachIndexed { index, file ->
            // 最新的一个文件不删除
            if (index == 0) {
                return@forEachIndexed
            }
            totalCount += file.length()
            if (file.lastModified() < invalidTime || index >= maxCount) {
                YLog.d("deleteFile: ${TimeUtil.formatCurrTime(file.lastModified())}, ${TimeUtil.formatCurrTime(invalidTime)}")
                file.delete()
                deleteCount++
            }
        }
        YLog.d("deleteFile: ${list.size}, delete-count: $deleteCount")
    }

    private fun toFileList(file: File): List<File> {
        return file.listFiles()
            ?.sortedWith { one, two ->
                (two.lastModified() - one.lastModified()).toInt()
            }
            ?: emptyList()
    }

    private fun saveLogLooper() {
        CoroutinePool.coreTask.launch {
            while (isActive) {
                delay(10_000L)
                saveCacheLogForce()
            }
        }
    }

    fun saveCacheLogForce() {
        YLog.d("LogUtil", "saveCacheLogForce")
        if (isWritingLog.get()) {
            return
        }
        saveCacheLog(true)
    }
}