package com.yupao.wxcollect.util.delete

import java.io.File

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/30/030</p>
 *
 * <AUTHOR>
 */
interface IFileFilter {

    fun filter(index: Int, file: File): Bo<PERSON>an

}

/**
 * 限制最大占用空间
 */
class MaxSizeFilter(private val maxSize: Long): IFileFilter {

    private var totalSize = 0L

    override fun filter(index: Int, file: File): Boolean {
        totalSize += file.length()
        return totalSize < maxSize
    }
}

/**
 * 限制最小修改时间
 */
class LastModifyFilter(secondTime: Long): IFileFilter {

    private val validTime = if(secondTime == Long.MAX_VALUE) 0 else (System.currentTimeMillis() - secondTime * 1000)

    override fun filter(index: Int, file: File): Boolean {
        return file.lastModified() > validTime
    }
}

/**
 * 限制最大数量
 */
class MaxCountFilter(private val maxCount: Int): IFileFilter {

    override fun filter(index: Int, file: File): Bo<PERSON>an {
        return (index + 1) <= maxCount
    }
}

/**
 * 限制最小数量
 */
class MinCountFilter(private val minCount: Int): IFileFilter {
    override fun filter(index: Int, file: File): Boolean {
        println("MinCountFilter index: $index, minCount: $minCount, ${file.absolutePath}")
        return (index + 1) >= minCount
    }
}