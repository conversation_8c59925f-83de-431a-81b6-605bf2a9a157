package com.yupao.wxcollect.util

import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.WECHAT_DATABASE_NAME
import com.yupao.wxcollect.service.procedure.entity.request.FileType
import com.yupao.wxcollect.service.procedure.entity.request.FileUploadParam
import com.yupao.wxcollect.transmit.CheckFileExistTransmitRequest
import com.yupao.wxcollect.transmit.CopyTransmitRequest
import com.yupao.wxcollect.transmit.FetchChildFilePathTransmitRequest
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.ylog.YLog
import me.zhanghai.android.files.FileUtil
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 16:47
 * @Description 微信文件工具
 */
object WechatFileUtil {

    /**
     * 复制文件
     */
    fun copy(sourceFile: String, destDir: String, callback: ((Boolean) -> Unit)? = null) {
        val file = File(destDir)
        if (!file.exists()) {
            file.mkdirs()
        }
        val source = if(sourceFile.startsWith("/")) sourceFile.substring(1) else sourceFile
        val dest = if(destDir.startsWith("/")) destDir.substring(1) else destDir
        YLog.i(
            "WechatFileUtil",
            "copy: sourceFile = $source, destFile = $dest"
        )
        FileUtil.copy(
            App.getContext() ?: return,
            "/",
            arrayOf(source),
            "/",
            arrayOf(dest),
        ) {
            YLog.i("WechatFileUtil", "copy-result: $it")
            if (it) {
                setFileReadWriteMode(dest, callback)
            } else {
                callback?.invoke(false)
            }
        }
    }

    fun setFileReadWriteMode(
        dest: String,
        callback: ((Boolean) -> Unit)?
    ) {
        val source = if(dest.startsWith("/")) dest.substring(1) else dest
        YLog.i("WechatFileUtil", "setFileReadWriteMode: $source")
        FileUtil.setMode(
            context = App.getContext(),
            "/",
            arrayOf(source)
        ) { result ->
            YLog.i("WechatFileUtil", "setMode-result: $result")
            callback?.invoke(result)
        }
    }

    fun deleteFile(filePath: List<String>?, callback: ((Boolean) -> Unit)? = null) {
        if (filePath.isNullOrEmpty()) {
            callback?.invoke(false)
            return
        }
        val result = filePath.map {
            if (it.startsWith("/")) {
                it.substring(1)
            } else {
                it
            }
        }
        FileUtil.delete(App.getContext() ?: return, result, callback)
    }

    /**
     * 查找数据库路径 32为 /data/user/0/com.tencent.mm/MicroMsg
     * eg: e0c69019eaa3c23f8d23c851f0634d43
     */
    suspend fun getDatabaseName(baseDir: String?, callback: ((List<String>?) -> Unit)? = null) {
        if (baseDir != null) {
            getChildFilePathFromOtherProcess(baseDir) {
                val result = filterDbPath(it?.map { it.substringAfterLast("/") })
                YLog.i("WechatFileUtil", "getDatabaseName2: result = $result")
                if (result.isEmpty()) {
                    val cmd = "ls $baseDir"
                    val list = CmdUtil.execCmdWithBack(cmd)
                    val result2 = filterDbPath(list)
                    YLog.i("WechatFileUtil", "getDatabaseName: result = $result2")
                    callback?.invoke(result2)
                } else {
                    callback?.invoke(result)
                }
            }
        } else {
            callback?.invoke(null)
        }
    }

    private fun filterDbPath(list: List<String>?): List<String> {
        val regex = Regex("\\w{32}")
        val result = list?.filter {
            it.matches(regex) && it != "ee1da3ae2100e09165c2e52382cfe79f"
        } ?: listOf()
        return result
    }

    fun getFileName(filePath: String?): String? {
        filePath ?: return null
        return File(filePath).name
    }

    fun getChildFilePath(filePath: String?): List<String> {
        return kotlin.runCatching {
            FileUtil.getChildFilePath(filePath)
        }.getOrNull() ?: listOf()
    }

    fun isFileExisted(filePath: String?): Boolean {
        return kotlin.runCatching {
            FileUtil.isFileExisted(filePath)
        }.getOrNull() == true
    }

    fun checkFileExistedFromOtherProcess(filePath: String, callback: ((Boolean) -> Unit)? = null) {
        IReportClientManager.instance.addTransmitRequest(
            CheckFileExistTransmitRequest(
                filePath,
                callback
            )
        )
    }

    fun getChildFilePathFromOtherProcess(filePath: String, callback: ((List<String>?) -> Unit)? = null) {
        IReportClientManager.instance.addTransmitRequest(
            FetchChildFilePathTransmitRequest(
                filePath,
                callback
            )
        )
    }

    fun copyFromOtherProcess(
        sourceFile: String,
        destDir: String,
        callback: ((Boolean) -> Unit)? = null
    ) {
        IReportClientManager.instance.addTransmitRequest(
            CopyTransmitRequest(
                sourceFile,
                destDir,
                callback
            )
        )
    }

    /**
     * 获取文件格式
     */
    fun guessFormat(fileUploadSimple: FileUploadParam): String {
        return when (fileUploadSimple.type) {
            FileType.IMAGE -> {
                "png"
            }
            FileType.VIDEO -> {
                "mp4"
            }
            else -> {
                val path = fileUploadSimple.path
                path.split(".").last()
            }
        }
    }

    /**
     * 文件重命名
     */
    suspend fun rename(sourcePath: String, newName: String): Result<String> {
        val sourceFile = File(sourcePath)
        if (sourceFile.name == newName) {
            return Result.success(sourcePath)
        }
        val newFile = File(sourceFile.parentFile, newName)
        val result = sourceFile.renameTo(newFile)
        if (result) {
            return Result.success(newFile.absolutePath)
        }
        YLog.w("WechatFileUtil", "rename fail, retry rename by cmd")
        // mv /sdcard/old.txt /sdcard/new.txt
        val cmdResult = CmdUtil.execute("mv $sourcePath ${newFile.absolutePath}")
        if (cmdResult.isOK()) {
            return Result.success(newFile.absolutePath)
        }
        YLog.w("WechatFileUtil", "rename fail, sourcePath: ${sourcePath}, newName: $newName, cmdResult: ${cmdResult.code}")
        return Result.failure(cmdResult.exception ?: Exception("rename fail"))
    }

    /**
     * 获取目录下所有EnMicroMsg，微信库损坏的时候会创建一个EnMicroMsg2.db……
     * 根据[dir]获取EnMicroMsgXX.db，按修改时间逆序排序。
     *
     * @param dir 数据库目录 eg. /data/user/0/com.tencent.mm/MicroMsg/ee1da3ae2100e09165c2e52382cfe79f
     * @return List<String> eg.
     *  /data/user/0/com.tencent.mm/MicroMsg/ee1da3ae2100e09165c2e52382cfe79f/EnMicroMsg.db
     *  /data/user/0/com.tencent.mm/MicroMsg/ee1da3ae2100e09165c2e52382cfe79f/EnMicroMsg2.db
     *  /data/user/0/com.tencent.mm/MicroMsg/ee1da3ae2100e09165c2e52382cfe79f/EnMicroMsg3.db
     *
     */
    suspend fun fetchAllDatabase(dir: String): List<String> {
        // 根据目录逆序获取所有文件
        val result = CmdUtil.execute("ls -t $dir", 10_000L)
        if (!result.isOK()) {
            return listOf("$dir${File.separator}$WECHAT_DATABASE_NAME")
        }
        val regex = Regex("EnMicroMsg(\\d)*\\.db")
        val map = result.result.split("\n").filter {
            it.isNotBlank() && it.matches(regex)
        }.map {
            "$dir${File.separator}$it"
        }
        if (map.isEmpty()) {
            return listOf("$dir${File.separator}$WECHAT_DATABASE_NAME")
        }
        return map
    }
}