package com.yupao.wxcollect.util

import com.yupao.transmit.execute.TimeoutException
import com.yupao.wxcollect.constant.CMD_TIMEOUT
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.io.DataInputStream
import java.io.DataOutputStream
import java.io.IOException

/**
 * <AUTHOR>
 * @Date 2023/4/21/021 10:31
 * @Description
 */
object CmdUtil {

    fun execCmd(cmd: String): String? {
        var os: DataOutputStream? = null
        var process: Process? = null
        try {
            process = Runtime.getRuntime().exec("su")
            os = DataOutputStream(process.outputStream)
            os.writeBytes(cmd + "\n")
            os.writeBytes("exit\n")
            os.flush()
            val result = process.waitFor()
            YLog.i("CmdUtil", "execCmd: $cmd => $result")
            return if(result == 0) null else "code: $result"
        } catch (e: Exception) {
            e.printStackTrace()
            return e.message
        } finally {
            os?.close()
            process?.destroy()
        }
    }

    fun execCmdWithBack(command: String): List<String> {
        val result = mutableListOf<String>()
        var dos: DataOutputStream? = null
        var dis: DataInputStream? = null
        try {
            val p = Runtime.getRuntime().exec("su") // 经过Root处理的android系统即有su命令
            dos = DataOutputStream(p.outputStream)
            dis = DataInputStream(p.inputStream)
            dos.writeBytes(
                """
                $command
                
                """.trimIndent()
            )
            dos.flush()
            dos.writeBytes("exit\n")
            dos.flush()
            var line: String? = null
            while (dis.readLine().also { line = it } != null) {
                line?.let {
                    result.add(it)
                }
            }
            p.waitFor()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        } finally {
            if (dos != null) {
                try {
                    dos.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            if (dis != null) {
                try {
                    dis.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        YLog.i("CmdUtil", "execCmdWithBack: command = $command => ${result.joinToString("\n")}")
        return result
    }

    /**
     * 异步执行，不关注结果
     */
    fun executeAsync(command: String, callback: (suspend (result: CmdResult) -> Unit)? = null) {
        CoroutinePool.coreTask.launch {
            val result = execute(command)
            callback?.invoke(result)
        }
    }

    suspend fun execute(command: String, timeout: Long = CMD_TIMEOUT): CmdResult {
        return withContext(Dispatchers.IO) {
            return@withContext withTimeoutOrNull(timeout) {
                val startTime = System.currentTimeMillis()
                val stringBuilder = StringBuilder()
                var code: Int = -1
                var exp: Exception? = null
                var dos: DataOutputStream? = null
                var dis: DataInputStream? = null
                try {
                    // 经过Root处理的android系统即有su命令
                    val p = Runtime.getRuntime().exec("su")
                    dos = DataOutputStream(p.outputStream)
                    dis = DataInputStream(p.inputStream)
                    dos.writeBytes(
                        """
                $command
                
                """.trimIndent()
                    )
                    dos.flush()
                    dos.writeBytes("exit\n")
                    dos.flush()
                    var line: String?
                    while (dis.readLine().also { line = it } != null) {
                        line?.let {
                            stringBuilder.append(it).append("\n")
                        }
                    }
                    code = p.waitFor()
                } catch (e: java.lang.Exception) {
                    YLog.printException("CmdUtil", e)
                    exp = e
                } finally {
                    if (dos != null) {
                        try {
                            dos.close()
                        } catch (e: IOException) {
                            e.printStackTrace()
                        }
                    }
                    if (dis != null) {
                        try {
                            dis.close()
                        } catch (e: IOException) {
                            e.printStackTrace()
                        }
                    }
                }
                val result = CmdResult(
                    code = code,
                    result = stringBuilder.toString(),
                    exception = exp,
                )
                val duration = System.currentTimeMillis() - startTime
                if (duration > 3000) {
                    YLog.w("CmdUtil", "execCmdWithBack: duration: ${duration}ms, $command => $result")
                } else {
                    YLog.i("CmdUtil", "execCmdWithBack: duration: ${duration}ms, $command => $result")
                }
                result
            } ?: run {
                YLog.w("CmdUtil", "execCmdWithBack timeout, $command")
                CmdResult(
                    code = -1,
                    result = "",
                    exception = TimeoutException("timeout command: $command"),
                )
            }
        }
    }
}

data class CmdResult(
    val code: Int,
    val result: String,
    val exception: Exception? = null,
) {
    fun isOK() = code == 0
}