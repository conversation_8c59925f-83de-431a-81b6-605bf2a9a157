package com.yupao.wxcollect.util

import com.yupao.share.cache.db.kv.KvDbHelper

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/11/011</p>
 *
 * <AUTHOR>
 */
object KVUtil {

    private const val KEY_KEEP_ALIVE = "keep_alive"

    private const val KEY_UPLOAD_SOURCE_DIR = "upload_source_dir"

    private const val KEY_UPLOAD_COMPRESS_DIR = "upload_compress_dir"

    suspend fun isOpenKeepAlive(): Boolean {
        return KvDbHelper.get(KEY_KEEP_ALIVE)?.value?.toBoolean() ?: true
    }

    suspend fun setKeepAlive(isCheck: Boolean) {
        KvDbHelper.save(KEY_KEEP_ALIVE, isCheck.toString())
    }

    suspend fun getUploadSourceDir(): String {
        return KvDbHelper.get(KEY_UPLOAD_SOURCE_DIR)?.value ?: ""
    }

    suspend fun setUploadSourceDir(dir: String) {
        KvDbHelper.save(KEY_UPLOAD_SOURCE_DIR, dir)
    }

    suspend fun getUploadCompressDir(): String {
        return KvDbHelper.get(KEY_UPLOAD_COMPRESS_DIR)?.value ?: ""
    }

    suspend fun setUploadCompressDir(dir: String) {
        KvDbHelper.save(KEY_UPLOAD_COMPRESS_DIR, dir)
    }
}