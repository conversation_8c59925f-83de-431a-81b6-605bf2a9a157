package com.yupao.wxcollect.util

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkInfo

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 15:45
 * @Description
 */
object NetworkUtil {

    /**
     * @return 网络是否连接可用
     */
    @SuppressLint("MissingPermission")
    fun isNetworkConnected(context: Context?): Boolean {
        val connManager = context
            ?.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager ?: return false
        val networkinfo: NetworkInfo? = connManager.activeNetworkInfo
        return networkinfo?.isConnected ?: false
    }

    fun setAirplaneMode(isOpen: Boolean?) {
        kotlin.runCatching {
            CmdUtil.executeAsync("settings put global airplane_mode_on ${if (isOpen == true) 1 else 0}") { result ->
                if (result.isOK()) {
                    CmdUtil.executeAsync("am broadcast -a android.intent.action.AIRPLANE_MODE --ez state ${isOpen == true}")
                }
            }
        }
    }
}