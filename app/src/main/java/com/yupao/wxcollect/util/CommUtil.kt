package com.yupao.wxcollect.util

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.os.Process
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.service.db.Ignore
import java.util.UUID
import kotlin.reflect.KClass

/**
 * <AUTHOR>
 * @Date 2023/4/11/011 19:52
 * @Description
 */
object CommUtil {

    fun getUUID() = kotlin.run {
        return@run UUID.randomUUID().toString().replace("-", "")
    }

    /**
     * 获取进程名称
     *
     * @return
     */
    fun getProcessName(context: Context = AndroidSystemUtil.getContext()) : String? {
        if (Build.VERSION.SDK_INT >= 33) {
            return Process.myProcessName()
        } else {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
            val mutableList:List<ActivityManager.RunningAppProcessInfo>? = activityManager?.runningAppProcesses
            val myPid = Process.myPid()
            mutableList?.forEach {
                if (it.pid == myPid)
                    return it.processName
            }
        }
        return null
    }

    /**
     * 将实体类字段名解析成Set
     *
     * @param entity
     * @return
     */
    fun parseFieldName(entity: KClass<*>): Set<String> {
        val set = mutableSetOf<String>()
        entity.java.declaredFields.forEach {
            it.isAccessible = true
            val ignoreAnnot = it?.annotations?.find { annotation ->
                annotation is Ignore
            }
            if (ignoreAnnot == null) {
                set.add(it.name)
            }
        }
        return set
    }
}