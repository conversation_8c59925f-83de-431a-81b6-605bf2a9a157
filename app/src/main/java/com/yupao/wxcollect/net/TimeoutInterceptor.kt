package com.yupao.wxcollect.net

import okhttp3.Interceptor
import okhttp3.Response
import java.util.concurrent.TimeUnit

/**
 * 自定义超时拦截器
 *
 * <p>创建时间：2025/2/25/025</p>
 *
 * <AUTHOR>
 */
class TimeoutInterceptor: Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val timeout = HeaderUtils.getInt(request, HttpConstant.Key.Timeout)
        val connectTimeout = HeaderUtils.getInt(request, HttpConstant.Key.ConnectTimeout) ?: timeout ?: 0
        val writeTimeout = HeaderUtils.getInt(request, HttpConstant.Key.WriteTimeout) ?: timeout ?: 0
        val readTimeout = HeaderUtils.getInt(request, HttpConstant.Key.ReadTimeout) ?: timeout ?: 0
        if (connectTimeout <= 0 && writeTimeout <= 0 && readTimeout <= 0) {
            return chain.proceed(request)
        }
        var newChain = if (connectTimeout > 0) {
            chain.withConnectTimeout(connectTimeout, TimeUnit.SECONDS)
        } else chain
        if (writeTimeout > 0) {
            newChain = newChain.withReadTimeout(writeTimeout, TimeUnit.SECONDS)
        }
        if (readTimeout > 0) {
            newChain = newChain.withWriteTimeout(readTimeout, TimeUnit.SECONDS)
        }
        return newChain.proceed(request)
    }
}