package com.yupao.wxcollect.net

import com.yupao.net.core.interceptor.LoggerInterceptor
import com.yupao.net.media.UnChangeClient
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.constant.DEV_URL
import com.yupao.ylog.YLog
import okhttp3.Interceptor

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/10/24/024</p>
 *
 * <AUTHOR>
 */
object AppBusinessClient: UnChangeClient() {

    override fun getBaseUrl(): String {
        val baseUrl = AppConstant.URL.baseUrl()
        YLog.i("AppBusinessClient", "baseUrl: $baseUrl")
        if (baseUrl == DEV_URL) {
            return DEV_URL
        }
        return baseUrl
    }

    override fun getInterceptorList(): List<Interceptor> {
        val cls = Class.forName("com.yupao.net.media.BusinessHeaderInterceptor")
        return listOfNotNull(cls.newInstance() as? Interceptor, LoggerInterceptor())
    }
}