package com.yupao.wxcollect.net

import com.google.common.hash.Hashing
import com.yupao.wxcollect.constant.DeviceConfig
import com.yupao.ylog.YLog
import okhttp3.Interceptor
import okhttp3.Response
import java.util.Random


/**
 * 全局header
 *
 * <p>创建时间：2024/12/16/016</p>
 *
 * <AUTHOR>
 */
class HttpHeaderInterceptor: Interceptor {

    private fun createNonce(): Int {
        return (Random().nextInt(999998) + 1)
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        kotlin.runCatching {
            //判断header,取静态变量
            val builder = request.newBuilder()
            val isWxHeader = HeaderUtils.isWxHeader(request)
            if(!isWxHeader){
                val header = mapOf(
                    "platform" to "android",
                    "timestamp" to (request.header("timestamp") ?: System.currentTimeMillis().toString()),
                    "nonce" to (request.header("nonce") ?: createNonce().toString()),
                )
                val sign = sign(header, "3d36b50c4761cdb2c9bc9f1192ab41bd")
                header.forEach { (key, value) ->
                    builder.addHeader(key, value)
                }
                builder.addHeader("edisign", sign ?: "")
            }else{
                builder.addHeader("User-Agent", DeviceConfig.wxHeaderUserAgent?:"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Mobile Safari/537.36")
            }
             val newRequest = builder
                .method(request.method, request.body)
                .build()
            return chain.proceed(newRequest)
        }.getOrElse {
            YLog.printException("HttpHeaderInterceptor", it, "header-intercept")
            throw it
        }
    }

    private fun sign(parameter: Map<String, String?>, signSecret: String?): String? {
        val sb = map2Str(parameter)
        sb.append(signSecret)
        val signStr = sb.toString()
        return Hashing.sha256().newHasher().putString(signStr, Charsets.UTF_8).hash().toString()
    }

    private fun map2Str(data: Map<String, String?>): StringBuilder {
        val keySet = data.keys
        val keyArray = keySet.toTypedArray().sortedArray()
        val sb = StringBuilder()
        val var5 = keyArray.size
        for (var6 in 0 until var5) {
            val k = keyArray[var6]
            val v = data[k]
            val vStr: String = v ?: ""
            sb.append(k).append("=").append(vStr).append("&")
        }
        return sb
    }
}