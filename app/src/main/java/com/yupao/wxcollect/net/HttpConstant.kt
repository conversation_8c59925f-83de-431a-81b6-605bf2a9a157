package com.yupao.wxcollect.net

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2025/2/25/025</p>
 *
 * <AUTHOR>
 */
object HttpConstant {

    object Key {
        /**
         * 超时时间
         */
        const val Timeout = "system:timeout"

        /**
         * 连接超时时间, 为空则取[Timeout]的值
         */
        const val ConnectTimeout = "system:timeout:connect"

        /**
         * 读取超时时间, 为空则取[Timeout]的值
         */
        const val ReadTimeout = "system:timeout:read"

        /**
         * 写入超时时间, 为空则取[Timeout]的值
         */
        const val WriteTimeout = "system:timeout:write"

        /**
         * 是否是微信公众号请求
         */
        const val IS_WX_HEADER = "isWxHeader"
    }

}