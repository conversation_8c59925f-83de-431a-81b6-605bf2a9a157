package com.yupao.wxcollect.net

import okhttp3.Request

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2025/2/25/025</p>
 *
 * <AUTHOR>
 */
object HeaderUtils {

    fun getInt(request: Request, key: String): Int? {
        return kotlin.runCatching {
            request.headers[key]?.toIntOrNull()
        }.getOrNull()
    }

    fun isWxHeader(request: Request): Bo<PERSON>an {
        return kotlin.runCatching {
            request.headers[HttpConstant.Key.IS_WX_HEADER]?.toBoolean()
        }.getOrNull() ?: false
    }

}