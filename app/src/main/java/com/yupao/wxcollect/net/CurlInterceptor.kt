package com.yupao.wxcollect.net

import com.yupao.net.core.interceptor.curl.CurlBuilder
import com.yupao.net.core.interceptor.curl.Options
import com.yupao.wxcollect.App
import com.yupao.wxcollect.util.LogUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import okhttp3.Interceptor
import okhttp3.MultipartBody
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/4/24/024 11:30
 * @Description
 */
class CurlInterceptor(
    private val isSaveToFile: Boolean = true
) : Interceptor {

    companion object {
        private const val DEFAULT_LIMIT = 1024L * 1024L * 10
        private const val DEFAULT_DELIMITER = " "
        private const val JSON_TAG = "application/json"

        private val cacheDir = "${App.getContext()?.getExternalFilesDir(null)?.absolutePath}/log/net-error/"

        fun clearCacheDir() {
            kotlin.runCatching {
                val file = File(cacheDir)
                LogUtil.deleteFileSync(file)
            }
        }
    }

    private val fileDir by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        val path = cacheDir
        val file = File(path)
        if (!file.exists()) {
            file.mkdirs()
        }
        file
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        try {
            val response = chain.proceed(request)
            kotlin.runCatching {
                checkResp(request, response)
            }
            return response
        } catch (e: Exception) {
            // 请求失败的时候写入curl
            outputCurl(request)
            throw e
        }
    }

    private fun outputCurl(request: Request) {
        val body = request.body
        if (body !is MultipartBody) {
            kotlin.runCatching {
                val curl = getCurlBuilder(request).build()
                output(request.url.encodedPath, curl)
            }.exceptionOrNull()?.printStackTrace()
        }
    }

    private fun checkResp(request: Request, response: Response) {
        val contentType = response.header("Content-Type")
        val responseBody = response.peekBody((1024 * 1024).toLong())
        val resp = if (contentType != null && contentType.contains("image")) {
            "Content-Type: $contentType"
            return
        } else {
            responseBody.string()
        }
        kotlin.runCatching {
            val json = JSONObject(resp)
            // php api
            json.optJSONObject("head")?.let {
                val code = it.optString("code")
                if (code == "200") {
                    return
                }
            }
            // java api
            if (json.optString("code") == "0") {
                return
            }
            outputCurl(request)
        }
    }

    private fun getCurlBuilder(copy: Request?): CurlBuilder {
        return CurlBuilder(copy, DEFAULT_LIMIT, arrayListOf(), Options.EMPTY, DEFAULT_DELIMITER)
    }

    private fun output(path: String, curl: String) {
        if (isSaveToFile) {
            val fileName = (TimeUtil.simpleFormatCurrTimeWithMs() + path).replace(Regex("\\W"), "_")
            val file = File(fileDir, fileName)
            YLog.i("CurlInterceptor", "output: path = $path save to file: ${file.absolutePath}")
            file.createNewFile()
            file.writeText(curl)
        }
    }
}