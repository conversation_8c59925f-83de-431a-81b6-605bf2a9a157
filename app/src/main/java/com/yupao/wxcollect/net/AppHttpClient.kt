package com.yupao.wxcollect.net

import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.net.IClientService
import com.yupao.net.core.interceptor.GlobalInterceptor
import com.yupao.net.utils.SSLSocketClient
import com.yupao.wxcollect.constant.*
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 17:11
 * @Description
 */
class AppHttpClient(
    val baseUrl: String,
    private val timeout: Long = 40,
    private val isAddCurlInterceptor: Boolean = true,
    ): IClientService {
    companion object {
        val instanceForC = AppHttpClient(AppConstant.URL.baseUrl())

        val instanceForB by lazy {
            when (AppConstant.URL.baseUrl()) {
                DEV_URL ->  AppHttpClient(DEV_URL_B)
                TEST_URL ->  AppHttpClient(TEST_URL_B)
                MASTER_URL ->  AppHttpClient(MASTER_URL_B)
                PROD_URL ->  AppHttpClient(PROD_URL_B)
                else -> AppHttpClient(PROD_URL_B)
            }
        }

        val instanceShortTimeout by lazy {
            when (AppConstant.URL.baseUrl()) {
                DEV_URL ->  AppHttpClient(DEV_URL_B, 10, false)
                TEST_URL ->  AppHttpClient(TEST_URL_B, 10, false)
                MASTER_URL ->  AppHttpClient(MASTER_URL_B, 10, false)
                PROD_URL ->  AppHttpClient(PROD_URL_B, 10, false)
                else -> AppHttpClient(PROD_URL_B, 10, false)
            }
        }
    }

    private val serviceCache: MutableMap<String, Any> = mutableMapOf()

    private val isRetrofitReset: AtomicBoolean = AtomicBoolean(false)

    val okHttpClient by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        val builder = OkHttpClient.Builder()
            .addInterceptor(GlobalInterceptor())
            .addInterceptor(HttpHeaderInterceptor())
            .addInterceptor(TimeoutInterceptor())
            .connectTimeout(timeout, TimeUnit.SECONDS)
            .readTimeout(timeout, TimeUnit.SECONDS)
            .writeTimeout(timeout, TimeUnit.SECONDS)
        if (isAddCurlInterceptor) {
            builder.addInterceptor(CurlInterceptor())
        }
        if (BuildConfig.DEBUG) {
            builder.addInterceptor(LogInterceptor())
//            builder.eventListener(HttpEventListener("/backend/collect/wechat/uploadChatHistory"))
        }
        builder.sslSocketFactory(
            SSLSocketClient.sSLSocketFactory,
            SSLSocketClient.trustManager[0]
        )
        return@lazy builder.build()
    }

    /**
     * retrofit示例
     * 为了对API地址更换时不需要重新启动提供支持，所以这里把retrofit示例设置为null
     */
    @Volatile
    private var retrofit: Retrofit? = null

    /**
     * 创建ApiService实例
     * @param service 类型
     * @return ApiService实例
     */
    override fun <T> createApiService(service: Class<T>): T {
        assertRetrofitNoNull()
        if (serviceCache.containsKey(service.name)) return serviceCache[service.name] as T
        val serviceImpl: T = retrofit!!.create(service)
        serviceCache[service.name] = serviceImpl!!
        return serviceImpl
    }

    /**
     * 重置retrofit示例
     * 更新地址
     */
    override fun reset() {
        if (isRetrofitReset.compareAndSet(false, true)) {
            serviceCache.clear()
            this.retrofit = null
            isRetrofitReset.getAndSet(false)
        }
    }

    /**
     * 创建retrofit示例
     */
    private fun assertRetrofitNoNull() {
        if (retrofit == null) {
            retrofit = synchronized(lock = this, block = {
                return@synchronized if (retrofit == null) {
                    Retrofit.Builder()
                        .baseUrl(baseUrl)
                        .client(okHttpClient)
                        .addConverterFactory(GsonConverterFactory.create())
                        .build()
                } else {
                    retrofit
                }
            })
        }
    }
}

/**
 * 忽略字段，查询数据库的时候会忽略该注解标识的字段
 */
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.FIELD)
annotation class IgnoreField