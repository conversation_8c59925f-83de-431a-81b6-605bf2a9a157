package com.yupao.wxcollect.service.accessibility.filter

import android.view.accessibility.AccessibilityEvent

/**
 * <AUTHOR>
 * @Date 2023/4/23/023 16:14
 * @Description
 */
interface IPageHandler: IHandlerStatus {

    companion object {
        const val DEFAULT_PRIORITY = -1
    }

    val page: List<String>?

    // 最大调用次数
    val maxPerformCount: Int

    val viewHandlerList: MutableList<IViewHandler>?

    var priority: Int

    suspend fun acceptEventType(event: AccessibilityEvent?): Boolean

    suspend fun filter(event: AccessibilityEvent?): Boolean

    suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean

    fun isRemove(): <PERSON><PERSON><PERSON>
}