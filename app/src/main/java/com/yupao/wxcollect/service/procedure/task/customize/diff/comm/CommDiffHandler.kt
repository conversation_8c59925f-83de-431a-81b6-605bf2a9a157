package com.yupao.wxcollect.service.procedure.task.customize.diff.comm

import com.google.gson.Gson
import com.yupao.wxcollect.service.procedure.entity.db.CommDiffLocalModel
import com.yupao.wxcollect.service.procedure.task.StepDurationTrace
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffEntity
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffResultEntity
import com.yupao.wxcollect.service.procedure.task.customize.diff.IDiffHandler
import com.yupao.wxcollect.service.procedure.task.customize.diff.TaskDiffHelper
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/9/009</p>
 *
 * <AUTHOR>
 */
class CommDiffHandler<T: CommDiffLocalModel>(
    private val appType: String,
    private val taskCode: String,
    private val provider: ICommProvider<T>,
): IDiffHandler {

    private val TAG = "Diff:${taskCode}"

    private val dbHelper by lazy { CommDbHelper(appType, taskCode, provider) }

    private val diffHelper by lazy { TaskDiffHelper() }

    private val durationTrace = StepDurationTrace(TAG)

    private val gson by lazy { Gson() }

    private var lastId = -1

    companion object {
        // 额外多余查询的数据，可以有效减少因为数据移除导致上报了未修改的数据
        private const val EXTRA_QUERY_COUNT = 10
    }

    override suspend fun transform(
        entity: DiffEntity
    ): DiffResultEntity {
        durationTrace.reset()
        val size = entity.list.size
        val prevVersion = entity.prevVersion
        YLog.i(TAG, "transform start, size: $size, prevVersion: $prevVersion")
        val modelList = dbHelper.queryLimit(size + EXTRA_QUERY_COUNT, lastId)
        YLog.i(TAG, "transform, local query size: ${modelList.size}")
        lastId = modelList.getOrNull(size - 1)?.id ?: -1
        durationTrace.trace()
        // 新增列表
        val newList = mutableListOf<Map<String, Any?>>()
        // 修改列表
        val modifyList = mutableListOf<Map<String, Any?>>()
        // 上一次未成功同步的数据
        val leakList = mutableListOf<Map<String, Any?>>()
        // 待插入列表
        val insertList = mutableListOf<T>()
        val currTime = entity.currTime
        val newVersion = dbHelper.newVersion
        durationTrace.trace()
        entity.list.forEachIndexed { index, map ->
            val uniqueValue = map[provider.uniqueKey]?.toString() ?: ""
            // 新增或移除是小概率事件，优先尝试通过index查询，可以大幅提高命中率，减少遍历
            val localModel = modelList.getOrNull(index)?.takeIf { model ->
                model.uniqueId == uniqueValue
            } ?: modelList.find { model ->
                model.uniqueId == uniqueValue
            }
            val hashCode = diffHelper.uniqueCode(map, provider.uniqueIgnoreKey)
            val element = toLocalModel(
                null,
                uniqueValue,
                hashCode,
                map,
                currTime,
                newVersion
            )
            if (localModel != null) {
                if (hashCode != localModel.uniqueCode) {
                    modifyList.add(map)
                } else {
                    // 比要求上报的版本大，说明没有成功上报给后端，需要补报
                    if ((localModel.version ?: 0) > prevVersion) {
                        leakList.add(map)
                    }
                    // 该数据在本地未做任何修改
                    element.version = localModel.version
                    element.updateTime = localModel.updateTime
                }
            } else {
                newList.add(map)
            }
            insertList.add(element)
        }
        durationTrace.trace()
        dbHelper.insertAll(insertList)
        durationTrace.trace()
        YLog.i(TAG, "transform result: version: ${newVersion}, newSize: ${newList.size}, " +
                "updateSize: ${modifyList.size}, leakSize: ${leakList.size}")
        // 注意顺序不能交换，以新提交的数据为准
        val resultList = (newList + modifyList + leakList)
        durationTrace.trace()
        return DiffResultEntity(
            version = newVersion,
            list = resultList
        )
    }

    override suspend fun dispatchFinished() {
        dbHelper.switchDatabase()
    }

    private fun toLocalModel(
        id: Int?,
        uniqueValue: String,
        uniqueCode: Int,
        map: Map<String, Any?>,
        updateTime: Long,
        version: Int,
    ): T {
        return this.provider.createLocalModel(
            id,
            uniqueValue,
            uniqueCode,
            map,
            updateTime,
            version
        )
    }
}