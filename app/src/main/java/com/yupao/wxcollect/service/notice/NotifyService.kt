package com.yupao.wxcollect.service.notice

import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import com.yupao.wxcollect.service.ReportService
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/7/15/015 17:49
 * @Description 监听微信通知
 */
class NotifyService: NotificationListenerService() {
    private val TAG = "NotifyService"

    override fun onNotificationRemoved(sbn: StatusBarNotification?) {
        Log.d(TAG, "onNotificationRemoved: sbn = $sbn")
    }

    private var lastRecordTime = 0L

    override fun onNotificationPosted(sbn: StatusBarNotification, rankingMap: RankingMap?) {
        val currTime = TimeUtil.getCurrTime()
        if(currTime - lastRecordTime > 60_000){
            // 限制频率，超过一分钟才会打印
            lastRecordTime = currTime
            YLog.i(TAG, "onNotificationPosted sbn = $sbn")
        }
    }

}