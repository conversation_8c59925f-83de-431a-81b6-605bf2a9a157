package com.yupao.wxcollect.service.procedure.entity.director

import com.google.gson.Gson
import org.json.JSONObject

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/21/021</p>
 *
 * <AUTHOR>
 */


object TaskStatusTypeAdapter {
    private const val KEY_TYPE = "type"

    private const val KEY_DATA = "data"

    private val gson by lazy { Gson() }

    private val subClasses = TaskStatus::class.java.declaredClasses

    fun deserialize(source: String?): TaskStatus {
        source ?: return TaskStatus.Unknown()
        return kotlin.runCatching {
            val jsonObject = JSONObject(source)
            val type = jsonObject.optString(KEY_TYPE)
            val data = jsonObject.optString(KEY_DATA)
            subClasses.find {
                it.simpleName == type
            }?.let {
                gson.fromJson(data, it) as? TaskStatus
            }
        }.getOrNull() ?: TaskStatus.Unknown()
    }

    fun serialize(taskStatus: ITaskStatus): String {
        val type = taskStatus.javaClass.simpleName
        val data = gson.toJson(taskStatus)
        return JSONObject().apply {
            put(KEY_TYPE, type)
            put(KEY_DATA, data)
        }.toString()
    }
}