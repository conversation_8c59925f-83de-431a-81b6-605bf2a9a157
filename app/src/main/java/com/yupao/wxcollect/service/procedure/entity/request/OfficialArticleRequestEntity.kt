package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep
import kotlinx.android.parcel.IgnoredOnParcel

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/10/23/023</p>
 *
 * <AUTHOR>
 */
@Keep
data class OfficialArticleRequestEntity(
    val officialAccountName: String?,
    val officialAccountOriginId: String?,
    val wechatOriginId: String?,
    val phoneNum: String?,
    val articleUnique: String?,
    val articleContentOssUrl: String?,
    val articleCreatedAt: String?,
    @IgnoredOnParcel
    val articleUrl: String?,
    @IgnoredOnParcel
    val failedMsg: String?,
)

@Keep
data class OfficialArticleRequestData(
    val paramOssUrl: String?,
)