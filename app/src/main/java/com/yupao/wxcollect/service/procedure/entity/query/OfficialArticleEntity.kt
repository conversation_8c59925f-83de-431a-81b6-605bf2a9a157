package com.yupao.wxcollect.service.procedure.entity.query

import android.net.Uri
import androidx.annotation.Keep

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/10/20/020</p>
 *
 * <AUTHOR>
 */
@Keep
data class OfficialArticleEntity(
    // 聊天内容
    val content: String? = null,
    // 创建时间
    val createTime: String? = null,
    // 群ID
    val talker: String? = null,
    // 公众号名称
    val nickname: String? = null,
) {

    /**
     * 文章列表
     */
    fun getArticleList(): List<ArticleEntity> {
        val list = getContentUrl()
        return list.map {
            ArticleEntity(
                createTime = createTime,
                url = it,
                articleUnique = getArticleUnique(it),
                talker = talker,
                nickname = nickname
            )
        }
    }

    /**
     * 获取内容Url
     *
     * @return
     */
    private fun getContentUrl(): Set<String> {
        content ?: return emptySet()
        val startFlag = "http://mp.weixin.qq.com/s"
        val endFlag = "#rd"
        val list = parseUrl(content, startFlag, endFlag)
        val startFlag2 = "https://mp.weixin.qq.com/s"
        val list2 = parseUrl(content, startFlag2, endFlag)
        return mutableSetOf<String>().apply {
            addAll(list)
            addAll(list2)
        }
    }

    private fun parseUrl(
        content: String,
        startFlag: String,
        endFlag: String
    ): MutableList<String> {
        var startIndex = 0
        val list = mutableListOf<String>()
        while (startIndex < content.length) {
            val start = content.indexOf(startFlag, startIndex)
            if (start < 0) {
                break
            }
            val end = content.indexOf(endFlag, start)
            if (start in 0 until end) {
                val url = content.substring(start, end + endFlag.length)
                list.add(url)
            } else {
                break
            }
            startIndex = (start + startFlag.length).coerceAtLeast(end + endFlag.length)
        }
        return list
    }


    /**
     * 获取文章唯一标识
     *
     * @return
     */
    private fun getArticleUnique(contentUrl: String?): String? {
        if (contentUrl.isNullOrBlank()) {
            return null
        }
        return kotlin.runCatching {
            Uri.parse(contentUrl).getQueryParameter("sn")
        }.getOrNull()
    }

}

@Keep
data class ArticleEntity(
    // 创建时间
    val createTime: String?,
    // 公众号名称
    val nickname: String?,
    // 公众号文章地址
    val url: String?,
    // 公众号文章唯一标识
    val articleUnique: String?,
    // 群ID
    val talker: String?,
)