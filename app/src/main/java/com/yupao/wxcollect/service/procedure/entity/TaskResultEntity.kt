package com.yupao.wxcollect.service.procedure.entity

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.entity.TaskResultEntity.Companion.CODE_ARGUMENT_EMPTY
import com.yupao.wxcollect.service.procedure.task.TaskType

/**
 * <AUTHOR>
 * @Date 2023/4/6/006 9:46
 * @Description 任务执行结果
 */
@Keep
data class TaskResultEntity(
    @TaskType val taskType: Int?,
    val code: Int?,
    val data: ReportResultEntity? = null,
    val msg: String? = null,
    var appType: String? = null,
) {
    companion object {
        // 配置为空
        const val CODE_CONFIG_EMPTY = -1

        const val CODE_NO_NETWORK = -2

        const val CODE_REQUEST_FAILED = -3

        const val CODE_EXCEPTION = -4

        const val CODE_ARGUMENT_EMPTY = -5

        const val CODE_FAILED = -6

        const val CODE_SUCCEED = 0

        const val CODE_DATA_EMPTY = 1
    }

    fun isOk(): Boolean {
        return (code ?: CODE_ARGUMENT_EMPTY) >= CODE_SUCCEED
    }
}

@Keep
data class SimpleResultEntity(
    override val code: Int,
    override val msg: String? = null,
    override val count: Int? = null,
): ITaskResult {
    companion object {
        fun succeed(count: Int) = SimpleResultEntity(ITaskResult.CODE_SUCCEED, count = count)

        fun failed(msg: String? = null) = SimpleResultEntity(ITaskResult.CODE_FAILED, msg)

        fun invalidArgs(msg: String? = null) = SimpleResultEntity(ITaskResult.CODE_ARGUMENT_EMPTY, msg)

        fun exception(msg: String? = null) = SimpleResultEntity(ITaskResult.CODE_EXCEPTION, msg)

        fun canceled(msg: String? = null) = SimpleResultEntity(ITaskResult.CODE_CANCEL, msg)

        fun config(msg: String? = null) = SimpleResultEntity(ITaskResult.CODE_CONFIG, msg)

        fun waitRetry(msg: String? = null) = SimpleResultEntity(ITaskResult.CODE_WAIT_RETRY, msg)
    }
}