package com.yupao.wxcollect.service.accessibility.filter

import android.graphics.Rect
import android.os.SystemClock
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.yupao.wxcollect.service.accessibility.filter.IViewHandler.Companion.DEFAULT_DELAY_TIME
import com.yupao.wxcollect.service.accessibility.filter.IViewHandler.Companion.INVALID_TIME
import com.yupao.wxcollect.service.accessibility.process.AutoSwitchAccountTask
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/21/021 14:54
 * @Description 点击View
 */
open class ClickViewHandler(
    // 查找的文本
    private val viewText: String? = null,
    // 第几个，-1表示最后一个
    private val index: Int = 0,
    // 延迟执行click时间
    private val delayTime: Long = DEFAULT_DELAY_TIME,
    // 延迟分发时间
    private val delayDispatchTime: Long = INVALID_TIME,
    // 最大调用次数
    override val maxPerformCount: Int = 1,
    // 兄弟过滤器
    private val brotherFilter: AccessibilityNodeInfoFilter? = null,
    // 上一个ViewHandler执行之后多久开始直接执行本ClickViewHandler
    override val delayTimeAfterPrev: Long = IViewHandler.INVALID_TIME,
    override var pageHandler: IPageHandler? = null,
    private val eventType: Int = AutoSwitchAccountTask.WINDOW_STATUS,
    // 点击View Y轴便宜
    private val clickViewOffsetY: Int = 0,
    // 任务执行之后的后续任务
    private val afterTask: (suspend () -> Unit)? = null,
    // 拦截器，返回true会禁用
    open val iterator: (suspend (AccessibilityEvent) -> Boolean)? = null,
): IViewHandler {
    override var status: Int = HandlerStatus.IDLE

    private var performCount: Int = 0

    override suspend fun acceptEventType(event: AccessibilityEvent?): Boolean {
        val type = event?.eventType ?: 0
        return type and eventType == type
    }

    open fun findSourceByEvent(event: AccessibilityEvent?) = event?.source

    private var isFirstDelay = true

    override suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean {
        viewText ?: return false
        event ?: return false
        val accept = acceptEventType(event)
        if (!accept) {
            YLog.i("ClickViewHandler", "dispatchEvent: not support eventType: ${event.eventType}")
            return false
        }
        status = HandlerStatus.DISPATCH
        var source = findSourceByEvent(event)
        YLog.i("ClickViewHandler", "dispatchEvent: event = $event, " +
                "viewText = $viewText, delayDispatchTime = $delayDispatchTime, source = $source")
        if (isFirstDelay && delayDispatchTime != INVALID_TIME) {
            isFirstDelay = false
            SystemClock.sleep(delayDispatchTime)
            if (source == null) {
                source = findSourceByEvent(event)
            }
        }
        val result = source?.findAccessibilityNodeInfosByText(viewText)
            ?.let {
                if (it.isEmpty()) {
                    YLog.i("ClickViewHandler", "not found node, viewText = $viewText, index = $index")
                } else if (it.size > 1) {
                    YLog.i("ClickViewHandler", "find node count: ${it.size}, index: $index")
                }
                val info = AccessibilityNodeInfoUtil.findAccessibilityNodeByIndex(index, it)
                val result = AccessibilityNodeInfoUtil.findAccessibilityNodeByBrotherIndex(info, brotherFilter)
                YLog.i("ClickViewHandler", "dispatchEvent: info = $info")
                YLog.i("ClickViewHandler", "dispatchEvent: brother = $result")
                return@let result
            }
            ?.let {
                status = HandlerStatus.WAIT_RUNNING
                if (iterator?.invoke(event) != true) {
                    YLog.i("ClickViewHandler", "performAction: nodeInfo = $this")
                    performCount++
                    SystemClock.sleep(delayTime)
                    YLog.i("ClickViewHandler", "performAction: performCount = $performCount")
                    status = HandlerStatus.RUNNING
                    var result = it.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    if (!result) {
                        result = performClick(it)
                    }
                    YLog.i("ClickViewHandler", "performAction: result = $result, viewText = $viewText, performCount = $performCount")
                    result
                } else false
            }
        status = if (result == true) {
            afterTask?.invoke()
            HandlerStatus.SUCCEED
        } else {
            YLog.i("ClickViewHandler", "dispatchEvent: result = $source")
            if (result == null) {
                AccessibilityNodeInfoUtil.printNodeInfo(source)
            }
            HandlerStatus.FAILED
        }
        YLog.i("ClickViewHandler", "dispatchEvent: result = $result")
        return result  == true
    }

    private fun performClick(nodeInfo: AccessibilityNodeInfo): Boolean {
        val bound = Rect()
        nodeInfo.getBoundsInScreen(bound)
        val x = (bound.left + bound.right) / 2
        val y = (bound.top + bound.bottom) / 2 + clickViewOffsetY
        val cmd = "input tap $x $y"
        val result = CmdUtil.execCmd(cmd)
        return result == null
    }

    override fun isRemove(): Boolean {
        return performCount >= maxPerformCount
    }

    override fun getWillPerformCount(): Int {
        return performCount
    }

    override fun toString(): String {
        return "ClickViewHandler(viewText=$viewText, index=$index, maxPerformCount=$maxPerformCount, performCount=$performCount)"
    }
}