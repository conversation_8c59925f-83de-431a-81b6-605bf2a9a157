package com.yupao.wxcollect.service

import com.yupao.wxcollect.util.CmdUtil
import com.yupao.wxcollect.util.SpUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/7/5/005 13:56
 * @Description 充电开关控制
 */
object BatterySwitchManager {
    private const val key = "battery_switch"

    // 电量低于该值，运行充电
    private const val RECHARGE_ENABLE = 20

    // 电量超过该值，禁止充电
    private const val RECHARGE_DISABLE = 100

    private var isCtrl: Boolean? = null

    fun isCtrl(): Boolean {
        // 默认开启控制
        return this.isCtrl ?: SpUtil.getString(key)?.toBoolean() ?: true
    }

    fun save(isCtrl: Boolean) {
        this.isCtrl = isCtrl
        SpUtil.putString(key, isCtrl.toString())
        CoroutinePool.other.launch {
            if (isCtrl) {
                checkBattery()
            } else {
                setupBatterySwitch(true)
            }
        }
    }

    private fun checkBattery() {
        kotlin.runCatching {
            val list = CmdUtil.execCmdWithBack("dumpsys battery | grep level")
            val level = getBatteryValue(list, "level")?.trim()?.toInt()
            YLog.i("BatterySwitchManager", "checkBattery: level = $level")
            level ?: return
            if (level >= RECHARGE_DISABLE) {
                setupBatterySwitch(false)
                YLog.i("BatterySwitchManager", "close charge")
            } else if (level <= RECHARGE_ENABLE) {
                setupBatterySwitch(true)
                YLog.i("BatterySwitchManager", "open charge")
            }
        }.exceptionOrNull()?.printStackTrace()
    }

    private fun getBatteryValue(list: List<String>, key: String): String? {
        if (list.isEmpty()) {
            return null
        }
        list.forEach {
            val split = it.trim().split(":")
            if (split.first() == key && split.size > 1) {
                return split.getOrNull(1)
            }
        }
        return null
    }

    private fun setupBatterySwitch(isAllowCharge: Boolean) {
        if (isAllowCharge) {
            CmdUtil.executeAsync("echo 0 > /sys/class/power_supply/battery/input_suspend")
        } else {
            CmdUtil.executeAsync("echo 1 > /sys/class/power_supply/battery/input_suspend")
        }
    }
}