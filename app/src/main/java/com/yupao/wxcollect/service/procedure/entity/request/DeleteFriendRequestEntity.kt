package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/6/21/021 9:13
 * @Description
 */
@Keep
data class DeleteFriendRequestEntity(
    val waiterAccount: String?,
    val customers: List<DeleteFriendEntity>?,
)

@Keep
data class DeleteFriendEntity(
    // 被删除id
    val customerAccount: String?,
    // 删除时间
    val time: String?,
)