package com.yupao.wxcollect.service.procedure.task.customize.diff.comm

import com.yupao.wxcollect.service.db.IDiffDao
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.procedure.entity.db.CommDiffLocalModel
import com.yupao.wxcollect.service.procedure.task.customize.diff.inf.IDbHelper
import com.yupao.ylog.YLog
import kotlinx.coroutines.runBlocking

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/9/009</p>
 *
 * <AUTHOR>
 */
class CommDbHelper<T: CommDiffLocalModel>(
    private val appType: String,
    private val taskCode: String,
    private val provider: ICommProvider<T>
): IDbHelper {
    private val TAG = "CommDbHelper:${appType}:${taskCode}"

    private fun getDao(appType: String, isBackup: Boolean): IDiffDao<T> {
        return provider.getDao(appType, isBackup)
    }

    override fun clearTable(appType: String, isBackup: Boolean) {
        provider.clearTable(appType, isBackup)
    }

    private val databaseUseKey by lazy {
        "diff_backup-$appType-$taskCode"
    }

    private val dataDiffVersionKey by lazy {
        "data_diff_version-$appType-$taskCode"
    }

    private val dataDiffVersion by lazy {
        runBlocking {
            (KVHelper.get(dataDiffVersionKey)?.toInt() ?: 0).apply {
                YLog.i(TAG, "dataDiffVersion: $this")
            }
        }
    }

    val newVersion by lazy {
        dataDiffVersion + 1
    }

    private val isUseBackup: Boolean by lazy {
        runBlocking {
            (KVHelper.get(databaseUseKey)?.toBoolean() ?: false).apply {
                YLog.i(TAG, "isUseBackup: $this")
            }
        }
    }

    suspend fun switchDatabase() {
        // 切换数据库diff表，并且清空当前数据库diff表
        kotlin.runCatching {
            val useBackup = isUseBackup
            // 清除查询用的数据库所有数据
            getDao(appType, useBackup).clearTable()
            // 切换数据库
            KVHelper.set(databaseUseKey, (!useBackup).toString())
            KVHelper.set(dataDiffVersionKey, newVersion.toString())
            YLog.i(TAG, "switchDatabase isUseBackup: $isUseBackup, newVersion: $newVersion")
        }.onFailure {
            YLog.printException(TAG, it, "switchDatabase")
        }
    }

    suspend fun queryLimit(
        limit: Int,
        lastId: Int,
    ): List<T> {
        return getDao(appType, isUseBackup).queryLimit(limit, lastId)
    }

    suspend fun insertAll(
        insertList: List<T>,
    ) {
        if (insertList.isEmpty()) {
            return
        }
        kotlin.runCatching {
            // 注意此处跟查询是相反的数据库
            getDao(appType, !isUseBackup).insertAll(insertList)
            YLog.i(TAG, "insert size: ${insertList.size}, isUseBackup: $isUseBackup")
        }.onFailure {
            YLog.printException(TAG, it, "insertAll")
        }
    }
}