package com.yupao.wxcollect.service.procedure

import kotlinx.coroutines.delay

/**
 * 重试帮助类
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
class RetryHelper<T>(
    // 最大重试次数
    private val maxRetryCount: Int,
    // 重试延迟策略, 默认立即重试
    private val delayStrategy: IDelayStrategy = FixedDelayStrategy(0),
    // 重试拦截器, 延迟[delayTime]毫秒之后进行第[count]重试
    private val retryInterceptor: (suspend (count: Int, delayTime: Long) -> Boolean)? = null,
) {

    private var retryCount: Int = 0

    private val retryResultList = mutableListOf<Result<T>>()

    /**
     * 执行操作, 如果操作失败, 则进行重试
     */
    suspend fun perform(action: suspend () -> Result<T>): RetryHelper<T> {
        val result = try {
            action.invoke()
        } catch (e: Exception) {
            Result.failure(e)
        }
        retryResultList.add(result)
        if (result.isSuccess) {
            return this
        }
        if (retryCount >= maxRetryCount) {
            return this
        }
        retryCount++
        val delayTime = delayStrategy.delayTime(retryCount)
        val isIntercept = retryInterceptor?.invoke(retryCount, delayTime)
        if (isIntercept == true) {
            return this
        }
        if (delayTime > 0) {
            delay(delayTime)
        }
        return perform(action)
    }

    /**
     * 获取最近一次的结果
     */
    fun getRecently() = retryResultList.lastOrNull()

    /**
     * 获取最近一次成功的结果
     */
    fun getRecentlySuccessful() = retryResultList.lastOrNull {
        it.isSuccess
    }

    /**
     * 获取最近一次失败的结果
     */
    fun getRecentlyFailed() = retryResultList.lastOrNull {
        it.isFailure
    }

    /**
     * 获取所有结果
     */
    fun getAllResult(): List<Result<T>> = retryResultList
}

suspend fun RetryHelper<String>.notEmptyResult(
    errorMsg: String = "empty",
    action: suspend () -> String?
): Result<String>? {
    return this.perform {
        val result = action.invoke()
        if (result.isNullOrEmpty()) {
            Result.failure(Exception(errorMsg))
        } else {
            Result.success(result)
        }
    }.getRecently()
}

suspend fun <T>RetryHelper<T>.notNullResult(
    errorMsg: String = "null",
    action: suspend () -> T?
): Result<T>? {
    return this.perform {
        val result = action.invoke()
        if (result == null) {
            Result.failure(Exception(errorMsg))
        } else {
            Result.success(result)
        }
    }.getRecently()
}

suspend fun RetryHelper<Unit>.emptyResult(
    action: suspend () -> String?
): Result<Unit>? {
    return this.perform {
        val result = action.invoke()
        if (result.isNullOrEmpty()) {
            Result.success(Unit)
        } else {
            Result.failure(RuntimeException(result))
        }
    }.getRecently()
}

suspend fun RetryHelper<Unit>.yesResult(
    errorMsg: String = "failed",
    action: suspend () -> Boolean
): Result<Unit>? {
    return this.perform {
        val result = action.invoke()
        if (result) {
            Result.success(Unit)
        } else {
            Result.failure(RuntimeException(errorMsg))
        }
    }.getRecently()
}

suspend fun <Any>RetryHelper<Any?>.notExceptionResult(
    action: suspend () -> Any?
): Result<Any?>? {
    return this.perform {
        kotlin.runCatching {
            action.invoke()
        }
    }.getRecently()
}

interface IDelayStrategy {
    fun delayTime(count: Int): Long
}

/**
 * 固定增加延迟
 *
 * @property rate：速率
 */
class FixedDelayStrategy(
    private val rate: Int
) : IDelayStrategy {
    override fun delayTime(count: Int): Long {
        return rate * 1L
    }
}

/**
 * 线性增加延迟
 *
 * @property rate：速率
 */
class LinearDelayStrategy(
    private val rate: Int
) : IDelayStrategy {
    override fun delayTime(count: Int): Long {
        return count * rate * 1L
    }
}

/**
 * 指数增加延迟
 *
 * @property rate：速率
 */
class ExponentialDelayStrategy(
    private val rate: Int
) : IDelayStrategy {
    override fun delayTime(count: Int): Long {
        return (rate * 1L) shl (count - 1)
    }
}