package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.ylog.YLog
import java.util.Collections
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 网络控制开关状态观察者
 *
 * <p>创建时间：2024/6/11/011</p>
 *
 * <AUTHOR>
 */
class NetworkStatusChangedObserver constructor(
    private val flag: String,
    private val configSize: Int
): IStatusChangedObserver {


    private val mainNetworkHandler by lazy { NetworkControlHandler(flag, ReportConfig.APP_MAIN) }

    private val subNetworkHandler by lazy { NetworkControlHandler(flag, ReportConfig.APP_SUB) }

    // 拷贝数据库成功的databasePath
    private val copySucceedPath = Collections.synchronizedSet(mutableSetOf<String>())

    private val closeNetwork = AtomicBoolean(false)

    override suspend fun onChanged(config: TaskExecutorConfig, status: TrainExecutorStatus) {
        if (status is IFinishedStatus || status is TrainExecutorStatus.CopyDatabaseSucceed) {
            openNetwork(config.appType, config.databasePath)
        }
        if (status is TrainExecutorStatus.Execute) {
            closeNetwork(config.appType)
            return
        }
    }

    private suspend fun openNetwork(appType: String, databasePath: String) {
        copySucceedPath.add(databasePath)
        val size = copySucceedPath.size
        // 如果数据库都拷贝成功了，则打开网络
        YLog.i(tag = flag, "openNetwork size: $size, configSize: $configSize")
        if (size == configSize) {
            val handler = if (appType == ReportConfig.APP_SUB) subNetworkHandler else mainNetworkHandler
            handler.openNetwork()
        }
    }

    private suspend fun closeNetwork(appType: String) {
        // 是否开启了<确定执行时不关闭微信网络吗>
        val netSwitchEnable = KVHelper.netSwitchEnable()
        YLog.i("${this.flag} closeNetwork netSwitchEnable: $netSwitchEnable")
        if (netSwitchEnable) {
            return
        }
        // 不管主号还是副号，只有有一个关闭了网络，后续都不会再关闭网络了
        val handler = if (appType == ReportConfig.APP_SUB) subNetworkHandler else mainNetworkHandler
        if (!handler.isSupportNetworkCtrl || closeNetwork.get()) {
            return
        }
        handler.closeNetwork()
        closeNetwork.set(true)
    }
}