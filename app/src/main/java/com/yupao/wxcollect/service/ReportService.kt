package com.yupao.wxcollect.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.Messenger
import android.os.PowerManager
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.service.notice.ReportNotificationManager
import com.yupao.wxcollect.service.procedure.AutoInstallManager
import com.yupao.wxcollect.service.procedure.KeepAliveManager
import com.yupao.wxcollect.service.procedure.dispatch.TaskTrainDispatcher
import com.yupao.wxcollect.service.procedure.keepalive.HeartBeatManager
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffHelper
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.util.KVUtil
import com.yupao.wxcollect.util.MemoryUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/6/006 11:02
 * @Description 上报服务
 */
@AndroidEntryPoint
class ReportService: Service() {
    companion object {
        const val KEY_NOTICE_CONFIG = "notice_config"

        const val KEY_FROM = "from"

        const val KEY_SUBMIT_CONFIG = "submit_config"

        private const val TAG = "ReportService"

        private var reportService: ReportService? = null

        // 是否正在运行
        var isRunning = false
            private set
    }

    private val message: Messenger by lazy {
        IReportServiceManager.instance.apply {
            bindService(this@ReportService)
        }.getMessenger()
    }

    private val notificationManager by lazy {
        ReportNotificationManager.instance
    }

    @Inject
    lateinit var reportRepository: ReportRepository

    private val timeReceiver by lazy {
        {
            performWakeup()
        }
    }

    // 是否正在唤醒
    private var isPerformWakeup: Boolean = false

    private fun performWakeup() {
        try {
            if (isPerformWakeup) {
                YLog.i(TAG, "performWakeup")
                return
            }
            isPerformWakeup = true
            val content = "更新时间: ${TimeUtil.simpleFormatCurrTime()}"
            notificationManager.updateNotice(content)
            // 检测电量
//            BatterySwitchManager.check()
            // 静默升级
//            AutoInstallManager.checkVersion()
            KeepAliveManager.wakeup()
            MemoryUtil.checkMemory()
            CoroutinePool.coreTask.launch {
                val isOpen = KVUtil.isOpenKeepAlive()
                com.yupao.keepalive.KeepAliveManager.wakeupDelay(isOpen)
            }
        } finally {
            isPerformWakeup = false
        }
    }

    private fun requestWakeLock() {
        GlobalScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                YLog.i(TAG, "requestWakeLock start")
                val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
                val wakeLock: PowerManager.WakeLock = pm.newWakeLock(
                    PowerManager.PARTIAL_WAKE_LOCK,
                    "MainActivity:mywakelocktag"
                )
                wakeLock.acquire()
                YLog.i(TAG, "requestWakeLock finished")
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return message.binder
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        YLog.i(TAG, "onStartCommand: 启动服务, isRunning = $isRunning")
        val from = intent?.getStringExtra(KEY_FROM)
        YLog.i(TAG, "onStartCommand: from = $from")
        if (!isRunning) {
            createNotification(intent)
            CoreClient.startService()
            KeepAliveManager.perform()
            DiffHelper.clearInvalidDatabase()
        } else {
            CoreClient.checkClientRegister()
        }
        if (!isRunning ||
            from == IReportServiceManager.KEY_FROM_APP_CONFIG ||
            from == IReportServiceManager.KEY_FROM_SETUP
        ) {
            requestWakeLock()
            TaskTrainDispatcher.instance.wakeup()
        }
        HeartBeatManager.addObserver(1, timeReceiver)
        reportService = this
        isRunning = true
        return super.onStartCommand(intent, flags, startId)
    }

    private fun createNotification(intent: Intent?) {
        val notificationConfig: NotificationConfig = intent?.getParcelableExtra(KEY_NOTICE_CONFIG) ?: NotificationConfig(
            title = "微信数据采集服务",
        )
        val noticeId =
            notificationConfig.noticeId ?: (System.currentTimeMillis() % Int.MAX_VALUE).toInt()
        notificationConfig.noticeId = noticeId
        val notification = notificationManager.showNotification(this, notificationConfig)
        YLog.i(TAG, "noticeId: $noticeId")
        startForeground(noticeId, notification)
    }

    override fun stopService(name: Intent?): Boolean {
        YLog.i("ReportService", "stopService: name = $name")
        CoreClient.unregister()
        return super.stopService(name)
    }

    override fun onDestroy() {
        YLog.i("ReportService", "onDestroy:")
        isRunning = false
        HeartBeatManager.stop()
        super.onDestroy()
    }
}