package com.yupao.wxcollect.service.procedure.dispatch

import androidx.annotation.WorkerThread
import com.yupao.wxcollect.service.SystemStatusManager
import com.yupao.wxcollect.service.procedure.dispatch.handler.CopyDatabaseHandler
import com.yupao.wxcollect.service.procedure.dispatch.handler.DatabaseHandler
import com.yupao.wxcollect.service.procedure.dispatch.handler.TaskHandler
import com.yupao.wxcollect.service.procedure.dispatch.status.IFinishedStatus
import com.yupao.wxcollect.service.procedure.dispatch.status.IStatusChanged
import com.yupao.wxcollect.service.procedure.dispatch.status.TrainExecutorStatus
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.TaskRuntimeEntity
import com.yupao.wxcollect.service.procedure.entity.tag
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffHelper
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * 任务合集（执行一批一批任务合集，像火车一样(train)）执行
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
class TaskTrainExecutor constructor(
    // 任务唯一标识
    val taskUnique: String,
    // 任务执行配置
    val config: TaskExecutorConfig,
    // 任务开始时间
    val startTime: Long = TimeUtil.getCurrTime(),
): IStatusChanged {

    private val tag = "TaskTrainExecutor:$taskUnique"

    private val copyDatabaseHandler by lazy { CopyDatabaseHandler(taskUnique, config.databasePath, config.isCopyForce) }

    private val taskHandler by lazy { TaskHandler(taskUnique, this) }

    private val databaseHandler by lazy { DatabaseHandler(
        flag = taskUnique,
        wechatDatabasePath = config.databasePath,
        password = config.password,
        statusHandler = this
    ) }

    var status: TrainExecutorStatus = TrainExecutorStatus.Ready
        private set


    private var statusChanged: (suspend (TaskExecutorConfig, TrainExecutorStatus) -> Unit)? = null

    fun setStatusChanged(statusChanged: suspend (TaskExecutorConfig, TrainExecutorStatus) -> Unit) {
        this.statusChanged = statusChanged
    }

    private val finishedCallbackList = mutableListOf<() -> Unit>()

    fun addFinishedCallback(finishedCallback: () -> Unit) {
        this.finishedCallbackList.add(finishedCallback)
    }

    @WorkerThread
    suspend fun execute() {
        YLog.i(tag, "execute task list: ${config.configList.tag()}")
        updateStatus(TrainExecutorStatus.Execute(taskUnique))

        val list = config.configList
        val taskCount = list.size
        // 任务为空
        if (taskCount == 0) {
            updateStatus(TrainExecutorStatus.Finished(0, emptyList(), emptyList()))
            return
        }

        // 拷贝数据库
        updateStatus(TrainExecutorStatus.CopyDatabase)
        val databasePath = copyDatabaseHandler.copyDatabase()
        if (databasePath.isNullOrEmpty()) {
            // 拷贝失败
            SystemStatusManager.addStatusMessage(SystemStatusManager.StatusType.DATABASE_COPY_FAILED, "数据库拷贝失败，请等待下一次执行")
            updateStatus(TrainExecutorStatus.Failed(copyDatabaseHandler.getFailedReason()))
            return
        }
        if (!checkStatus()) return

        val size = copyDatabaseHandler.getFileSize(databasePath)
        //判断是否大于2G
        if(size > 2048){
            SystemStatusManager.addStatusMessage(SystemStatusManager.StatusType.STORAGE_FULL, "请联系社群维护组进行处理")
        }
        updateStatus(TrainExecutorStatus.CopyDatabaseSucceed(size))


        // 适当延迟，以保障数据上报时网络可用
        delay(1000)
        if (!checkStatus()) return

        // 查询并保存用户信息
        updateStatus(TrainExecutorStatus.OpenDatabase)
        val userEntity = databaseHandler.open(databasePath) ?: return
        if (!checkStatus()) return
        DiffHelper.checkUsername(config.appType, userEntity.username)

        // 开始执行任务
        updateStatus(TrainExecutorStatus.PerformTask)
        taskHandler.perform(list, TaskRuntimeEntity(databaseHandler.databaseHelp, userEntity))
    }

    private fun checkStatus(): Boolean {
        val isRunning = this.status !is IFinishedStatus
        if (!isRunning) {
            YLog.i(tag, "checkStatus task $tag finished")
        }
        return isRunning
    }

    override suspend fun updateStatus(status: TrainExecutorStatus) {
        if (this.status is IFinishedStatus) {
            YLog.w(tag, "ignore status update: $status")
            return
        }
        this.status = status
        this.statusChanged?.invoke(config, status)
        if (status is IFinishedStatus) {
            finishedCallbackList.forEach {
                it.invoke()
            }
            release()
        }
    }

    private fun release() {
        YLog.i(tag, "release")
        this.statusChanged = null
        this.finishedCallbackList.clear()
        this.databaseHandler.databaseHelp.close()
    }

    fun cancel(uniqueList: List<String>?, reason: String?, isInterruptTask: Boolean = true) {
        YLog.i(tag, "cancel task: $reason, isInterruptTask: $isInterruptTask, $uniqueList")
        runBlocking {
            if (isInterruptTask) {
                taskHandler.cancelAllTask(uniqueList, reason)
            }
            // 取消所有任务
            if (uniqueList == null) {
                updateStatus(TrainExecutorStatus.Canceled(reason))
                YLog.i(tag, "cancel all task1: $reason")
            } else {
                // 判断两个集合数据是否一样，如果一样，则取消所有任务
                val configList = config.configList.map { it.unique }
                if (configList.size != uniqueList.size) {
                    return@runBlocking
                }
                configList.forEach {
                    if (!uniqueList.contains(it)) {
                        return@runBlocking
                    }
                }
                updateStatus(TrainExecutorStatus.Canceled(reason))
                YLog.i(tag, "cancel all task2: $reason")
            }
        }
    }

    fun print() {
        kotlin.runCatching {
            val completedTaskList = taskHandler.getCompletedTaskList()
            val runningTaskList = taskHandler.getRunningTaskList()
            YLog.i(tag, "status: $status\n" +
                    "completed task: ${completedTaskList.tag()}\n" +
                    "running task: ${runningTaskList.tag()}\n" +
                    "process: ${completedTaskList.size}/${completedTaskList.size + runningTaskList.size}"
            )
        }.onFailure {
            YLog.printException(tag, it, "print")
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TaskTrainExecutor

        if (config.databasePath != other.config.databasePath) return false

        return true
    }

    override fun hashCode(): Int {
        return config.databasePath.hashCode()
    }
}