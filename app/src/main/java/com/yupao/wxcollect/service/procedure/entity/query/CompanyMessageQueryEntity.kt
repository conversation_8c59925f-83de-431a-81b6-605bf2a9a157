package com.yupao.wxcollect.service.procedure.entity.query

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 17:28
 * @Description 需要查询企业消息的实体，不要轻易修改属性名称
 */
@Keep
data class CompanyMessageQueryEntity(
    val bizChatUserId: String? = null,
    val userName: String? = null,
    val content: String? = null,
    val createTime: String? = null,
    val talker: String? = null,
    val chatName: String? = null,
)