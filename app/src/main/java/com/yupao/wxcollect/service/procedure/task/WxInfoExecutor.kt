package com.yupao.wxcollect.service.procedure.task

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.entity.ITaskResult
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.TaskRuntimeEntity
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.entity.director.ReportData
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.ReportNode

/**
 * 采集微信用户信息
 *
 * <p>创建时间：2024/5/22/022</p>
 *
 * <AUTHOR>
 */
class WxInfoExecutor(
    private val taskEntity: CollectTaskEntity
): ITaskExecutor {

    private val tagFlag = taskEntity.entity.getTag()

    private var tag = "OfficialArticle[${tagFlag}]"

    override suspend fun perform(runtimeEntity: TaskRuntimeEntity): ITaskResult {
        val (username, _, alias) = runtimeEntity.userEntity
        val entity = taskEntity.entity
        val appType = entity.condition.appType
        val systemPart = if (appType == ReportConfig.APP_SUB) WechatNetModel.SUB else WechatNetModel.MAIN
        val data = ReportData(
            count = 1,
            data = WechatNetModel(
                wechatAccount = alias,
                originId = username,
                systemPart = systemPart,
            )
        )
        val result = ReportNode(tag).performReport(taskEntity, data)
        taskEntity.notifyStatusChanged(result.status)
        return result.result
    }
}

@Keep
data class WechatNetModel(
    val wechatAccount: String?,
    val originId: String?,
    val systemPart: String,
) {
    companion object {
        const val MAIN = "3"

        const val SUB = "4"
    }
}