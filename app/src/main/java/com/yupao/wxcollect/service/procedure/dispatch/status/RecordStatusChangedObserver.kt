package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.wxcollect.service.procedure.entity.ITaskConfig
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.TaskRecordEntity
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.record.TaskRecordHandler
import com.yupao.ylog.YLog

/**
 * 日志记录状态观察者
 *
 * <p>创建时间：2024/6/11/011</p>
 *
 * <AUTHOR>
 */
class RecordStatusChangedObserver(
    flag: String?
) : IStatusChangedObserver {

    private val tag = "RecordStatusChanged:$flag"

    override suspend fun onChanged(config: TaskExecutorConfig, status: TrainExecutorStatus) {
        if (status is TrainExecutorStatus.Execute) {
            reportExecuteRecord(config)
            return
        }
        if (status is TrainExecutorStatus.CopyDatabaseSucceed) {
            reportCopySucceed(config, status.size)
            return
        }
        if (status is TrainExecutorStatus.TaskCompleted) {
            reportResult(status)
            return
        }
        if (status is TrainExecutorStatus.Failed) {
            reportFailed(config, status.errorMsg)
            return
        }
        if (status is TrainExecutorStatus.Canceled) {
            reportFailed(config, status.reason ?: "任务取消")
            return
        }
    }

    private suspend fun reportExecuteRecord(config: TaskExecutorConfig) {
        val list = parseTaskRecordEntity(config)
        if (list.isEmpty()) return
        list.forEach { entity ->
            YLog.d("${entity.name}:${entity.unique}", "queryTime: ${entity.queryTime}, ${(entity.queryTime ?: 0) / 60}")
            val contentList = listOfNotNull(
                "上传类型: ${entity.name}",
                if (BuildConfig.DEBUG) "任务标识: ${entity.unique.substring(0, entity.unique.length.coerceAtMost(16))}" else null,
                entity.interval?.takeIf { it > 0 }?.let { "上传间隔: ${it / 60 }分钟" },
                "数据库密码: ${entity.password}",
                entity.queryTime?.takeIf { it > 0 }?.let { "查询${it / 60 }分钟内的数据" },
            )
            TaskRecordHandler.insert(entity, contentList, isNotifyUI = false)
        }
        TaskRecordHandler.notifyUIUpdate(true)
    }

    private fun asTaskRecordEntity(it: ITaskConfig, password: String?): TaskRecordEntity? {
        return if (it is CollectTaskEntity) {
            TaskRecordEntity(
                unique = it.entity.unique,
                name = it.entity.taskName,
                appType = it.entity.condition.appType,
                password = password,
                interval = it.entity.taskInterval,
                queryTime = it.entity.query.queryTime,
                taskSort = it.entity.condition.sort,
                taskCode = it.entity.taskCode,
            )
        } else null
    }

    private fun parseTaskRecordEntity(config: TaskExecutorConfig): List<TaskRecordEntity> {
        return config.configList.mapNotNull {
            asTaskRecordEntity(it, config.password)
        }
    }

    private suspend fun reportResult(status: TrainExecutorStatus.TaskCompleted) {
        val entity = asTaskRecordEntity(status.config, null) ?: kotlin.run {
            YLog.w(tag, "reportResult not support: ${status.config}")
            return
        }
        val isOK = status.result.isOk()
        val content = if (isOK) "推广成功: ${status.result.count ?: "-"}条" else "推广失败: ${status.result.msg}"
        val result = if (isOK) TaskRecordHandler.TypeSucceed else TaskRecordHandler.TypeFailed
        TaskRecordHandler.insert(entity, content, result)
    }

    private suspend fun reportFailed(config: TaskExecutorConfig, errorMsg: String) {
        val list = parseTaskRecordEntity(config)
        if (list.isEmpty()) return
        list.forEach { entity ->
            val content = "推广失败: $errorMsg"
            TaskRecordHandler.insert(entity = entity, content = content, result = TaskRecordHandler.TypeFailed)
        }
    }

    private suspend fun reportCopySucceed(config: TaskExecutorConfig, size: Long) {
        val list = parseTaskRecordEntity(config)
        if (list.isEmpty()) return
        list.forEach { entity ->
            val content = "数据库文件大小: ${size}M"
            TaskRecordHandler.insert(entity, content)
        }
    }
}