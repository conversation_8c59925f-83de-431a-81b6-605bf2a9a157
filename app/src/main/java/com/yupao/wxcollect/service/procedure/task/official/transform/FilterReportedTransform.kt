package com.yupao.wxcollect.service.procedure.task.official.transform

import com.yupao.wxcollect.service.procedure.entity.query.ArticleEntity
import com.yupao.wxcollect.service.procedure.entity.request.ArticlesEntity
import com.yupao.wxcollect.service.procedure.entity.request.ValidIdsRequestEntity
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.service.procedure.task.NetRequestHelp
import com.yupao.ylog.YLog

/**
 * 过滤已经上报的文章
 *
 * <p>创建时间：2024/6/13/013</p>
 *
 * <AUTHOR>
 */
class FilterReportedTransform(
    private val username: String?,
    private val phoneNum: String?,
    private val resp: ReportRepository,
): IArticleTransform {
    companion object {
        private const val TAG = "FilterReportedTransform"
    }

    private val help by lazy { NetRequestHelp() }


    override suspend fun transform(list: List<ArticleEntity>): List<ArticleEntity> {
        if (list.isEmpty()) {
            return list
        }
        var uploadList: List<ArticleEntity>? = null
        help.submit(TAG) {
            resp.validIds(ValidIdsRequestEntity(
                wechatOriginId = username,
                phoneNum = phoneNum,
                articles = list.map {
                    ArticlesEntity(
                        articleUnique = it.articleUnique,
                        officialAccountOriginId = it.talker
                    )
                }
            )).apply {
                if (this?.isOK() == true) {
                    uploadList = this.getData()?.filter { it.isCollect == false }?.mapNotNull { entity ->
                        list.find { it.articleUnique == entity.articleUnique }
                    } ?: emptyList()
                }
            }
        }
        YLog.d(TAG, "fetchUploadList, ${uploadList?.size}")
        return uploadList ?: list
    }
}