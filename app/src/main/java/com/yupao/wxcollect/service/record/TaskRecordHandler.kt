package com.yupao.wxcollect.service.record

import com.google.gson.Gson
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.MessageEntity
import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.constant.DeviceConfig
import com.yupao.wxcollect.database.CommonDatabaseModule
import com.yupao.wxcollect.service.db.TaskRecordDao
import com.yupao.wxcollect.service.procedure.entity.TaskRecordEntity
import com.yupao.wxcollect.service.procedure.entity.TaskRecordExtLocalModel
import com.yupao.wxcollect.service.procedure.entity.TaskRecordLocalModel
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
object TaskRecordHandler {
    private const val TAG = "TaskRecordHandler"

    //执行app UI日志记录最大数量
    private const val maxCount = 5000

    // 成功
    const val TypeSucceed = "Succeed"

    // 失败
    const val TypeFailed = "Failed"

    // 未知
    const val TypeUnknown = "Unknown"

    private val isUpdateUI = AtomicBoolean(false)

    private val dao: TaskRecordDao? by lazy {
        App.getContext()?.let {
            CommonDatabaseModule.getDataBase(it)
        }?.getTaskRecordDao()
    }

    private val gson by lazy { Gson() }

    init {
        updateUILooper()
    }

    suspend fun insert(entity: TaskRecordEntity, content: String, result: String = TypeUnknown) {
        insert(entity, listOf(content), result, true)
    }

    suspend fun insert(
        entity: TaskRecordEntity,
        contentList: List<String>,
        result: String = TypeUnknown,
        isNotifyUI: Boolean
    ) {
        if (contentList.isEmpty()) return
        val createTime = TimeUtil.getCurrTime()
        kotlin.runCatching {
            val taskList = contentList.mapNotNull { content ->
                // 是否已经执行完成了，执行完成，不重复写入结果
                if (result != TypeUnknown) {
                    val model = dao?.queryLast(entity.unique)
                    if (model != null && model.result != TypeUnknown) {
                        YLog.i(
                            TAG,
                            "ignore result: $result, ${model.unique}, ${model.result}, $content"
                        )
                        return@mapNotNull null
                    }
                }
                TaskRecordLocalModel(
                    unique = entity.unique,
                    name = entity.name,
                    createTime = createTime,
                    content = content,
                    appType = entity.appType,
                    result = result,
                    sort = TimeUtil.getLauncherTime(),
                    ext = gson.toJson(TaskRecordExtLocalModel(entity.taskSort, entity.taskCode)),
                )
            }
            dao?.insertLoop(taskList, DeviceConfig.logMaxNum ?: maxCount) ?: kotlin.run {
                YLog.w(TAG, "insertLoop failed: dao is null")
            }
        }.onFailure {
            val taskList = entity.unique
            YLog.printException(
                TAG,
                it,
                "insert: $taskList, content: $contentList, result: $result"
            )
        }
        if (isNotifyUI) {
            notifyUIUpdate()
        }
    }

    /**
     * 通知UI更新
     * @param isImmediately: 是否立即更新
     */
    fun notifyUIUpdate(isImmediately: Boolean = false) {
        isUpdateUI.set(true)
        if (isImmediately) {
            sendMessageToUI()
        }
    }

    private fun updateUILooper() {
        // 每隔3s更新一次UI
        GlobalScope.launch(Dispatchers.IO) {
            while (isActive) {
                delay(3000L)
                if (isUpdateUI.getAndSet(false)) {
                    YLog.i(TAG, "notifyUIUpdate")
                    sendMessageToUI()
                }
            }
        }
    }

    private fun sendMessageToUI() {
        MessageManager.executor.submit(
            MessageEntity(
                MessageCode.UpdateRecord,
                AppConstant.Host.wxCollectUI
            )
        ) {
            true
        }
    }

}