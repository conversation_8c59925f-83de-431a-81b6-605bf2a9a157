package com.yupao.wxcollect.service.procedure.task.customize.diff

import android.util.SparseArray
import com.yupao.wxcollect.service.procedure.entity.db.IBinarySearch

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/21/021</p>
 *
 * <AUTHOR>
 */
class TaskDiffHelper {

    /**
     * 获取唯一值
     * @param map：数据源
     * @param uniqueIgnoreKey: 忽略校验的key
     */
    fun uniqueCode(map: Map<String, Any?>, uniqueIgnoreKey: List<String>? = null): Int {
        val valueList = map.entries.sortedBy { it.key }
            .mapNotNull {
                if (uniqueIgnoreKey?.contains(it.key) == true) {
                    return@mapNotNull null
                }
                it.value
            }
        return valueList.hashCode()
    }

    /**
     * 二分查找
     */
    fun <T: IBinarySearch> binarySearch(
        sortedList: List<T>,
        targetSortValue: Int
    ): List<T> {
        val matchedItems = mutableListOf<T>()
        var low = 0
        var high = sortedList.size - 1

        while (low <= high) {
            val mid = low + (high - low) / 2
            when {
                sortedList[mid].sortCode() < targetSortValue -> low = mid + 1
                sortedList[mid].sortCode() > targetSortValue -> high = mid - 1
                else -> {
                    // 找到一个匹配项，向两端扩展查找范围
                    var left = mid
                    var right = mid
                    while (left >= 0 && sortedList[left].sortCode() == targetSortValue) {
                        matchedItems.add(sortedList[left])
                        left--
                    }
                    while (right < sortedList.size && sortedList[right].sortCode() == targetSortValue) {
                        matchedItems.add(sortedList[right])
                        right++
                    }
                    break // 已经找到并收集了所有连续的匹配项
                }
            }
        }
        return matchedItems
    }

}