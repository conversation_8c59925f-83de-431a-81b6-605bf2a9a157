package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep
import com.google.gson.JsonObject

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/10/23/023</p>
 *
 * <AUTHOR>
 */
@Keep
data class UploadRequestEntity(

    /**
     * 入口场景id
     */
    val entryId: Int,
    /**
     * 资源类型，0:图片，1视频，2音频
     *
     * 参考[FileType]
     */
    @FileType
    val type: Int,
    /**
     * 文件格式名,不包含'.'分隔符
     */
    val format: String,

    /**
     * 处理类型
     */
    val processType: Int= 4,

    /**
     * 拍摄时间,时间字符。例如2022/06/07 14:02:00
     */
    val takeTime: String? = null,
    /**
     * 视频拍摄时长 单位秒
     */
    val videoTime: Int? = null,
    /**
     * 城市
     */
    val city: String? = null,
    /**
     * 地址详情
     */
    val address: String? = null,
    /**
     * 水印类型，音频可以为空
     */
    val wmId: Int? = null,
    /**
     * 水印相关扩展信息，音频可以为空
     */
    val wmcInfo: JsonObject? = null,
    /**
     * 鱼泡uid
     */
    val uid: String? = null,

    /**
     * 相册类型，0：个人
     */

    var albumType: Int? = null,

    /**
     * 用户id，albumType为0时是鱼泡用户id
     */
    var busId: String? = null,

    /**
     * 上传方式
     */
    val httpMethod: String? = null,

    /**
     * 额外属性字段
     */
    val extraInfo: JsonObject? = null
)
