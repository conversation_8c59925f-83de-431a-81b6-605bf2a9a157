package com.yupao.wxcollect.service.procedure.task.official.report

import com.yupao.wxcollect.service.procedure.task.official.OfficialArticleExecutor
import com.yupao.ylog.YLog
import java.io.File

/**
 * 写入文件
 *
 * <p>创建时间：2024/6/13/013</p>
 *
 * <AUTHOR>
 */
class WriteToFileHandle {
    companion object {
        private const val TAG = "WriteToFileHandle"
    }

    fun performSaveToFile(name: String?, content: String): String {
        if (content.isBlank()) {
            YLog.e(TAG, "name: $name content is empty.")
            throw RuntimeException("未获取到内容")
        }
        val file = File(OfficialArticleExecutor.baseFile().absolutePath + File.separator + name)
        file.parentFile?.takeUnless { it.exists() }?.mkdirs()
        file.writeText(content)
        return file.absolutePath
    }
}