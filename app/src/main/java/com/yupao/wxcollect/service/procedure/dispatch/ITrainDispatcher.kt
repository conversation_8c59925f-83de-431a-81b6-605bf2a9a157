package com.yupao.wxcollect.service.procedure.dispatch

import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
interface ITrainDispatcher {

    /**
     * 分发任务
     *
     * @param configList 任务配置列表
     */
    suspend fun dispatch(configList: List<TaskExecutorConfig>)

    /**
     * 唤醒分发
     */
    fun wakeup()

    /**
     * 打印数据
     */
    fun print()

    /**
     * 取消任务
     *
     * @param uniqueList： 任务唯一标识
     * @param reason：取消原因
     */
    fun cancel(uniqueList: List<String>, reason: String?)
}