package com.yupao.wxcollect.service.procedure.task

import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.wxcollect.transmit.director.DebugConfig
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog

/**
 * 跟踪每一步执行耗时
 *
 * <p>创建时间：2024/6/24/024</p>
 *
 * <AUTHOR>
 */
class StepDurationTrace(private val tag: String) {

    private var lastTime = 0L

    private var step: Int = 0

    private var totalDuration = 0L

    private var count = 0

    private var extDuration = TimeUtil.getCurrTime()

    private val isEnable = BuildConfig.DEBUG

    fun reset(ext: String = "") {
        if(!isEnable) return
        count++
        lastTime = 0L
        step = 0
        trace(ext)
    }

    fun trace(ext: String = "") {
        if(!isEnable) return
        val currTime = TimeUtil.getCurrTime()
        if (lastTime > 0) {
            val duration = currTime - lastTime
            totalDuration += duration
            val message = if(ext.isEmpty()) "step$step" else "step$step[$ext]"
            YLog.i(tag = tag, "$message count: $count duration: ${duration}ms, total: ${totalDuration}ms, ext: ${currTime - extDuration}ms")
        }
        lastTime = currTime
        step++
    }
}