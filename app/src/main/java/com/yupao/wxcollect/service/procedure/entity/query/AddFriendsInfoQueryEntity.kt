package com.yupao.wxcollect.service.procedure.entity.query

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @Date 2023/4/20/020 16:50
 * @Description
 */
@Keep
data class AddFriendsInfoQueryEntity(
    val content: String? = null,
    val talker: String? = null,
    val nickname: String? = null,
    val createTime: String? = null,
    val alias: String? = null,
    // 好友备注
    val conRemark: String? = null,
)