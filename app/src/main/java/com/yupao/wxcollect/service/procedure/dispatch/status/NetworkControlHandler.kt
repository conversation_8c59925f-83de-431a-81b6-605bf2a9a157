package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.AppTouchEventManager
import com.yupao.wxcollect.service.net_monitor.NetMonitorService
import com.yupao.wxcollect.service.procedure.FixedDelayStrategy
import com.yupao.wxcollect.service.procedure.RetryHelper
import com.yupao.wxcollect.service.procedure.emptyResult
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.ylog.YLog
import kotlinx.coroutines.delay

/**
 * 微信网络开关控制
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
class NetworkControlHandler(
    flag: String?,
    private val appType: String,
    // 发生错误时最大重试次数
    private val retryCountIfFailed: Int = 3
) {
    private val TAG = "NetworkControl:$flag"

    private val appTouchEventManager by lazy { AppTouchEventManager() }

    private val openRetryHelper by lazy {
        RetryHelper<Unit>(retryCountIfFailed, FixedDelayStrategy(500)) { count, _ ->
            YLog.i(TAG, "openNetwork: retry count: $count")
            false
        }
    }

    private val closeRetryHelper by lazy {
        RetryHelper<Unit>(retryCountIfFailed, FixedDelayStrategy(500)) { count, _ ->
            YLog.i(TAG, "closeNetwork: retry count: $count")
            false
        }
    }

    /**
     * 是否支持网络开关控制
     */
    val isSupportNetworkCtrl by lazy {
        kotlin.runCatching {
            isWechatNetworkCtrl()
        }.getOrNull() ?: true
    }

    private fun isWechatNetworkCtrl(): Boolean {
        // 微信在挂前台的时候不断网进行复制，连续3次复制出错暂停，10分钟无屏幕操作的时候进行断网；
        return com.yupao.wxcollect.constant.isWechatNetworkCtrl && !appTouchEventManager.isTouchAtLeast(
            "com.tencent.mm",
            10 * 60_000L,
            appType != ReportConfig.APP_SUB
        )
    }

    private suspend fun turnWechatNetwork(isOpen: Boolean): String? {
        if (isOpen == !NetMonitorService.isRunning) {
            return null
        }
        val result = kotlin.runCatching {
            if (!isOpen) {
                val result = NetMonitorService.start(
                    App.getContext()
                        ?: return "null context", 123
                )
                if (result) null else "打开失败"
            } else {
                NetMonitorService.stop(App.getContext() ?: return "null context")
                null
            }
        }.getOrNull()
        // 适当休眠
        delay(3000)
        return result
    }

    suspend fun openNetwork(): Boolean {
        if (!isSupportNetworkCtrl) return false
        val result = openRetryHelper.emptyResult {
            turnWechatNetwork(true)
        }
        YLog.i(TAG, "openNetwork: result = ${result.toString()}")
        return result?.isSuccess ?: false
    }

    suspend fun closeNetwork(): Boolean {
        if (!isSupportNetworkCtrl) return false
        val result = closeRetryHelper.emptyResult {
            turnWechatNetwork(false)
        }
        YLog.i(TAG, "closeNetwork: result = ${result.toString()}")
        return result?.isSuccess ?: false
    }
}