package com.yupao.wxcollect.service.procedure.api

import com.yupao.wxcollect.net.HttpConstant
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.Url

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/11/6/006</p>
 *
 * <AUTHOR>
 */
interface WechatApi {

    @GET
    @Headers("${HttpConstant.Key.IS_WX_HEADER}: true")
    fun urlContent(@Url url: String?, @Header(HttpConstant.Key.Timeout) timeoutSecond: Int): Call<ResponseBody>
}