package com.yupao.wxcollect.service.accessibility.filter

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/4/27/027 19:06
 * @Description 过滤
 *  1. 找到text对应的控件
 *  2. 找到偏移offset第几个的className对应控件
 */
@Keep
data class AccessibilityNodeInfoFilter(
    val className: String? = null,
    val offset: Int? = IViewHandler.CURR_OFFSET,
    // 是否贪婪模式，会遍历子控件
    val isGreedyMode: Boolean = false
) {
    companion object {
        const val IMAGE = "android.widget.ImageView"

        const val TEXT = "android.widget.TextView"
    }
}