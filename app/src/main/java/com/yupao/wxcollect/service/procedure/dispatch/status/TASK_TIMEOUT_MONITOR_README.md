# 任务超时监控功能说明

## 概述

`CheckStatusChangedObserver` 实现了对群聊消息和好友申请任务的超时监控功能。当任务执行时间超过设定的 `timeoutTime` 时，会通过 `SystemStatusManager` 弹出悬浮窗提醒用户。

## 功能特性

### 1. 监控目标任务
- **群聊天记录** (`TaskCode.WxGroupChat`)
- **微信群好友** (`TaskCode.WxGroupFriend`) 
- **微信好友申请采集** (`TaskCode.WxFriendApply`)

### 2. 核心功能
- ✅ **任务开始监控**：当任务进入 `Execute` 状态时开始计时
- ✅ **超时检测**：根据 `timeoutTime` 设置超时时间
- ✅ **悬浮窗提醒**：超时时通过 `SystemStatusManager` 显示提醒
- ✅ **智能去重**：同时只显示一个超时提醒，避免重复
- ✅ **自动清理**：任务完成或失败时自动移除监控

### 3. 状态处理
- **Execute**：开始监控，记录执行时间，启动超时检查
- **TaskCompleted**：移除监控，取消超时检查
- **Failed**：清理所有相关任务的监控
- **Canceled**：清理所有相关任务的监控

## 实现原理

### 1. 任务筛选
```kotlin
private fun filterTargetTasks(configList: List<ITaskConfig>): List<CollectTaskEntity> {
    return configList.filterIsInstance<CollectTaskEntity>()
        .filter { task ->
            task.entity.taskCode in TARGET_TASK_CODES
        }
}
```

### 2. 超时监控
```kotlin
// 记录任务开始时间
executingTasks[unique] = currentTime

// 启动超时检查协程
val timeoutJob = coroutineScope.launch {
    delay(timeoutTime)
    if (executingTasks.containsKey(unique)) {
        handleTaskTimeout(unique, task, timeoutTime)
    }
}
```

### 3. 超时处理
```kotlin
private suspend fun handleTaskTimeout(unique: String, task: CollectTaskEntity, timeoutTime: Long) {
    // 防重复提醒
    if (hasTimeoutAlert.getAndSet(true)) {
        return
    }
    
    // 生成提醒消息
    val message = "任务「$taskName」执行超时(${timeoutMinutes}分钟)，请检查网络或重启应用"
    
    // 显示悬浮窗提醒
    SystemStatusManager.addStatusMessage(
        SystemStatusManager.StatusType.SERVICE,
        message,
        priority = 9
    )
}
```

## 使用方式

### 1. 自动集成
观察者已自动添加到 `TrainStatusDispatcher` 中：

```kotlin
private val observerList = listOf(
    NetworkStatusChangedObserver(flag, configSize),
    DirectorStatusChangedObserver(),
    RecordStatusChangedObserver(flag),
    CheckStatusChangedObserver()  // 新增
)
```

### 2. 配置超时时间
在任务配置中设置 `timeoutTime`：

```kotlin
TaskExecutorConfig(
    // ... 其他配置
    timeoutTime = 5 * 60 * 1000L, // 5分钟超时
    configList = listOf(
        CollectTaskEntity(
            entity = TaskEntity(
                taskCode = TaskCode.WxGroupChat,
                timeoutTime = 3 * 60 * 1000L // 单个任务3分钟超时
            )
        )
    )
)
```

### 3. 超时时间优先级
1. **任务级别**：`task.entity.timeoutTime`（如果 > 0）
2. **配置级别**：`config.timeoutTime`（作为默认值）

## 提醒消息格式

### 超时提醒
```
任务「群聊天记录采集」执行超时(5分钟)，请检查网络或重启应用
```

### 消息特性
- **类型**：`SystemStatusManager.StatusType.SERVICE`
- **优先级**：9（高优先级）
- **显示方式**：悬浮窗提示
- **去重机制**：同时只显示一个超时提醒

## 监控状态管理

### 1. 数据结构
```kotlin
// 正在执行的任务：unique -> 开始时间
private val executingTasks = ConcurrentHashMap<String, Long>()

// 超时检查任务：unique -> Job
private val timeoutJobs = ConcurrentHashMap<String, Job>()

// 超时提醒标志
private val hasTimeoutAlert = AtomicBoolean(false)
```

### 2. 状态清理
- **任务完成**：移除对应任务的监控
- **任务失败/取消**：清理所有相关任务的监控
- **所有任务完成**：重置超时提醒标志

## 测试验证

### 1. 运行测试
```kotlin
// 运行所有测试
CheckStatusChangedObserverTest.runAllTests()

// 单独测试
CheckStatusChangedObserverTest.testGroupChatTimeout()
CheckStatusChangedObserverTest.testFriendApplyNormalComplete()
```

### 2. 测试场景
- ✅ 群聊消息任务超时
- ✅ 好友申请任务正常完成
- ✅ 多个任务中一个超时
- ✅ 任务失败情况处理

### 3. 调试信息
```kotlin
// 获取当前监控状态
val status = observer.getMonitoringStatus()
// 输出：正在监控任务数: 2, 是否有超时提醒: false
```

## 日志输出

### 关键日志
```
CheckStatusChangedObserver: 开始监控任务超时，任务数量: 2
CheckStatusChangedObserver: 任务开始执行: test-unique-123, 超时时间: 300000ms
CheckStatusChangedObserver: 任务超时: test-unique-123, 超时时间: 300000ms
CheckStatusChangedObserver: 已显示超时提醒: 任务「群聊天记录采集」执行超时(5分钟)，请检查网络或重启应用
CheckStatusChangedObserver: 任务完成: test-unique-456
CheckStatusChangedObserver: 所有监控任务已完成，重置超时提醒标志
```

## 注意事项

### 1. 性能考虑
- 使用 `ConcurrentHashMap` 保证线程安全
- 协程异步处理，不阻塞主线程
- 任务完成后及时清理资源

### 2. 内存管理
- 任务完成/失败时自动清理监控数据
- 使用弱引用避免内存泄漏

### 3. 用户体验
- 同时只显示一个超时提醒
- 提醒消息包含具体的任务名称和超时时间
- 支持点击悬浮窗查看更多信息

### 4. 扩展性
- 可以轻松添加更多任务类型到监控范围
- 支持自定义超时处理逻辑
- 可以集成更多的提醒方式

## 故障排除

### 1. 超时提醒不显示
- 检查 `SystemStatusManager` 是否正确初始化
- 确认 `KVHelper.isClashTipsDb()` 返回 true
- 查看日志确认任务是否被正确识别

### 2. 重复提醒
- 检查 `hasTimeoutAlert` 标志是否正确重置
- 确认任务完成后是否正确清理监控

### 3. 内存泄漏
- 确保任务完成/失败时调用了清理方法
- 检查协程是否正确取消

这个实现提供了完整的任务超时监控功能，能够有效提醒用户任务执行异常，提升用户体验。
