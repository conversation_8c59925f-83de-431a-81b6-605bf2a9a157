package com.yupao.wxcollect.service.procedure.task.customize.diff

import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.database.CommonDatabaseModule
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.contact.ContactDiffProvider
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.group.GroupDiffProvider
import com.yupao.wxcollect.util.SpUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/9/009</p>
 *
 * <AUTHOR>
 */
object DiffHelper {

    private const val KEY_MAIN_USER_NAME = "main_username"

    private const val KEY_SUB_USER_NAME = "sub_username"

    private val commDbHelper by lazy {
        listOf(
            GroupDiffProvider(),
            ContactDiffProvider(),
        )
    }

    private fun getUsername(appType: String): String? {
        return SpUtil.getString(if (appType == ReportConfig.APP_SUB) KEY_SUB_USER_NAME else KEY_MAIN_USER_NAME)
    }

    fun checkUsername(appType: String, username: String?) {
        val cacheUsername = getUsername(appType)
        if (cacheUsername == username) {
            return
        }
        YLog.i("DiffHelper", "modifyUsername, " +
                "appType: $appType, username: $username, cacheUsername: $cacheUsername")
        if (appType == ReportConfig.APP_SUB) {
            SpUtil.putString(KEY_SUB_USER_NAME, username)
        } else {
            SpUtil.putString(KEY_MAIN_USER_NAME, username)
        }
        commDbHelper.forEach {
            it.clearTable(appType, true)
            it.clearTable(appType, false)
        }
    }

    private fun getReportVersionKey(appType: String, taskCode: String): String {
        return "report_diff_version-$appType-$taskCode"
    }

    fun getReportVersion(appType: String, taskCode: String): Int {
        return SpUtil.getInt(getReportVersionKey(appType, taskCode))
    }

    fun saveReportVersion(appType: String, taskCode: String, version: Int) {
        // 上报的版本一定是正数
        if (version <= 0) {
            return
        }
        SpUtil.putInt(getReportVersionKey(appType, taskCode), version, true)
    }

    /**
     * 清理无效的数据库
     */
    fun clearInvalidDatabase() {
        CoroutinePool.other.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val context = AndroidSystemUtil.getContext()
                CommonDatabaseModule.getDataBase(context).getMainContactDao().clearTable()
                CommonDatabaseModule.getDataBase(context).getSubContactDao().clearTable()
                YLog.i("DiffHelper", "clearInvalidDatabase succeed")
            }.onFailure {
                YLog.printException("clearInvalidDatabase", it)
            }
        }
    }
}