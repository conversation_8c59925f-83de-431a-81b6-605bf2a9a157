package com.yupao.wxcollect.service.concurrent

import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.lang.RuntimeException
import kotlin.random.Random

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/25/025</p>
 *
 * <AUTHOR>
 */
interface IConcurrentEntity<T> {

    val block: suspend () -> T

    val group: String

    val result: CompletableDeferred<T?>
}

data class ConcurrentEntity<T>(
    override val block: suspend () -> T,
    override val group: String,
    override val result: CompletableDeferred<T?>,
): IConcurrentEntity<T>

//fun main() {
//    runBlocking {
//        val channel = Channel<Int>(3)
//        launch(Dispatchers.IO) {
//            for (item in channel) {
//                delay(200)
//                println("received: $item")
//            }
//            println("perform finished")
//        }
//        launch(Dispatchers.IO) {
//            repeat(3) {
//                delay(100)
//                channel.send(it)
//            }
//            println("send finished")
//        }
//        delay(4000)
//        println("finished")
//    }
//}


fun main() {
    val deferred = CompletableDeferred<String>()

    // 启动一个异步任务
    GlobalScope.launch {
        delay(100) // 模拟耗时操作
        deferred.complete("Operation completed")
        println("send")
    }

    // 等待操作完成并获取结果
    val result = runBlocking {
        delay(1000) // 模拟耗时操作
        deferred.await()
    }

    println(result) // 输出: Operation completed
}