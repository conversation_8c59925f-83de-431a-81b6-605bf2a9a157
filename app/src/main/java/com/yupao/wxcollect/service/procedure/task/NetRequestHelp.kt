package com.yupao.wxcollect.service.procedure.task

import com.yupao.data.protocol.BaseEntity
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.procedure.entity.TaskResultEntity
import com.yupao.wxcollect.util.NetworkUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.delay
import kotlin.math.pow

/**
 * <AUTHOR>
 * @Date 2023/4/20/020 13:51
 * @Description 网络请求需要重试
 */
class NetRequestHelp {

    // 最大重试次数
    private val maxTryCount = 4

    // 重试最小延迟时间
    private val minDelayTime = 1000

    suspend fun submit(
        tag: String? = null,
        count: Int? = 0,
        block: (suspend () -> BaseEntity<*>?)? = null
    ): Pair<Int, String?> {
        YLog.i("NetRequestHelp", "$tag start submit, count = $count")
        val currCount = count ?: 0
        var expMessage: String? = null
        val currTime = TimeUtil.getCurrTime()
        val result = try {
            block?.invoke()
        } catch (e: Exception) {
            e.printStackTrace()
            expMessage = e.message
            YLog.printException(tag, e)
            null
        }
        val duration = TimeUtil.getCurrTime() - currTime
        YLog.i("NetRequestHelp", "$tag response duration: ${duration}ms")
        if (result?.isOK() != true) {
            YLog.w(tag, "submit: result: ${result?.getMsg()}")
            val context = App.getContext()
            val connected = NetworkUtil.isNetworkConnected(context)
            if (connected || currCount >= maxTryCount) {
                return if (connected) {
                    Pair(TaskResultEntity.CODE_REQUEST_FAILED, "请求失败: ${result?.getMsg() ?: expMessage}")
                } else {
                    Pair(TaskResultEntity.CODE_NO_NETWORK, "网络断开连接")
                }
            }
            YLog.w(tag ?: "NetRequestHelp", "submit: connected: $connected, try count: $currCount")
            if (!connected) {
                NetworkUtil.setAirplaneMode(false)
            }
            delayTime(currCount)
            return submit(tag, currCount + 1, block)
        } else {
            YLog.i(tag, "submit: succeed")
        }
        return Pair(TaskResultEntity.CODE_SUCCEED, null)
    }

    private suspend fun delayTime(count: Int)  {
        val pow = 1.5.pow(count.toDouble())
        val delayTime = pow * minDelayTime
        YLog.i("NetRequestHelp", "delayTime: count = $count, delayTime = $delayTime")
        delay(delayTime.toLong())
    }
}