package com.yupao.wxcollect.service.procedure.task.official

import com.yupao.ylog.YLog
import kotlinx.coroutines.runBlocking
import okhttp3.OkHttpClient
import okhttp3.Request
import org.jsoup.Jsoup
import org.jsoup.nodes.Element
import java.io.File

/**
 * 公众号内容过滤
 *
 * <p>创建时间：2024/5/7/007</p>
 *
 * <AUTHOR>
 */
class ContentFilter {

    // 只有手机号满足以下格式，并且前后非数字才会满足条件
    // 131-1234-1234
    // 131 1234 1234
    // 13112341234
    // (\D)1(([\d]{10})|([\d]{2}( |-)[\d]{4}( |-)[\d]{4}))(\D)
    private val phoneRegex = Regex("(\\D)1(([\\d]{10})|([\\d]{2}( |-)[\\d]{4}( |-)[\\d]{4}))(\\D)")

    // title标签
    private val titleTag = "<h1 class=\"rich_media_title \" id=\"activity-name\">"

    private val keywords = listOf("招", "找")

    private val keywordRegex = Regex(keywords.joinToString("|"))

    private val blackListCache by lazy {
        BlackListCache()
    }

    suspend fun filter(content: String?): String? {
        if (content.isNullOrBlank()) {
            return "内容为空"
        }
        val filterTitle = kotlin.runCatching {
            filterTitle(content)
        }.onFailure {
            return it.message
        }.getOrNull() ?: return null
        val filterContent = filterContent(content) ?: return null
        return "$filterTitle & $filterContent"
    }

    private suspend fun filterTitle(content: String): String? {
        val articleTitle = getArticleTitle(content) ?: kotlin.run {
            val ext = if(content.length > 10) content.substring(0, 10) else content
            return "未获取到标题: ${ext}..."
        }
        val contains = articleTitle.contains(keywordRegex)
        if (contains) {
            val list = blackListCache.fetchBlackList()
            list.forEach {
                if (articleTitle.contains(it)) {
                    throw RuntimeException("《${articleTitle}》包含黑名单：${it}")
                }
            }
            return null
        }
        return "《${articleTitle}》不包含${keywords.joinToString(",")}"
    }

    private fun filterContent(content: String): String? {
        return kotlin.runCatching {
            val doc = Jsoup.parse(content)
            val stringBuilder = StringBuilder()
            getArticleContent(doc.body(), stringBuilder)
            val bodyContent = stringBuilder.toString()
            // 校验是否包含手机号
            if (!bodyContent.contains(phoneRegex)) {
                return "内容不包含手机号"
            }
            // 校验是否包含关键词
            val contains = bodyContent.contains(keywordRegex)
            if (contains) {
                return null
            }
            return "内容不包含${keywords.joinToString(",")}"
        }.onFailure {
            YLog.printException("ContentFilter", it)
        }.getOrDefault("exception")
    }

    fun getArticleContent(element: Element, stringBuilder: StringBuilder) {
        for (child in element.children()) {
            val ownText = child.ownText()
            if (!ownText.isNullOrBlank()) {
                stringBuilder.append(ownText).append("\n")
            }
            val children = child.children()
            if (children.isNullOrEmpty()) {
                if (ownText.isNullOrBlank()) {
                    val text = child.text()
                    if (!text.isNullOrBlank()) {
                        stringBuilder.append(text).append("\n")
                    }
                }
            } else {
                // 如果元素有子节点，递归提取子节点的最深层文本
                getArticleContent(child, stringBuilder)
            }
        }
    }

    private fun getArticleTitle(content: String): String? {
        val doc = Jsoup.parse(content)
        val title = doc.title()
        if (!title.isNullOrBlank()) {
            return title
        }
        var index = content.indexOf(titleTag)
        return if (index > 0) {
            val indexStart = index + titleTag.length
            index = content.indexOf("</h1>", indexStart)
            val indexEnd = if(index > 0) index else content.length.coerceAtMost(indexStart + 50)
            if (indexEnd > indexStart) {
                return content.substring(indexStart, indexEnd).trim()
            } else {
                null
            }
        } else {
            null
        }
    }
}

private fun checkPhone() {
    val content = "如果不知道如何操作加下方微信（安欣：18296298796微信同步"
    val phoneRegex = Regex("(\\D)1(([\\d]{10})|([\\d]{2}( |-)[\\d]{4}( |-)[\\d]{4}))(\\D)")
    val result = content.contains(phoneRegex)
    println(result)
}

private fun writeBody(url: String) {
    val contentFilter = ContentFilter()
    val httpClient = OkHttpClient.Builder().build()
    val doc = Jsoup.parse(fetchUrlContent(url, httpClient).second ?: "")
    val stringBuilder = StringBuilder()
    contentFilter.getArticleContent(doc, stringBuilder)
    val file = File("E:\\temp\\article-content.txt")
    file.parentFile?.takeUnless { it.exists() }?.mkdirs()
    file.writeText(stringBuilder.toString())
}

fun main() {
//    val list = "/r/258c/106/pb/f/20240327/df609b317cc14cd19ddf2849dc7d37a9.html\n" +
//            "/r/e275/106/pb/f/20240327/a3cb327426f5482887ed61b3f57c815b.html\n" +
//            "/r/0273/106/pb/f/20240327/19812dd5bbb3443eb49bd3d6e0c11636.html\n" +
//            "/r/5169/106/pb/f/20240327/37fa68cae94e488e9da069778bbd3050.html\n" +
//            "/r/0074/106/pb/f/20240327/357f1b5b153f4a1bb325aba4429bd444.html\n" +
//            "/r/04b1/106/pb/f/20240327/cd198f1d374d4cfe8c521023e9e5ab5a.html\n" +
//            "/r/425c/106/pb/f/20240327/fd49794783634ba7a21ebf6de0cf2e54.html\n" +
//            "/r/1b31/106/pb/f/20240327/da290976e8f04d85b20f06c6d24e8729.html\n" +
//            "/r/35e2/106/pb/f/20240327/9fa27d3202924c528e7d8a246016901c.html\n" +
//            "/r/6be3/106/pb/f/20240327/abe22f67d6514787a993a27ba18ca58d.html\n" +
//            "/r/38ce/106/pb/f/20240327/ec396f2490cd45f996716d58d1b04962.html\n" +
//            "/r/f228/106/pb/f/20240327/a592f259ccdc451ea6148e8d35a840e2.html\n" +
//            "/r/c1a6/106/pb/f/20240327/0fbed559745445e1905fd1d48ec5dd26.html\n" +
//            "/r/849f/106/pb/f/20240327/6208be1b3bba43f5bfa284b9cc69621d.html\n" +
//            "/r/4941/106/pb/f/20240327/7ffcc66257374a0c8e956e91e25f63c0.html\n" +
//            "/r/8fef/106/pb/f/20240327/f31a697e1a474a31aa0f5c9099e700cd.html\n" +
//            "/r/a22f/106/pb/f/20240327/ecc68a2c5e3041f3b64496e393302a8e.html\n" +
//            "/r/955f/106/pb/f/20240327/19bb81a619984126aeadae70b1fbf2f6.html\n" +
//            "/r/32f6/106/pb/f/20240327/774f502037be43eeb78eb1f050d56a1b.html\n" +
//            "/r/b843/106/pb/f/20240327/8507334cd3ab442dbf80336577b33078.html\n" +
//            "/r/498e/106/pb/f/20240327/d1ffb8d7f8ee48c3a8b309f907c9112d.html\n" +
//            "/r/d9a9/106/pb/f/20240327/eddc1db526134676be63fe9ae2f0ab72.html\n" +
//            "/r/67b5/106/pb/f/20240327/d76a181ad684450092a122633b59078b.html\n" +
//            "/r/78da/106/pb/f/20240327/633644f64a6b4de7b487566122f9ac41.html\n" +
//            "/r/4ff2/106/pb/f/20240327/fb9811189737458cba4fb8f14dee5bc6.html\n" +
//            "/r/c7a2/106/pb/f/20240327/35e78e29296a4765b409e2e4e8940ba6.html\n" +
//            "/r/e4e2/106/pb/f/20240327/fc279dcda5f646deace7e25dd2057110.html\n" +
//            "/r/ee50/106/pb/f/20240327/84e661241704424cac703d125804f9ab.html\n" +
//            "/r/02f5/106/pb/f/20240327/f400793b35444ea1ba214d0e74234cb4.html\n" +
//            "/r/5c8e/106/pb/f/20240327/ebca85371560462889b6ce1f34f853ae.html\n" +
//            "/r/1956/106/pb/f/20240327/235d920ec69f446d9ae603564f8948fb.html\n" +
//            "/r/2bc7/106/pb/f/20240327/9d912b8f7aec40d8961b69b860e2c5f9.html\n" +
//            "/r/5653/106/pb/f/20240327/f9d5b16ecc084a23a42d80000997dafd.html\n" +
//            "/r/b2a0/106/pb/f/20240327/a313c0cc2461407bb02821e5c4a7fc5d.html\n" +
//            "/r/6195/106/pb/f/20240327/d8e57b96e5be41678a2d0172550ff280.html\n" +
//            "/r/4f9e/106/pb/f/20240327/de3fcdc64968473497fded8e40b931bb.html\n" +
//            "/r/8aa4/106/pb/f/20240327/dc9b57787fa947b4ac406c102768cdee.html\n" +
//            "/r/c2ea/106/pb/f/20240327/f6f881699271427bac4bea2f911a2547.html\n" +
//            "/r/713c/106/pb/f/20240327/dbcff5bad46244368abbae17a98d1acc.html\n" +
//            "/r/9260/106/pb/f/20240327/97c136dc6b22421b8d7add0bbc12c0de.html\n" +
//            "https://mp.weixin.qq.com/s/liDiD_eydue7-i7tc2v6WQ\n" +
//            "https://mp.weixin.qq.com/s/ogGLsVAiJn0ILxUpaVKmhw\n" +
//            "http://mp.weixin.qq.com/s?__biz=MTI0MDU3NDYwMQ==&mid=2657634004&idx=1&sn=42ad959799aa5adf9eef99db86f583f8&chksm=7bd587e103141c0e06781867a736ddd0e2df0b9ef32a28a3ad04954c8c05f04246b520a8afff&scene=0&xtrack=1#rd"
//    checkTitle(list)
    checkTitle("http://mp.weixin.qq.com/s?__biz=MzIyNDgzMjA3Nw==&mid=2247585010&idx=1&sn=12d1b004ede0df876f9ef6cf32c1920f&chksm=e944a8a3e873962f6aef1e4a8c6aaa0cdf1815956e901fcc835002e054d8e2f8dd18bb739131&scene=0&xtrack=1#rd")
//    writeBody("https://static-test-public.cdqlkj.cn/r/544e/106/pb/f/20240507/952312c4f4fd4b04aa5a9bb5d3ac36e8.html")
}

private fun checkTitle(list: String) {
    val httpClient = OkHttpClient.Builder().build()
    val file = File("E:\\temp\\test-article.txt")
    file.parentFile?.takeUnless { it.exists() }?.mkdirs()
    file.delete()
    file.createNewFile()
    list.split("\n").forEach {
        if (it.isBlank()) {
            return@forEach
        }
        val (url, content) = fetchUrlContent(it, httpClient)
        val isValid = runBlocking {
            ContentFilter().filter(content)
        }
        println("$isValid: $url")
        file.appendText("$isValid: $url\n")
    }
}

private fun fetchUrlContent(
    url: String,
    httpClient: OkHttpClient
): Pair<String, String?> {
    val url = if (url.startsWith("https") || url.startsWith("http")) {
        url
    } else {
        "https://static-test-public.cdqlkj.cn$url"
    }
    val content = httpClient.newCall(
        Request.Builder()
            .url(url)
            .get().build()
    ).execute().body?.string()
    return Pair(url, content)
}