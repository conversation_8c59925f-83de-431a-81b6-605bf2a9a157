package com.yupao.wxcollect.service.procedure.entity

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
interface ITaskResult {
    companion object {
        // 配置为空
        const val CODE_CONFIG_EMPTY = -1

        const val CODE_NO_NETWORK = -2

        const val CODE_REQUEST_FAILED = -3

        const val CODE_EXCEPTION = -4

        const val CODE_ARGUMENT_EMPTY = -5

        const val CODE_FAILED = -6

        const val CODE_CANCEL = -7

        const val CODE_CONFIG = -8

        const val CODE_WAIT_RETRY = -9

        const val CODE_SUCCEED = 0

        const val CODE_DATA_EMPTY = 1
    }

    val code: Int

    // 推广失败原因
    val msg: String?

    // 推广成功条数
    val count: Int?

    fun isOk(): Boolean {
        return code >= CODE_SUCCEED
    }
}