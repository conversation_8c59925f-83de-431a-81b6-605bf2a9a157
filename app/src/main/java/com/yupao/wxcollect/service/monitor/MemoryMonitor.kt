package com.yupao.wxcollect.service.monitor

import android.app.ActivityManager
import android.content.Context
import com.yupao.wxcollect.App
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 内存监控器
 * 每间隔5分钟检测一次系统内存状态
 *
 * <p>创建时间：2024/12/20</p>
 *
 * <AUTHOR>
 */
class MemoryMonitor {

    companion object {
        private const val TAG = "MemoryMonitor"

        // 检测间隔（5分钟）
        private const val MONITOR_INTERVAL = 5 * 60 * 1000L

        // 内存警告阈值（80%）
        private const val MEMORY_WARNING_THRESHOLD = 0.8f
    }

    private val coroutineScope = CoroutineScope(Dispatchers.IO + Job())
    private var monitorJob: Job? = null
    private val isMonitoring = AtomicBoolean(false)

    // 内存状态监听器列表
    private val memoryListeners = mutableListOf<MemoryStatusListener>()

    /**
     * 内存状态数据类
     */
    data class MemoryInfo(
        val totalMemoryKB: Long,
        val usedMemoryKB: Long,
        val availableMemoryKB: Long,
        val usagePercentage: Float,
        val timestamp: Long = System.currentTimeMillis()
    ) {
        val totalMemoryMB: Long get() = totalMemoryKB / 1024
        val usedMemoryMB: Long get() = usedMemoryKB / 1024
        val availableMemoryMB: Long get() = availableMemoryKB / 1024

        override fun toString(): String {
            return "MemoryInfo(总内存=${totalMemoryMB}MB, 已用=${usedMemoryMB}MB, 可用=${availableMemoryMB}MB, 使用率=${
                String.format(
                    "%.1f",
                    usagePercentage * 100
                )
            }%)"
        }
    }

    /**
     * 内存状态监听器接口
     */
    interface MemoryStatusListener {
        /**
         * 内存状态更新
         */
        fun onMemoryStatusUpdate(memoryInfo: MemoryInfo)

        /**
         * 内存警告（使用率超过80%）
         */
        fun onMemoryWarning(memoryInfo: MemoryInfo)

        /**
         * 内存状态正常
         */
        fun onMemoryNormal(memoryInfo: MemoryInfo)
    }

    /**
     * 开始内存监控
     */
    fun startMonitoring() {
        if (isMonitoring.getAndSet(true)) {
            YLog.w(TAG, "内存监控已在运行")
            return
        }

        YLog.i(TAG, "开始内存监控，检测间隔: ${MONITOR_INTERVAL / 1000}秒")

        monitorJob = coroutineScope.launch {
            while (isMonitoring.get()) {
                try {
                    // 检测内存状态
                    val memoryInfo = getSystemMemoryInfo()
                    if (memoryInfo != null) {
                        handleMemoryStatus(memoryInfo)
                    } else {
                        YLog.e(TAG, "获取内存信息失败")
                    }

                    // 等待下次检测
                    delay(MONITOR_INTERVAL)

                } catch (e: Exception) {
                    YLog.printException(TAG, e, "内存监控异常")
                    delay(MONITOR_INTERVAL) // 异常时也要等待，避免频繁重试
                }
            }
        }
    }

    /**
     * 停止内存监控
     */
    fun stopMonitoring() {
        if (!isMonitoring.getAndSet(false)) {
            YLog.w(TAG, "内存监控未在运行")
            return
        }

        monitorJob?.cancel()
        monitorJob = null

        YLog.i(TAG, "内存监控已停止")
    }

    /**
     * 添加内存状态监听器
     */
    fun addMemoryListener(listener: MemoryStatusListener) {
        if (!memoryListeners.contains(listener)) {
            memoryListeners.add(listener)
            YLog.i(TAG, "添加内存监听器")
        }
    }

    /**
     * 移除内存状态监听器
     */
    fun removeMemoryListener(listener: MemoryStatusListener) {
        if (memoryListeners.remove(listener)) {
            YLog.i(TAG, "移除内存监听器")
        }
    }

    /**
     * 手动检测内存状态
     */
    suspend fun checkMemoryStatus(): MemoryInfo? {
        return getSystemMemoryInfo()
    }

    /**
     * 获取系统内存信息
     * 基于您提供的方法进行改进
     */
    private suspend fun getSystemMemoryInfo(): MemoryInfo? {
        return withContext(Dispatchers.IO) {
            kotlin.runCatching {
                // 方法1：使用 ActivityManager
                val am =
                    App.getContext()?.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                val memInfo = ActivityManager.MemoryInfo()
                am.getMemoryInfo(memInfo)

                val totalMemoryKB = memInfo.totalMem / 1024
                val availableMemoryKB = memInfo.availMem / 1024
                val usedMemoryKB = totalMemoryKB - availableMemoryKB
                val usagePercentage = usedMemoryKB.toFloat() / totalMemoryKB.toFloat()

                MemoryInfo(
                    totalMemoryKB = totalMemoryKB,
                    usedMemoryKB = usedMemoryKB,
                    availableMemoryKB = availableMemoryKB,
                    usagePercentage = usagePercentage
                )
            }.getOrElse {
                YLog.w(TAG, "ActivityManager 方法失败，尝试 /proc/meminfo 方法")

                // 方法2：读取 /proc/meminfo
                try {
                    val memMap = fetchMemInfoMap()
                    val memAvailable =
                        memMap["MemAvailable"]?.firstOrNull()?.split(" ")?.firstOrNull()
                            ?.toLongOrNull()
                    val memTotal =
                        memMap["MemTotal"]?.firstOrNull()?.split(" ")?.firstOrNull()?.toLongOrNull()

                    if (memAvailable != null && memTotal != null) {
                        val usedMemoryKB = memTotal - memAvailable
                        val usagePercentage = usedMemoryKB.toFloat() / memTotal.toFloat()

                        MemoryInfo(
                            totalMemoryKB = memTotal,
                            usedMemoryKB = usedMemoryKB,
                            availableMemoryKB = memAvailable,
                            usagePercentage = usagePercentage
                        )
                    } else {
                        YLog.e(TAG, "无法解析 /proc/meminfo 数据")
                        null
                    }
                } catch (e: Exception) {
                    YLog.printException(TAG, e, "读取 /proc/meminfo 失败")
                    null
                }
            }
        }
    }

    /**
     * 读取 /proc/meminfo 文件
     */
    private fun fetchMemInfoMap(): Map<String, List<String>> {
        val result = mutableMapOf<String, MutableList<String>>()

        try {
            val process = Runtime.getRuntime().exec("cat /proc/meminfo")
            val reader = BufferedReader(InputStreamReader(process.inputStream))

            reader.useLines { lines ->
                lines.forEach { line ->
                    val parts = line.split(":")
                    if (parts.size >= 2) {
                        val key = parts[0].trim()
                        val value = parts[1].trim()
                        result.getOrPut(key) { mutableListOf() }.add(value)
                    }
                }
            }

            process.waitFor()
        } catch (e: Exception) {
            YLog.printException(TAG, e, "执行 cat /proc/meminfo 失败")
        }

        return result
    }

    /**
     * 处理内存状态
     */
    private suspend fun handleMemoryStatus(memoryInfo: MemoryInfo) {
        withContext(Dispatchers.Main) {
            YLog.i(TAG, "内存状态: $memoryInfo")

            // 通知所有监听器
            memoryListeners.forEach { listener ->
                try {
                    listener.onMemoryStatusUpdate(memoryInfo)
                    // 根据使用率触发不同级别的回调
                    when {
                        memoryInfo.usagePercentage >= MEMORY_WARNING_THRESHOLD -> {
                            listener.onMemoryWarning(memoryInfo)
                        }

                        else -> {
                            listener.onMemoryNormal(memoryInfo)
                        }
                    }
                } catch (e: Exception) {
                    YLog.printException(TAG, e, "通知内存监听器失败")
                }
            }
        }
    }

    /**
     * 获取当前监控状态
     */
    fun isMonitoring(): Boolean {
        return isMonitoring.get()
    }

    /**
     * 销毁监控器
     */
    fun destroy() {
        stopMonitoring()
        memoryListeners.clear()
        YLog.i(TAG, "内存监控器已销毁")
    }
}
