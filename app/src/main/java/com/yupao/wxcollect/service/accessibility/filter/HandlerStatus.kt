package com.yupao.wxcollect.service.accessibility.filter

import androidx.annotation.IntDef

/**
 * <AUTHOR>
 * @Date 2023/5/9/009 15:24
 * @Description
 */

@IntDef(
    HandlerStatus.BASE,
)
@Retention(AnnotationRetention.SOURCE)
annotation class HandlerStatus {

    companion object {
        internal const val BASE = 1
        const val IDLE = BASE
        const val DISPATCH = BASE.shl(1)
        const val WAIT_RUNNING = BASE.shl(2)
        const val RUNNING = BASE.shl(3)
        const val SUCCEED = BASE.shl(4)
        const val FAILED = BASE.shl(5)
    }
}

/**
 * 任务状态
 */
interface IHandlerStatus {
    @HandlerStatus var status: Int
}