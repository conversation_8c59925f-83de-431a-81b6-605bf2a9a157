package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.entity.director.TaskEntity
import com.yupao.wxcollect.service.procedure.task.TaskCode
import com.yupao.ylog.YLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * CheckStatusChangedObserver 测试类
 * 用于测试任务超时监控功能
 */
object CheckStatusChangedObserverTest {
    
    private const val TAG = "CheckStatusObserverTest"
    
    /**
     * 测试群聊消息任务超时
     */
    fun testGroupChatTimeout() = runBlocking {
        YLog.i(TAG, "=== 测试群聊消息任务超时 ===")
        
        val observer = CheckStatusChangedObserver()
        
        // 创建测试配置
        val config = createTestConfig(
            taskCode = TaskCode.WxGroupChat,
            taskName = "群聊天记录采集",
            timeoutTime = 3000L // 3秒超时
        )
        
        // 模拟任务开始执行
        YLog.i(TAG, "模拟任务开始执行")
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-001"))
        
        // 等待超时
        YLog.i(TAG, "等待任务超时...")
        delay(4000L) // 等待4秒，确保超时
        
        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 群聊消息任务超时测试完成 ===")
    }
    
    /**
     * 测试好友申请任务正常完成
     */
    fun testFriendApplyNormalComplete() = runBlocking {
        YLog.i(TAG, "=== 测试好友申请任务正常完成 ===")
        
        val observer = CheckStatusChangedObserver()
        
        // 创建测试配置
        val config = createTestConfig(
            taskCode = TaskCode.WxFriendApply,
            taskName = "好友申请采集",
            timeoutTime = 5000L // 5秒超时
        )
        
        // 模拟任务开始执行
        YLog.i(TAG, "模拟任务开始执行")
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-002"))
        
        // 等待2秒后模拟任务完成
        delay(2000L)
        YLog.i(TAG, "模拟任务完成")
        
        // 创建任务完成状态
        val taskCompleted = TrainExecutorStatus.TaskCompleted(
            config = config.configList.first(),
            result = createMockTaskResult(),
            isNotifyDirectorIfFailed = false
        )
        
        observer.onChanged(config, taskCompleted)
        
        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 好友申请任务正常完成测试完成 ===")
    }
    
    /**
     * 测试多个任务，其中一个超时
     */
    fun testMultipleTasksWithOneTimeout() = runBlocking {
        YLog.i(TAG, "=== 测试多个任务，其中一个超时 ===")
        
        val observer = CheckStatusChangedObserver()
        
        // 创建多个任务的配置
        val config = createMultipleTasksConfig()
        
        // 模拟任务开始执行
        YLog.i(TAG, "模拟多个任务开始执行")
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-003"))
        
        // 等待2秒后完成第一个任务
        delay(2000L)
        YLog.i(TAG, "完成第一个任务（群聊好友）")
        val firstTaskCompleted = TrainExecutorStatus.TaskCompleted(
            config = config.configList[0],
            result = createMockTaskResult(),
            isNotifyDirectorIfFailed = false
        )
        observer.onChanged(config, firstTaskCompleted)
        
        // 继续等待，让第二个任务超时
        YLog.i(TAG, "等待第二个任务超时...")
        delay(4000L)
        
        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 多个任务测试完成 ===")
    }
    
    /**
     * 测试任务失败情况
     */
    fun testTaskFailed() = runBlocking {
        YLog.i(TAG, "=== 测试任务失败情况 ===")
        
        val observer = CheckStatusChangedObserver()
        
        val config = createTestConfig(
            taskCode = TaskCode.WxGroupFriend,
            taskName = "群好友采集",
            timeoutTime = 5000L
        )
        
        // 模拟任务开始执行
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-004"))
        
        // 等待1秒后模拟任务失败
        delay(1000L)
        YLog.i(TAG, "模拟任务失败")
        observer.onChanged(config, TrainExecutorStatus.Failed("模拟的任务失败"))
        
        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 任务失败测试完成 ===")
    }
    
    /**
     * 运行所有测试
     */
    fun runAllTests() = runBlocking {
        YLog.i(TAG, "开始运行所有测试...")
        
        testGroupChatTimeout()
        delay(1000L)
        
        testFriendApplyNormalComplete()
        delay(1000L)
        
        testMultipleTasksWithOneTimeout()
        delay(1000L)
        
        testTaskFailed()
        
        YLog.i(TAG, "所有测试完成")
    }
    
    /**
     * 创建测试配置
     */
    private fun createTestConfig(
        taskCode: String,
        taskName: String,
        timeoutTime: Long
    ): TaskExecutorConfig {
        val taskEntity = TaskEntity(
            taskCode = taskCode,
            taskName = taskName,
            timeoutTime = timeoutTime,
            invalidTime = timeoutTime * 2
        )
        
        val collectTaskEntity = CollectTaskEntity(
            unique = "test-unique-${System.currentTimeMillis()}",
            entity = taskEntity
        )
        
        return TaskExecutorConfig(
            databasePath = "/test/path",
            password = "test-password",
            appType = "3",
            configList = listOf(collectTaskEntity),
            isCopyForce = false,
            timeoutTime = timeoutTime,
            invalidTime = timeoutTime * 2
        )
    }
    
    /**
     * 创建多个任务的配置
     */
    private fun createMultipleTasksConfig(): TaskExecutorConfig {
        val task1 = CollectTaskEntity(
            unique = "test-unique-1-${System.currentTimeMillis()}",
            entity = TaskEntity(
                taskCode = TaskCode.WxGroupFriend,
                taskName = "群好友采集",
                timeoutTime = 3000L,
                invalidTime = 6000L
            )
        )
        
        val task2 = CollectTaskEntity(
            unique = "test-unique-2-${System.currentTimeMillis()}",
            entity = TaskEntity(
                taskCode = TaskCode.WxFriendApply,
                taskName = "好友申请采集",
                timeoutTime = 3000L,
                invalidTime = 6000L
            )
        )
        
        return TaskExecutorConfig(
            databasePath = "/test/path",
            password = "test-password",
            appType = "3",
            configList = listOf(task1, task2),
            isCopyForce = false,
            timeoutTime = 3000L,
            invalidTime = 6000L
        )
    }
    
    /**
     * 创建模拟的任务结果
     */
    private fun createMockTaskResult(): com.yupao.wxcollect.service.procedure.entity.ITaskResult {
        return object : com.yupao.wxcollect.service.procedure.entity.ITaskResult {
            override val isSucceed: Boolean = true
            override val errorMsg: String? = null
        }
    }
}
