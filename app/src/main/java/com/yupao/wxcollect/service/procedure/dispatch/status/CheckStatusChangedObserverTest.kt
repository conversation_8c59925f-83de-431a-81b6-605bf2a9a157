package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.procedure.dispatch.ConflictStrategy
import com.yupao.wxcollect.service.procedure.entity.ITaskResult
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.director.CollectEntity
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.task.TaskCode
import com.yupao.ylog.YLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

/**
 * CheckStatusChangedObserver 测试类
 * 用于测试任务超时监控功能
 */
object CheckStatusChangedObserverTest {

    private const val TAG = "CheckStatusObserverTest"

    /**
     * 测试群聊消息任务超时
     */
    fun testGroupChatTimeout() = runBlocking {
        YLog.i(TAG, "=== 测试群聊消息任务超时 ===")

        val observer = CheckStatusChangedObserver()

        // 创建测试配置
        val config = createTestConfig(
            taskCode = TaskCode.WxGroupChat,
            taskName = "群聊天记录采集",
            timeoutTime = 3000L // 3秒超时
        )
        // 模拟任务开始执行
        YLog.i(TAG, "模拟任务开始执行")
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-001"))

        // 等待超时
        YLog.i(TAG, "等待任务超时...")
        delay(4000L) // 等待4秒，确保超时

        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 群聊消息任务超时测试完成 ===")
    }

    /**
     * 测试好友申请任务正常完成
     */
    fun testFriendApplyNormalComplete() = runBlocking {
        YLog.i(TAG, "=== 测试好友申请任务正常完成 ===")

        val observer = CheckStatusChangedObserver()

        // 创建测试配置
        val config = createTestConfig(
            taskCode = TaskCode.WxFriendApply,
            taskName = "好友申请采集",
            timeoutTime = 5000L // 5秒超时
        )

        // 模拟任务开始执行
        YLog.i(TAG, "模拟任务开始执行")
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-002"))

        // 等待2秒后模拟任务完成
        delay(2000L)
        YLog.i(TAG, "模拟任务完成")

        // 创建任务完成状态
//        val taskCompleted = TrainExecutorStatus.TaskCompleted(
//            config = config.configList.first(),
//            result = createMockTaskResult(),
//            isNotifyDirectorIfFailed = false
//        )
//
//        observer.onChanged(config, taskCompleted)

        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 好友申请任务正常完成测试完成 ===")
    }

    /**
     * 测试任务失败情况
     */
    fun testTaskFailed() = runBlocking {
        YLog.i(TAG, "=== 测试任务失败情况 ===")

        val observer = CheckStatusChangedObserver()

        val config = createTestConfig(
            taskCode = TaskCode.WxGroupFriend,
            taskName = "群好友采集",
            timeoutTime = 5000L
        )

        // 模拟任务开始执行
        observer.onChanged(config, TrainExecutorStatus.Execute("test-train-004"))

        // 等待1秒后模拟任务失败
        delay(1000L)
        YLog.i(TAG, "模拟任务失败")
        observer.onChanged(config, TrainExecutorStatus.Failed("模拟的任务失败"))

        YLog.i(TAG, "监控状态: ${observer.getMonitoringStatus()}")
        YLog.i(TAG, "=== 任务失败测试完成 ===")
    }

    /**
     * 运行所有测试
     */
    fun runAllTests() = runBlocking {
        YLog.i(TAG, "开始运行所有测试...")

        testGroupChatTimeout()
        delay(1000L)

        testFriendApplyNormalComplete()
        delay(1000L)

        delay(1000L)

        testTaskFailed()

        YLog.i(TAG, "所有测试完成")
    }

    /**
     * 创建测试配置
     */
    private fun createTestConfig(
        taskCode: String,
        taskName: String,
        timeoutTime: Long
    ): TaskExecutorConfig {
        val collectEntity = com.yupao.wxcollect.service.procedure.entity.director.CollectEntity(
            unique = "test-unique-${System.currentTimeMillis()}",
            startTime = System.currentTimeMillis(),
            taskId = "test-task-id",
            taskUnique = "test-task-unique-${System.currentTimeMillis()}",
            taskName = taskName,
            taskCode = taskCode,
            taskInterval = 60, // 60秒间隔
            query = com.yupao.wxcollect.service.procedure.entity.director.QueryEntity(
                limitLine = 1000,
                sql = "SELECT * FROM test_table",
                keyList = listOf("id", "name"),
                queryTime = 3600 // 1小时
            ),
            condition = com.yupao.wxcollect.service.procedure.entity.director.ConditionEntity(
                appType = "3",
                sort = 1
            ),
            cycleCount = 1,
            database = com.yupao.wxcollect.service.procedure.entity.director.DatabaseEntity(
                isCopyForce = false
            ),
            extInfo = null,
            timeoutTime = timeoutTime,
            invalidTime = timeoutTime * 2,
            status = com.yupao.wxcollect.service.procedure.entity.director.TaskStatus.Ready(
                "test-unique-${System.currentTimeMillis()}"
            )
        )

        val collectTaskEntity = CollectTaskEntity(
            entity = collectEntity
        )

        return TaskExecutorConfig(
            databasePath = "/test/path",
            password = "test-password",
            appType = "3",
            configList = listOf(collectTaskEntity),
            isCopyForce = false,
            timeoutTime = timeoutTime,
            invalidTime = timeoutTime * 2,
            strategy = ConflictStrategy.Delay
        )
    }
}

fun main() {
    CheckStatusChangedObserverTest.testGroupChatTimeout()
}
