package com.yupao.wxcollect.service.accessibility.filter

import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.yupao.wxcollect.service.accessibility.process.IWechatTask
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/21/021 14:41
 * @Description
 */
open class PageHandler(
    // 包名, null：不限制包名
    private val packageName: String?,
    // 页面, null：不限制类名
    override val page: List<String>?,
    // 该页面处理的相关View集合
    override val viewHandlerList: MutableList<IViewHandler>?,
    // 最大调用次数
    override val maxPerformCount: Int = 1,
    // 优先级
    override var priority: Int = IPageHandler.DEFAULT_PRIORITY,
    private val eventType: Int = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED,
    private val viewPositionList: List<ViewPosition>? = null,
): IPageHandler {
    override var status: Int = HandlerStatus.IDLE

    var cacheEvent: AccessibilityEvent? = null

    var cacheNodeInfo: AccessibilityNodeInfo? = null

    @Volatile
    private var isAccept: Boolean = false

    @Volatile
    private var performCount: Int = 0

    init {
        viewHandlerList?.forEach {
            it.pageHandler = this
        }
    }

    override suspend fun acceptEventType(event: AccessibilityEvent?): Boolean{
        val type = event?.eventType ?: 0
        return type and eventType == type
    }

    override suspend fun filter(event: AccessibilityEvent?): Boolean {
        event ?: return false
        val page = this.page
        if (page?.size == 1 && page.firstOrNull() == IWechatTask.wechatDialogClass) {
            if (IWechatTask.isWechatDialogClass(event)) {
                return true
            }
        }
        if (packageName == null && page == null) {
            return true
        }
        if (packageName != null && event.packageName != packageName) {
            return false
        }
        if (page == null) {
            return true
        }
        val className = event.className
        return page.contains(className)
    }

    private fun initViewPosition(nodeInfo: AccessibilityNodeInfo?) {
        viewPositionList?.forEach {
            it.transform(nodeInfo)
        }
    }

    override suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean {
        if (acceptEventType(event)) {
            isAccept = filter(event)
            YLog.i("PageHandler", "dispatchEvent: page = ${event?.className}, isAccept = $isAccept, eventType = ${event?.eventType}," +
                    "eventType = $eventType, ${(event?.eventType ?: 0) and eventType}")
        }
        if (isAccept) {
            status = HandlerStatus.DISPATCH
            cacheEvent = event
            val source = event?.source
            if (source != null) {
                initViewPosition(source)
                cacheNodeInfo = source
            }
            performCount++
            val list = viewHandlerList
            val handler = list?.firstOrNull()
            YLog.i("PageHandler", "dispatchEvent: perform handler = $handler")
            val result = performEvent(handler, list, event)
            status = if (result) {
                HandlerStatus.SUCCEED
            } else {
                HandlerStatus.FAILED
            }
            return result
        }
        return isAccept
    }

    private suspend fun performEvent(
        handler: IViewHandler?,
        list: MutableList<IViewHandler>?,
        event: AccessibilityEvent?,
    ): Boolean {
        handler ?: return true
        list ?: return true
        status = HandlerStatus.RUNNING
        handler.dispatchEvent(event).takeIf { it }?.run {
            val isRemove = handler.isRemove()
            YLog.i("PageHandler", "performView: page = $page, isRemove = $isRemove, " +
                    "size = ${list.size}, $handler")
            if (isRemove) {
                list.remove(handler)
                list.firstOrNull()?.takeIf { it.delayTimeAfterPrev >= 0 }
                    ?.run {
                        performEvent(this, list, event)
                    }
            }
            return true
        }
        return false
    }

    override fun isRemove(): Boolean {
        return performCount >= maxPerformCount && viewHandlerList.isNullOrEmpty()
    }
}