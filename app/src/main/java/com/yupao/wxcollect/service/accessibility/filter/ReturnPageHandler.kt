package com.yupao.wxcollect.service.accessibility.filter

import android.view.accessibility.AccessibilityEvent
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/4/21/021 14:41
 * @Description 处于[excludePage]时忽略，其他页面在短暂停留之后都会直接返还
 */
open class ReturnPageHandler(
    // 包名, null：不限制包名
    private val packageName: String?,
    // 不执行的页面, null：不限制类名
    private val excludePage: List<String>?,
    // 历史页面
    private val historyPage: List<String>?,
    // 该页面处理的相关View集合
    override val viewHandlerList: MutableList<IViewHandler>?,
    // 最大调用次数
    override val maxPerformCount: Int = Int.MAX_VALUE,
    override var priority: Int = Int.MAX_VALUE
): PageHandler(
    packageName = packageName,
    page = null,
    viewHandlerList = viewHandlerList,
    maxPerformCount = maxPerformCount,
    priority = priority,
    viewPositionList = null,
) {
    private var currReturnPage: String? = null

    override suspend fun filter(event: AccessibilityEvent?): Boolean {
        event ?: return false
        if (packageName == null && excludePage == null) {
            return true
        }
        if (packageName != null && event.packageName != packageName) {
            return false
        }
        if (excludePage == null) {
            return true
        }
        val className = event.className
        return !excludePage.contains(className)
    }

    override suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean {
        val acceptEventType = acceptEventType(event)
        if (acceptEventType) {
            val className = event?.className
            if (filter(event)) {
                delay(IViewHandler.DEFAULT_DELAY_LONG_TIME)
                YLog.i("ReturnPageHandler", "Close page soon. page: $className")
                val result = finishPage(className?.toString())
                if (result == null) {
                    checkPageReturn(2000, className?.toString())
                }
                return result == null
            } else {
                currReturnPage = null
                YLog.i("ReturnPageHandler", "ignore return page: $className")
            }
        }
        return false
    }

    private fun finishPage(page: String?): String? {
        page ?: return "ReturnPageHandler, failed page: $page"
        val list = CmdUtil.execCmdWithBack("dumpsys window | grep mCurrentF")
        for (item in list) {
            if (item.contains(page)) {
                return CmdUtil.execCmd("input keyevent 4")
            }
        }
        return "ReturnPageHandler, not found page: $page, list = $list"
    }

    private suspend fun checkPageReturn(delayTime: Long, page: String?) {
        currReturnPage = page
        if (historyPage == null) {
            return
        }
        ProcessLifecycleOwner.get().lifecycleScope.launch(Dispatchers.IO) {
            delay(delayTime)
            val finishPage = currReturnPage
            if (historyPage.lastOrNull() == finishPage && finishPage != null) {
                YLog.i("ReturnPageHandler", "delay ${delayTime}ms finish page $finishPage retry")
                finishPage(finishPage)
            }
        }
    }
}