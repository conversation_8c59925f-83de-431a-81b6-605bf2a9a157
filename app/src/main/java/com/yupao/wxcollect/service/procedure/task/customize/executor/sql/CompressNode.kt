package com.yupao.wxcollect.service.procedure.task.customize.executor.sql

import com.yupao.ylog.YLog
import org.apache.commons.compress.archivers.sevenz.SevenZOutputFile
import java.io.File

/**
 * 文件压缩7z
 *
 * <p>创建时间：2024/1/4/004</p>
 *
 * <AUTHOR>
 */
class CompressNode (
    // /path/to/source/dir
    private val sourceDir: String?,
    // /path/to/output/file.7z
    private val outputFile: String?,
) {

    companion object {
        private const val TAG = "CompressNode"
    }

    fun perform(): String {
        if (sourceDir.isNullOrBlank() || outputFile.isNullOrBlank()) {
            throw RuntimeException("empty file: $sourceDir, $outputFile")
        }
        val sourceDir = File(this.sourceDir)
        if (!sourceDir.exists()) {
            throw RuntimeException("${this.sourceDir} not exist.")
        }
        val outputFile = File(this.outputFile)
        if (outputFile.parentFile?.takeUnless {
                it.exists()
            }?.mkdirs() == false) {
            YLog.w(TAG, "${this.outputFile} parent file create failed.")
        }
        SevenZOutputFile(outputFile).use { outArchive ->
            if (sourceDir.isDirectory) {
                sourceDir.walkTopDown().forEach { file ->
                    if (file != sourceDir) {
                        writeFile(file, sourceDir, outArchive)
                    }
                }
            } else {
                writeFile(sourceDir, sourceDir, outArchive)
            }
        }
        return outputFile.absolutePath
    }

    private fun writeFile(
        file: File,
        sourceDir: File,
        outArchive: SevenZOutputFile
    ) {
        var entryName = file.relativeTo(sourceDir).path
        if (entryName.isNullOrBlank()) {
            entryName = file.name
        }
        YLog.d("writeFile file: $file, entryName: $entryName")
        val entry = outArchive.createArchiveEntry(file, entryName)
        outArchive.putArchiveEntry(entry)
        file.inputStream().use { input ->
            outArchive.write(input)
        }
        outArchive.closeArchiveEntry()
    }
}