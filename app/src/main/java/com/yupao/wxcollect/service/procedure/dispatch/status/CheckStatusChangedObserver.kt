package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.procedure.entity.ITaskConfig
import com.yupao.wxcollect.service.procedure.entity.ITaskResult
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.entity.director.ICollectTaskStatus
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatus
import com.yupao.wxcollect.transmit.director.handle.CollectStatusChangedProxy

/**
 * 导演App状态观察者
 *
 * <p>创建时间：2024/6/11/011</p>
 *
 * <AUTHOR>
 */
class DirectorStatusChangedObserver: IStatusChangedObserver {

    private val commStatusChangedProxy by lazy { CollectStatusChangedProxy() }

    override suspend fun onChanged(config: TaskExecutorConfig, status: TrainExecutorStatus) {
        if (status is TrainExecutorStatus.Execute) {
            notifyStatusChanged(config.configList) {
                TaskStatus.Executed(it, status.trainUnique)
            }
            return
        }
        if (status is TrainExecutorStatus.CopyDatabase) {
            notifyStatusChanged(config.configList) {
                TaskStatus.CopyDb(it)
            }
            return
        }
        if (status is TrainExecutorStatus.TaskCompleted) {
            val result = status.result
            if (status.isNotifyDirectorIfFailed && !result.isOk()) {
                notifyStatusChanged(listOf(status.config)) {
                    when (result.code) {
                        ITaskResult.CODE_CANCEL -> {
                            // ignore: 取消任务，由导演App控制任务调度，不需要再去通知导演App
                            null
                        }
                        ITaskResult.CODE_EXCEPTION -> {
                            TaskStatus.Failed(it, TaskStatus.Failed.Exception, result.msg)
                        }
                        ITaskResult.CODE_CONFIG -> {
                            TaskStatus.Failed(it, TaskStatus.Failed.Config, result.msg)
                        }
                        else -> {
                            TaskStatus.Failed(it, TaskStatus.Failed.Unknown, result.msg)
                        }
                    }
                }
            }
        }
        if (status is TrainExecutorStatus.Failed) {
            notifyStatusChanged(config.configList) {
                TaskStatus.Failed(it, TaskStatus.Failed.Exception, status.errorMsg)
            }
            return
        }
    }

    private suspend fun notifyStatusChanged(config: List<ITaskConfig>, toStatus: (unique: String) -> ICollectTaskStatus?) {
        // 只有采集任务才支持上报状态
        val entityList = config.mapNotNull {
            if (it is CollectTaskEntity) {
                val status = toStatus(it.unique)
                if (status != null) {
                    it.entity.status = status
                    return@mapNotNull it.entity
                }
            }
            return@mapNotNull null
        }
        commStatusChangedProxy.onChanged(entityList)
    }
}