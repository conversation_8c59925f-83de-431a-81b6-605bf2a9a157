package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.transmit.director.handle.CollectStatusChangedProxy

/**
 * 校验任务是否超时（群聊消息、好友申请）
 *
 * <p>创建时间：2024/6/11/011</p>
 *
 */
class CheckStatusChangedObserver : IStatusChangedObserver {

    private val commStatusChangedProxy by lazy { CollectStatusChangedProxy() }

    override suspend fun onChanged(config: TaskExecutorConfig, status: TrainExecutorStatus) {
        if (status is TrainExecutorStatus.Execute) {

            return
        }
        if (status is TrainExecutorStatus.TaskCompleted) {


        }
        if (status is TrainExecutorStatus.Failed) {

            return
        }
    }

}