package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.SystemStatusManager
import com.yupao.wxcollect.service.procedure.entity.ITaskConfig
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.task.TaskCode
import com.yupao.wxcollect.transmit.director.handle.CollectStatusChangedProxy
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 校验任务是否超时（群聊消息、好友申请）
 *
 * <p>创建时间：2024/6/11/011</p>
 *
 */
class CheckStatusChangedObserver : IStatusChangedObserver {

    companion object {
        private const val TAG = "CheckStatusChangedObserver"

        // 目标任务类型：群聊消息和好友申请
        private val TARGET_TASK_CODES = setOf(
            TaskCode.WxGroupChat,    // 群聊天记录
            TaskCode.WxFriendApply   // 微信好友申请采集
        )
    }

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.IO + Job())

    // 正在执行的任务记录：unique -> 执行开始时间
    private val executingTasks = ConcurrentHashMap<String, Long>()

    // 超时检查任务：unique -> Job
    private val timeoutJobs = ConcurrentHashMap<String, Job>()

    // 是否已有超时提醒正在显示
    private val hasTimeoutAlert = AtomicBoolean(false)

    override suspend fun onChanged(config: TaskExecutorConfig, status: TrainExecutorStatus) {
        if(SystemStatusManager.hasTaskTimeout())return
        when (status) {
            is TrainExecutorStatus.Execute -> {
                handleExecuteStatus(config, status)
            }
            is TrainExecutorStatus.TaskCompleted -> {
                handleTaskCompleted(config, status)
            }
            is TrainExecutorStatus.Failed -> {
                handleTaskFailed(config, status)
            }
            is TrainExecutorStatus.Canceled -> {
                handleTaskCanceled(config, status)
            }
            else -> {
                // 其他状态不处理
            }
        }
    }

    /**
     * 处理任务开始执行状态
     */
    private fun handleExecuteStatus(config: TaskExecutorConfig, status: TrainExecutorStatus.Execute) {
        // 筛选出群聊消息和好友申请任务
        val targetTasks = filterTargetTasks(config.configList)
        if (targetTasks.isEmpty()) {
            YLog.d(TAG, "没有需要监控的任务类型")
            return
        }

        val currentTime = TimeUtil.getCurrTime()
        YLog.i(TAG, "开始监控任务超时，任务数量: ${targetTasks.size}")

        targetTasks.forEach { task ->
            val unique = task.unique
            val timeoutTime = task.entity.timeoutTime.takeIf { it > 0 } ?: config.timeoutTime

            // 记录任务开始执行时间
            executingTasks[unique] = currentTime

            YLog.i(TAG, "任务开始执行: $unique, 超时时间: ${timeoutTime}ms")

            // 启动超时检查任务
            val timeoutJob = coroutineScope.launch {
                try {
                    delay(timeoutTime)

                    // 检查任务是否还在执行中
                    if (executingTasks.containsKey(unique)) {
                        handleTaskTimeout(unique, task, timeoutTime)
                    }
                } catch (e: Exception) {
                    YLog.printException(TAG, e, "超时检查任务异常: $unique")
                }
            }

            timeoutJobs[unique] = timeoutJob
        }
    }

    /**
     * 处理任务完成状态
     */
    private fun handleTaskCompleted(config: TaskExecutorConfig, status: TrainExecutorStatus.TaskCompleted) {
        val unique = status.config.unique

        // 移除执行记录和超时检查任务
        removeTaskMonitoring(unique)

        YLog.i(TAG, "任务完成: $unique")
    }

    /**
     * 处理任务失败状态
     */
    private fun handleTaskFailed(config: TaskExecutorConfig, status: TrainExecutorStatus.Failed) {
        // 清理所有相关任务的监控
        config.configList.forEach { task ->
            removeTaskMonitoring(task.unique)
        }

        YLog.i(TAG, "任务失败，清理监控: ${status.errorMsg}")
    }

    /**
     * 处理任务取消状态
     */
    private fun handleTaskCanceled(config: TaskExecutorConfig, status: TrainExecutorStatus.Canceled) {
        // 清理所有相关任务的监控
        config.configList.forEach { task ->
            removeTaskMonitoring(task.unique)
        }

        YLog.i(TAG, "任务取消，清理监控: ${status.reason}")
    }

    /**
     * 筛选出目标任务（群聊消息和好友申请）
     */
    private fun filterTargetTasks(configList: List<ITaskConfig>): List<CollectTaskEntity> {
        return configList.filterIsInstance<CollectTaskEntity>()
            .filter { task ->
                task.entity.taskCode in TARGET_TASK_CODES
            }
    }

    /**
     * 处理任务超时
     */
    private suspend fun handleTaskTimeout(unique: String, task: CollectTaskEntity, timeoutTime: Long) {
        YLog.w(TAG, "任务超时: $unique, 超时时间: ${timeoutTime}ms")

        // 如果当前已有超时提醒，则不再显示新的提醒
        if (hasTimeoutAlert.getAndSet(true)) {
            YLog.i(TAG, "已有超时提醒正在显示，跳过任务: $unique")
            return
        }

        try {
            // 生成超时提醒消息
            val taskName = task.entity.taskName
            val timeoutMinutes = timeoutTime / (60 * 1000)
            val message = "任务「$taskName」执行超时(${timeoutMinutes}分钟)，请检查网络或重启应用"

            // 通过 SystemStatusManager 显示超时提醒
            SystemStatusManager.addStatusMessage(
                SystemStatusManager.StatusType.TASK_TIMEOUT,
                message,
                priority = 9
            )

            YLog.w(TAG, "已显示超时提醒: $message")

        } catch (e: Exception) {
            YLog.printException(TAG, e, "显示超时提醒失败")
        } finally {
            // 移除任务监控
            removeTaskMonitoring(unique)
        }
    }

    /**
     * 移除任务监控
     */
    private fun removeTaskMonitoring(unique: String) {
        // 移除执行记录
        executingTasks.remove(unique)

        // 取消并移除超时检查任务
        timeoutJobs.remove(unique)?.cancel()

        // 如果没有正在监控的任务了，重置超时提醒标志
        if (executingTasks.isEmpty()) {
            hasTimeoutAlert.set(false)
            YLog.i(TAG, "所有监控任务已完成，重置超时提醒标志")
        }

        YLog.d(TAG, "移除任务监控: $unique, 剩余监控任务: ${executingTasks.size}")
    }

    /**
     * 获取当前监控状态（用于调试）
     */
    fun getMonitoringStatus(): String {
        return "正在监控任务数: ${executingTasks.size}, 是否有超时提醒: ${hasTimeoutAlert.get()}"
    }

}