package com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.group

import androidx.room.Dao
import androidx.room.Entity
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.PrimaryKey
import androidx.room.Query
import androidx.room.Update
import com.yupao.wxcollect.service.db.IDiffDao
import com.yupao.wxcollect.service.procedure.entity.db.CommDiffLocalModel

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/9/009</p>
 *
 * <AUTHOR>
 */
@Dao
interface GroupDiffDao : IDiffDao<GroupDiffLocalModel> {

    @Query("select * from group_comm_diff where id > (:minId) LIMIT :limit")
    override fun queryLimit(limit: Int, minId: Int): List<GroupDiffLocalModel>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override suspend fun insertAll(list: List<GroupDiffLocalModel>)

    @Query("DELETE FROM group_comm_diff")
    override fun clearTable()
}


@Entity(tableName = "group_comm_diff")
data class GroupDiffLocalModel(
    @PrimaryKey(autoGenerate = true)
    override var id: Int? = null,
): CommDiffLocalModel()