package com.yupao.wxcollect.service.procedure.task

import com.yupao.wxcollect.service.WechatUserManager
import com.yupao.wxcollect.service.db.IDatabaseHelper
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity
import com.yupao.wxcollect.service.procedure.entity.query.UserIdQueryEntity
import com.yupao.wxcollect.service.procedure.entity.query.UsernameQueryEntity
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/21/021 16:18
 * @Description 获取微信账号
 */
class WechatAccountTask(val databasePath: String) {
    companion object {
        private const val TAG = "WechatAccountTask"
    }

    private val userEntity by lazy {
        WechatUserManager.getUser(databasePath)?.let {
            UserEntity(
                alias = it.alias,
                nickname = it.nickname,
                username = it.username
            )
        }
    }

    fun taskType(): Int {
        return TaskType.FETCH_WECHAT_ACCOUNT
    }

    private suspend fun queryNickname(databaseHelper: IDatabaseHelper): String? {
        return databaseHelper.query(
            "select value from userinfo where id=4 and type=3",
            UsernameQueryEntity::class,
            TAG,
        )?.firstOrNull()?.value
    }

    private suspend fun queryUsername(databaseHelper: IDatabaseHelper): String? {
        return databaseHelper.query(
            "select value from userinfo where id=2 and type=3",
            UsernameQueryEntity::class,
            TAG,
        )?.firstOrNull()?.value
    }

    private suspend fun queryAlias(databaseHelper: IDatabaseHelper): String? {
        return databaseHelper.query(
            "select value from userinfo where id=42 and type=3",
            UsernameQueryEntity::class,
            TAG,
        )?.firstOrNull()?.value
    }

    suspend fun perform(
        databaseHelper: IDatabaseHelper?,
        alias: String? = null
    ): UserEntity? {
        YLog.i(TAG, "perform alias: $alias")
        val databaseHelp = databaseHelper ?: return null
        val nickname = queryNickname(databaseHelper)
        var queryName = (nickname ?: "").replace("'", "''")
        // 如果还是没有，则使用缓存用户名
        if (queryName.isEmpty() && !alias.isNullOrEmpty()) {
            queryName = userEntity?.nickname ?: ""
        }
        val queryUsername = queryUsername(databaseHelper)
        var userIdEntity = if (queryUsername.isNullOrEmpty()) {
            if (alias.isNullOrEmpty()) {
                databaseHelp.query(
                    "select * from rcontact where nickname='$queryName' and verifyFlag = 0 and type != 4",
                    UserIdQueryEntity::class,
                    TAG,
                )?.firstOrNull()
            } else {
                queryByAlias(alias, databaseHelp)
            }
        } else {
            databaseHelp.query(
                "select * from rcontact where username='$queryUsername' and verifyFlag = 0 and type != 4",
                UserIdQueryEntity::class,
                TAG,
            )?.firstOrNull()
        }
        if (userIdEntity == null && !alias.isNullOrEmpty()) {
            userIdEntity = queryByAlias(alias, databaseHelp)
        }
        val username = userIdEntity?.username ?: queryUsername
        val alias: String? = if (!userIdEntity?.alias.isNullOrEmpty()) {
            userIdEntity?.alias
        } else {
            val queryAlias = queryAlias(databaseHelper)
            if (!queryAlias.isNullOrEmpty()) {
                queryAlias
            } else {
                username
            }
        }
        val currUserEntity = userEntity
        val entity = UserEntity(
            username = username ?: currUserEntity?.username,
            nickname = nickname ?: userIdEntity?.nickname ?: currUserEntity?.nickname,
            alias = alias ?: currUserEntity?.alias
        )
        if (currUserEntity == null ||
            currUserEntity.username != entity.username ||
            currUserEntity.nickname != entity.nickname ||
            currUserEntity.alias != entity.alias
        ) {
            WechatUserManager.updateUser(databasePath, entity)
        }
        WechatUserManager.saveUserEntityForOtherProcess(databasePath, entity)
        return entity
    }

    private suspend fun queryByAlias(
        alias: String,
        databaseHelp: IDatabaseHelper
    ): UserIdQueryEntity? {
        YLog.i(TAG, "query by alias: $alias")
        var result = databaseHelp.query(
            "select * from rcontact where alias='$alias' and verifyFlag = 0 and type != 4",
            UserIdQueryEntity::class,
            TAG,
        )?.firstOrNull()
        if (result == null && alias.startsWith("wxid_")) {
            YLog.i(TAG, "query by username: $alias")
            result = databaseHelp.query(
                "select * from rcontact where username='$alias' and verifyFlag = 0 and type != 4",
                UserIdQueryEntity::class,
                TAG,
            )?.firstOrNull()
        }
        return result
    }
}