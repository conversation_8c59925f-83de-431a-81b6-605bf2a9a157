package com.yupao.wxcollect.service.procedure.task.official.transform

import com.yupao.wxcollect.service.procedure.entity.query.ArticleEntity
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.ylog.YLog

/**
 * 添加缓存在本地的文章
 *
 * <p>创建时间：2024/6/13/013</p>
 *
 * <AUTHOR>
 */
class AttachCacheTransform(
    private val appType: String,
    private val username: String,
    val resp: ReportRepository,
): IArticleTransform {
    companion object {
        private const val TAG = "AttachCacheTransform"
    }

    private var cacheList: List<ArticleEntity>? = null

    override suspend fun transform(list: List<ArticleEntity>): List<ArticleEntity> {
        val cacheList = resp.fetchCacheOfficialArticle(appType, username) ?: return list
        val urlList = list.map { it.url }
        val resultList = cacheList.groupBy {
            urlList.contains(it.url)
        }
        // 本次添加已经存在
        val addedList = resultList[true]
        resp.deleteArticle(appType, username, addedList)
        // 待添加
        val pendingAddList = resultList[false]
        pendingAddList?.forEach {
            YLog.d(TAG, "cache-item: ${it.nickname}, ${it.createTime}, ${it.articleUnique}, ${it.url}")
        }
        YLog.i(TAG, "cache size: ${cacheList.size}, pending add size: ${pendingAddList?.size},added size: ${addedList?.size}")
        this.cacheList = pendingAddList
        return list + (pendingAddList ?: listOf())
    }

    fun getCacheList() = cacheList
}