package com.yupao.wxcollect.service.procedure.dispatch.status

import com.yupao.wxcollect.service.procedure.dispatch.TaskTrainDispatcher
import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig
import com.yupao.ylog.YLog

/**
 * 状态分发
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
class TrainStatusDispatcher(
    flag: String,
    configSize: Int,
) {

    private val tag = "TrainStatusDispatcher:$flag"

    private val observerList = listOf(
        NetworkStatusChangedObserver(flag, configSize),
        DirectorStatusChangedObserver(),
        RecordStatusChangedObserver(flag),
        CheckStatusChangedObserver()
    )

    suspend fun onChange(config: TaskExecutorConfig, status: TrainExecutorStatus) {
        YLog.i(tag, "onChange ${status.javaClass.simpleName}")
        // 当任务完成时，唤醒任务分发
        if (status is IFinishedStatus) {
            TaskTrainDispatcher.instance.wakeup()
        }
        observerList.forEach {
            it.onChanged(config, status)
        }
    }
}