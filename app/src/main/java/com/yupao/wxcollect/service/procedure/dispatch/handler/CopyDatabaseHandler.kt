package com.yupao.wxcollect.service.procedure.dispatch.handler

import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.wxcollect.constant.AppPath
import com.yupao.wxcollect.constant.AppPathCache
import com.yupao.wxcollect.constant.WECHAT_DATABASE_NAME
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.procedure.LinearDelayStrategy
import com.yupao.wxcollect.service.procedure.RetryHelper
import com.yupao.wxcollect.service.procedure.notEmptyResult
import com.yupao.wxcollect.transmit.director.DebugConfig
import com.yupao.wxcollect.util.SpUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.firstOrNull
import java.io.File

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
class CopyDatabaseHandler constructor(
    flag: String,
    // 微信数据库地址
    private val databasePath: String,
    // 是否强制拷贝
    private val isCopyForce: Boolean,
    // 发生错误时最大重试次数
    private val retryCountIfFailed: Int = 3
) {

    companion object {
        private const val TAG = "CopyDatabaseHandler"

        private const val KEY_DATABASE_BASE_DIR = "database_base_dir"
        fun resetDatabasePath() {
            if (!BuildConfig.DEBUG) return
            val value = TimeUtil.fileNameFormat()
            YLog.i(TAG, "resetDatabasePath: $value")
            SpUtil.putString(KEY_DATABASE_BASE_DIR, value)
        }

        private fun getResetDatabaseBaseDirName(): String {
            if (!BuildConfig.DEBUG) {
                return ""
            }
            return SpUtil.getString(KEY_DATABASE_BASE_DIR) ?: ""
        }
    }

    private val TAG = "CopyDatabase:$flag"

    private val retryHelper by lazy {
        RetryHelper<String>(retryCountIfFailed, LinearDelayStrategy(1000)) { count, _ ->
            YLog.i(TAG, "copyDatabase: retry count: $count")
            false
        }
    }

    /**
     * 原该目录下最近一次修改的数据库路径
     */
    private suspend fun getCopyWechatPath(): String {
        val isCopyLatestDb = KVHelper.isCopyLatestDb()
        YLog.i(TAG, "isCopyLatestDb: $isCopyLatestDb")
        if (!isCopyLatestDb) {
            return databasePath
        }
        val parentDir = File(databasePath).parent ?: return databasePath
        val databaseList = WechatFileUtil.fetchAllDatabase(parentDir)
        val newDatabasePath = databaseList.firstOrNull()
        if (newDatabasePath != databasePath) {
            YLog.w(TAG, "copy newDatabasePath: $newDatabasePath, configDatabasePath: $databasePath")
        }
        return newDatabasePath ?: databasePath
    }

    private suspend fun performCopyDatabase(databaseFile: File, destDir: String): String? {
        YLog.i(TAG, "performCopyDatabase delete database file")
        // 删除原来的数据库
        databaseFile.takeIf { it.exists() }?.delete()
        val wechatDatabasePath = getCopyWechatPath()
        val flow = callbackFlow {
            WechatFileUtil.copy(
                sourceFile = wechatDatabasePath,
                destDir = destDir,
            ) {
                YLog.i(TAG, "copy database result: $it")
                trySend(it)
            }
            awaitClose()
        }
        val result = flow.firstOrNull()
        if (result != true) {
            return null
        }
        val copyFilePath = destDir + File(wechatDatabasePath).name
        val newPath = WechatFileUtil.rename(copyFilePath, WECHAT_DATABASE_NAME).getOrNull()
        YLog.i(TAG, "copyFilePath: $copyFilePath, targetFilePath: $newPath")
        return newPath
    }

    suspend fun copyDatabase(): String? {
        val databaseBaseName = File(databasePath).parentFile?.name  ?: (databasePath.hashCode().toString())
        val dirName = getResetDatabaseBaseDirName()
        val destDir = AppPath.contact(AppPathCache.appWechatDir(), databaseBaseName, dirName) + File.separator
        YLog.i(TAG, "copyDatabase: $destDir, isCopyForce: $isCopyForce")
        val databaseFile = File(destDir, WECHAT_DATABASE_NAME)
        if (databaseFile.exists() && !isCopyForce) {
            val lastModified = databaseFile.lastModified()
            val currentTimeMillis = System.currentTimeMillis()
            // 如果最近修改时间低于2分钟则不用删除数据库，复用之前的
            if (!DebugConfig.isAllowCopyDb || currentTimeMillis - lastModified < 2 * 60 * 1000) {
                YLog.i(TAG, "copyDatabase: database existed, lastModified: ${TimeUtil.formatCurrTime(lastModified)}")
                return databaseFile.absolutePath
            }
        } else {
            File(destDir).takeUnless { it.exists() }?.mkdirs()
        }
        // 重试
        return retryHelper.notEmptyResult {
            performCopyDatabase(databaseFile, destDir)
        }?.getOrNull()
    }

    fun getFailedReason(): String {
        val isFileExisted = WechatFileUtil.isFileExisted(databasePath)
        return if (!isFileExisted) {
            "数据库文件不存在"
        } else {
            "数据库拷贝失败"
        }
    }

    fun getFileSize(filePath: String): Long {
        return kotlin.runCatching {
            val file = File(filePath)
            if (file.exists()) {
                val length = file.length()
                YLog.i(TAG, "file path size: $length, path: $filePath")
                return length / 1024 / 1024
            } else
                return -1L
        }.getOrNull() ?: -1L
    }
}