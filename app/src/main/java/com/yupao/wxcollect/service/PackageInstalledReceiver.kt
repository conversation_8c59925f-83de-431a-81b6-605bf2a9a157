package com.yupao.wxcollect.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/6/21/021 15:36
 * @Description app升级安装广播
 */

class PackageInstalledReceiver : BroadcastReceiver() {

    companion object {
        fun register(context: Context) {
            val intentFilter = IntentFilter()
            intentFilter.addAction(Intent.ACTION_PACKAGE_ADDED)
            intentFilter.addAction(Intent.ACTION_PACKAGE_REMOVED)
            intentFilter.addAction(Intent.ACTION_PACKAGE_REPLACED)
            intentFilter.addDataScheme("package")
            context.registerReceiver(PackageInstalledReceiver(), intentFilter)
        }
    }

    override fun onReceive(p0: Context?, intent: Intent?) {
        val action = intent?.action ?: return
        YLog.i("PackageInstalledReceiver, onReceive, action: $action")
        when (action) {
            Intent.ACTION_PACKAGE_ADDED -> {

            }
            Intent.ACTION_PACKAGE_REPLACED -> {

            }
            Intent.ACTION_PACKAGE_REMOVED -> {

            }
        }
    }
}