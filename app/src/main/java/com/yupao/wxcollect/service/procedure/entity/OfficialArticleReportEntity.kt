package com.yupao.wxcollect.service.procedure.entity

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 公众号文章上报
 *
 * <p>创建时间：2023/11/6/006</p>
 *
 * <AUTHOR>
 */
@Keep
@Entity(tableName = "official_article_report")
data class OfficialArticleReportEntity(
    // app类型
    val appType: String?,
    // 微信id
    val wechatId: String?,
    // 手机号
    val telNum: String?,
    // 创建时间
    val createTime: String?,
    // 公众号名称
    val officialName: String?,
    // 文章url
    val url: String?,
    // 文章唯一标识
    val articleUnique: String?,
    // 群ID
    val talker: String?,
    // 上报次数
    val reportCount: Int?,
    // 上报时间
    val reportTime: Long?,
    // 上报失败原因
    val reportFailMsg: String?,
    // 备注
    val remark: String?,
    @PrimaryKey(autoGenerate = true)
    val id: Int? = null
)