package com.yupao.wxcollect.service.net_monitor

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.net.VpnService
import android.os.Build
import android.os.ParcelFileDescriptor
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.execute.wxcollect.R
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.service.notice.ReportNotificationManager
import com.yupao.wxcollect.ui.main.MainActivity
import com.yupao.ylog.YLog
import me.zhanghai.android.files.util.closeSafe

/**
 * <AUTHOR>
 * @Date 2023/4/26/026 11:32
 * @Description 监控部分应用网络，主要用于单独给微信断网
 */
class NetMonitorService: VpnService() {

    companion object {
        const val KEY_NOTICE_CONFIG = "notice_config"

        const val KEY_TURN_ON = "turn_on"

        // 服务是否正在允许
        var isRunning = false
        private set

        fun requestPermission(activity: FragmentActivity, requestCode: Int = 123): Boolean {
            val intent = prepare(activity)
            if (intent != null) {
                activity.startActivityForResult(intent, requestCode)
                return false
            }
            return true
        }

        fun start(context: Context, requestCode: Int = 123): Boolean {
            var intent = prepare(context)
            if (intent == null) {
                intent = Intent(context, NetMonitorService::class.java)
                intent.putExtra(KEY_TURN_ON, true)
                intent.putExtra(KEY_NOTICE_CONFIG, NotificationConfig(
                    pendingActivityPath = MainActivity::class.java.name,
                    content = "正在断开微信网络",
                    title = "微信网络控制服务"
                ))
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
                return true
            } else {
                (context as? AppCompatActivity)?.startActivityForResult(intent, requestCode)
            }
            return false
        }

        fun stop(context: Context) {
            val isServiceRunning = isServiceRunning(context, NetMonitorService::class.java.name)
            YLog.i("NetMonitorService", "stop: isServiceRunning = $isServiceRunning, isRunning = $isRunning")
            if (isRunning || isServiceRunning) {
                val intent = Intent(context, NetMonitorService::class.java)
                intent.putExtra(KEY_TURN_ON, false)
                context.startService(intent)
            }
        }

        /**
         * @return 判断服务是否开启
         */
        private fun isServiceRunning(context: Context?, serviceName: String?): Boolean {
            if (TextUtils.isEmpty(serviceName) || context == null) return false
            val myManager = context.getSystemService(ACTIVITY_SERVICE) as ActivityManager
            //android 26之后只返回本应用的服务，之前会返回所有正在运行的服务，故值需要足够大
            val maxNum = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) 10 else 200
            val runningService =
                myManager.getRunningServices(maxNum) as ArrayList<ActivityManager.RunningServiceInfo>?
            var result = false
            for (i in runningService!!.indices) {
                val className = runningService[i].service.className
                if (TextUtils.equals(className, serviceName)) {
                    result = true
                    break
                }
            }
            return result
        }
    }

    private val notificationManager by lazy {
        ReportNotificationManager()
    }

    private var mInterface: ParcelFileDescriptor? = null

    private var fd: Int? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val isTurnOn = intent?.getBooleanExtra(KEY_TURN_ON, false) == true
        YLog.i("NetMonitorService", "onStartCommand: isTurnOn = $isTurnOn")
        if (isTurnOn) {
            val notificationConfig: NotificationConfig? = intent?.getParcelableExtra(KEY_NOTICE_CONFIG)
            val notification = notificationManager.showNotification(this, notificationConfig)
            val noticeId = notificationConfig?.noticeId ?: (System.currentTimeMillis() % Int.MAX_VALUE).toInt()
            notificationConfig?.noticeId = noticeId
            startForeground(noticeId, notification)
            setup()
        } else {
            stopService()
        }
        return super.onStartCommand(intent, flags, startId)
    }

    private fun stopService() {
        isRunning = false
        kotlin.runCatching {
            mInterface?.closeSafe()
        }
        stopSelf()
        YLog.i("NetMonitorService", "stopService")
    }

    private fun setup() {
        YLog.i("NetMonitorService", "setup: isRunning = $isRunning")
        if (isRunning) {
            return
        }
        kotlin.runCatching {
            val builder = Builder()
            builder.addDnsServer("192.168.0.1")
            builder.setBlocking(true)
            //这个方法就是给自己的VPN连接设置一个名字，将会展示在系统的通知栏中。
            builder.setSession(getString(R.string.application_name))
            // 只中断微信网络
            builder.addAllowedApplication("com.tencent.mm")
            //调用这个方法 后 将创建VPN 接口
            kotlin.runCatching {
                mInterface?.close()
            }.exceptionOrNull()?.printStackTrace()
            builder.addAddress("**************", 24)
            builder.addRoute("0.0.0.0", 0)
            //创建VPN 接口
            val fileDescriptor = builder.establish()
            fd = fileDescriptor?.fd
            mInterface = fileDescriptor
            notificationManager.updateNotice(this, "已关闭微信网络")
            ToastUtilsAssist.showCustomToast(this, "已关闭微信网络")
        }.exceptionOrNull()?.printStackTrace()
        YLog.i("NetMonitorService", "setup: init over, fd = $fd")
        isRunning = true
    }
}