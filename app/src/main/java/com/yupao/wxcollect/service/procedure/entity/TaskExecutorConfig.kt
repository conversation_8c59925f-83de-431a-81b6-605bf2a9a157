package com.yupao.wxcollect.service.procedure.entity

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.dispatch.ConflictStrategy

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
@Keep
data class TaskExecutorConfig(
    // 微信数据库地址
    val databasePath: String,
    // 微信数据库密码
    val password: String,
    // 应用类型
    val appType: String,
    // 任务配置列表
    val configList: List<ITaskConfig>,
    // 是否强制拷贝
    val isCopyForce: Boolean = false,
    // 任务超时时间，单位毫秒
    // 超过该时间后，关闭数据库，不再管理任务状态，任务仍然可以执行(也不能完全保证，比如还需要查询数据库)
    val timeoutTime: Long = DEFAULT_TIMEOUT_TIME,
    // 任务失效时间，单位毫秒，应该大于[timeoutTime]，超过该时间后，主动取消任务
    val invalidTime: Long = DEFAULT_INVALID_TIME,
    // 冲突策略
    val strategy: ConflictStrategy,
) {
    companion object {
        // 默认超时时间
        const val DEFAULT_TIMEOUT_TIME = 8 * 60_000L
        // 默认失效时间
        const val DEFAULT_INVALID_TIME = 12 * 60_000L
    }
}

interface ITaskConfig {

    val type: String

    val name: String

    val unique: String

    companion object {
        const val TypeSql = "sql"

        const val TypeWechatInfo = "wechatInfo"

        const val TypeOfficialArticle = "wxArticle"
    }
}

fun List<ITaskConfig?>.tag(): String {
    return this.joinToString("、"){
        "${it?.name}/${it?.unique}"
    }
}