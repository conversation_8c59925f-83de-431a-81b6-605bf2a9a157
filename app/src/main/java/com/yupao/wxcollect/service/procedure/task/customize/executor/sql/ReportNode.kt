package com.yupao.wxcollect.service.procedure.task.customize.executor.sql

import androidx.annotation.Keep
import com.yupao.utils.lang.json.GsonUtil
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.constant.DeviceConfig
import com.yupao.wxcollect.service.procedure.ExponentialDelayStrategy
import com.yupao.wxcollect.service.procedure.RetryHelper
import com.yupao.wxcollect.service.procedure.entity.SimpleResultEntity
import com.yupao.wxcollect.service.procedure.entity.director.CollectEntity
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.entity.director.ReportData
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatus
import com.yupao.wxcollect.service.procedure.entity.request.ReportNetModel
import com.yupao.wxcollect.service.procedure.model.ReportRepositoryEntryPoint
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffHelper
import com.yupao.ylog.YLog
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.delay

/**
 * 上报
 *
 * <p>创建时间：2024/12/13/013</p>
 *
 * <AUTHOR>
 */
class ReportNode(
    private val tag: String
) {

    private val resp by lazy {
        EntryPointAccessors.fromApplication(
            AndroidSystemUtil.getContext(),
            ReportRepositoryEntryPoint::class.java
        ).getReportRepository()
    }

    /**
     * 重试3次，依次等待：10s、20s、40s
     */
    private val retryHelper by lazy {
        RetryHelper<ReportNodeResult>(3, ExponentialDelayStrategy(10_000))
    }

    suspend fun report(collectEntity: CollectEntity, unique: String, data: ReportData): ReportNodeResult {
        val model = ReportNetModel(
            taskId = collectEntity.taskId,
            execStep = ReportNetModel.Succeed,
            execData = data.data,
            recordUuid = unique,
            execDataNum = data.count,
            version = data.version,
        )
        YLog.i(tag, "start report: ${GsonUtil.toJson(model)}")
        val result = retryHelper.perform {
            val entity = resp.commReport(model)
            YLog.i(tag, "report result code: ${entity.getCode()}, msg: ${entity.getMsg()}")
            // 成功直接返回
            if (entity.isOK()) {
                return@perform Result.success(ReportNodeResult(true))
            }
            // 非5xx错误以及异常，不重试
            if ((entity.getCode().toIntOrNull() ?: 0) / 100 != 5) {
                return@perform Result.success(ReportNodeResult(false, entity.getMsg(), false))
            }
            return@perform Result.failure(Exception(entity.getMsg()))
        }.getRecently()
        val resultData = result?.getOrNull()
        if (resultData?.isOk == true) {
            // 请求成功，更新diff版本
            DiffHelper.saveReportVersion(collectEntity.condition.appType, collectEntity.taskCode, data.version ?: 0)
        }
        return resultData ?: ReportNodeResult(false, result?.exceptionOrNull()?.message ?: "report failed", true)
    }

    private suspend fun delayReport(taskTag: String) {
        val seconds = DeviceConfig.reportDelayMaxSeconds ?: 0
        if (seconds <= 0) {
            return
        }
        val delayTime = (0..seconds).random() * 1000L
        YLog.i(tag, "$taskTag delay report: $delayTime ms")
        delay(delayTime)
    }

    suspend fun performReport(
        taskEntity: CollectTaskEntity,
        data: ReportData
    ): ReportResult {
        this.delayReport(taskEntity.entity.getTag())
        val unique = taskEntity.entity.unique
        val reportResult = report(taskEntity.entity, unique, data)
        val reportMethod = if (reportResult.isOk) {
            ReportData.NotReport
        } else if (reportResult.isAllowDowngrade) {
            ReportData.ReportMethodMqtt
        } else {
            ReportData.NotReport
        }
        val newData = data.copy(reportMethod = reportMethod)
        val result = if (reportResult.isOk) {
            SimpleResultEntity.succeed(data.count)
        } else {
            if (reportMethod != ReportData.NotReport) {
                SimpleResultEntity.failed(reportResult.msg)
            } else {
                SimpleResultEntity.waitRetry(reportResult.msg)
            }
        }
        return ReportResult(
            TaskStatus.Report(unique = unique, data = newData),
            result
        )
    }
}

@Keep
data class ReportNodeResult(
    val isOk: Boolean,
    val msg: String? = null,
    val isAllowDowngrade: Boolean = true,
)

data class ReportResult(
    val status: TaskStatus.Report,
    val result: SimpleResultEntity,
)