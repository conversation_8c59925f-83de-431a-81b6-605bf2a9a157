package com.yupao.wxcollect.service.procedure.task.customize.executor.sql

import com.yupao.wxcollect.hook.utils.SqlUtil
import com.yupao.wxcollect.service.db.IDatabaseHelper
import com.yupao.wxcollect.service.procedure.entity.query.RemarkQueryEntity
import com.yupao.wxcollect.service.procedure.entity.request.CustomerInfo
import com.yupao.wxcollect.service.procedure.task.ConfigException
import com.yupao.ylog.YLog

/**
 * 自定义任务查询数据库
 *
 * <p>创建时间：2024/1/3/003</p>
 *
 * <AUTHOR>
 */
class CustomizeQueryNode constructor(
    private val keyList: List<String>?,
    private val queryStatement: String?,
    private val limit: Int,
    private val tag: String?,
) {

    companion object {
        const val KEY_PAGE_LIMIT = "\${limit}"

        const val KEY_PAGE_OFFSET = "\${offset}"
    }

    // 是否支持分页查询
    val isSupportPage by lazy {
        queryStatement?.contains(KEY_PAGE_OFFSET) ?: false
    }

    /**
     * 获取一个分页查询的sql语句
     * @param queryStatement
     * @param limit
     * @param count
     */
    private fun fetchPageSql(queryStatement: String, limit: Int, count: Int): String {
        // SELECT *
        //  FROM rcontact
        // WHERE username NOT LIKE 'gh_%' AND
        //       usernameFlag = 0 AND
        //       verifyFlag = 0 AND
        //       nickname != '' AND
        //       type IN (2, 4)
        // LIMIT ${limit} OFFSET ${offset}
        //;
        if (!isSupportPage) {
            return queryStatement
        }
        return SqlUtil.replaceSql(queryStatement, mapOf(
            KEY_PAGE_LIMIT to limit.toString(),
            KEY_PAGE_OFFSET to (limit * count).toString(),
        ))
    }

    suspend fun perform(
        databaseHelper: IDatabaseHelper?,
        block: suspend (List<Map<String, Any?>>) -> Unit
    ) {
        if (queryStatement.isNullOrEmpty()) {
            throw ConfigException("sql为空")
        }
        val queryKeyList = (if (keyList.isNullOrEmpty()) fetchKeyList(queryStatement) else keyList)?.map {
            it.trim()
        }
        var count = 0
        while (true) {
            val querySql = fetchPageSql(queryStatement, limit, count++)
            val list = databaseHelper?.query(querySql, queryKeyList, tag) ?: emptyList()
            block.invoke(list)
            // 列表长度小于limit 或者不支持分页查询，则结束
            if (list.size < limit || !isSupportPage) {
                YLog.i(tag, "query count: $count")
                return
            }
        }
    }

    // select * from xxx
    // select c.*,a.chatroomname,a.memberCount from chatroom as a left join (select nickname, username ,message.lvbuffer t1,message.lvbuffer t2, message.talker, content,message.lvbuffer t3,message.lvbuffer t4, createTime,message.lvbuffer t5,message.lvbuffer t6,message.lvbuffer t7  from message inner join rcontact on rcontact.username = message.talker  where message.type = 1 and createTime > (strftime('%s','now') - 216003600) * 1000 ) as c on c.talker = a.chatroomname order by createTime desc
    // select value as aa from userinfo where id=2
    private fun fetchKeyList(sql: String?): List<String>? {
        val trimSql = sql?.trim()
        if (trimSql.isNullOrEmpty()) {
            return null
        }
        val index = trimSql.indexOf("from", ignoreCase = true)
        val keyText = trimSql.subSequence("select".length, index).trim()
        return keyText.split(",").mapNotNull {
            val key = it.trim()
            val asIndex = key.indexOf(" as ")
            if (asIndex >= 0 && asIndex < key.length) {
                return@mapNotNull key.substring(asIndex + 1)
            }
            val pointIndex = key.indexOf(".")
            if (pointIndex >= 0 && pointIndex < key.length) {
                return@mapNotNull key.substring(pointIndex + 1)
            }
            key
        }.filter {
            it != "*"
        }
    }

    private suspend fun query(databaseHelper: IDatabaseHelper?): List<CustomerInfo>? {
        val sql = "select username, conRemark from rcontact where conRemark != ''"
        return databaseHelper?.query(sql, RemarkQueryEntity::class, tag)?.map {
            CustomerInfo(
                customerAccount = it.username,
                recentChatDate = null,
                remark = it.conRemark,
            )
        }
    }

}