package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep

/**
 * 单文件上传返回实体
 */
@Keep
data class UploadEntity(

    /**
     * 资源唯一id
     */
    val resourceId: String?,

    /**
     * 阿里云oss地址
     */
    val url: String?,

    /**
     * 额外属性字段
     */
    val extraInfo: UploadExtraInfoEntity?

//    /**
//     * 上传地址
//     */
//    val uploadUrl: String?,
//    /**
//     * 访问地址
//     */
//    val accessUrl: String?,
//
//    /**
//     * map，上传时需要带给阿里云
//     */
//    val headers: Map<String, String>?,
//    /**
//     * 上传过期时间，格式为2022-05-28 00:01:02
//     */
//    val uploadExpiredTime: String,
)

@Keep
data class UploadExtraInfoEntity(
    /**
     * 访问地址
     */
    val accessUrl: String?,
    /**
     * 上传地址信息
     */
    val uploadUrlInfo: OssUploadInfo? = null,

    /**
     * 水印上传地址信息
     */
    val watermarkUploadUrlInfo: OssUploadInfo? = null
)

@Keep
data class OssUploadInfo(
    /**
     * 上传地址信息
     */
    val uploadUrl:String?,
    /**
     * map，上传时需要带给阿里云
     */
    val headers: Map<String, String>?,

    /**
     * post上传方式数据
     */
    val formData:String?
)
