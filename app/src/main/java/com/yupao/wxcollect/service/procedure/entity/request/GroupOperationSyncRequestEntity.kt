package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity

/**
 * <AUTHOR>
 * @Date 2023/4/17/017 16:05
 * @Description
 */
@Keep
data class GroupOperationSyncRequestEntity(
    @Transient
    val queryMin: String?,
    @Transient
    val userEntity: UserEntity?,
    @Transient
    val config: ReportConfig?,
    val list: List<GroupOperationEntity>?
): CommReportEntity(queryMin, userEntity, config)

@Keep
data class GroupOperationEntity(
    //当前信息ID
    val msg_id: String? = null,
    //微信群名
    val group_name: String? = null,
    //微信群ID
    val group_id: String? = null,
    //操作时间（毫秒时间戳）
    val operate_time: String? = null,
    //操作类型：1=邀请；2=二维码邀-*请；3=踢出；4=被踢；5=被邀请；6=二维码加群；
    val operate_type: String? = null,
    //操作人微信ID。当“操作类型”为4时，拿不到此数据，置空即可。
    val wechat_id: String? = null,
    //操作人微信昵称。当“操作类型”为4时，拿不到此数据，置空即可。
    val wechat_name: String? = null,
    //被操作人微信ID。当“操作类型”为1时，此字段留空即可。
    val to_wechat_id: String? = null,
    //被操作人微信昵称。当“操作类型”为1时，此字段留空即可。
    val to_wechat_name: String? = null,
    //被操作人列表：当“操作类型”为1或2时传此字段，邀请操作可同时邀请多人。
    val to_wechat_info_list: List<WechatInfoListEntity>? = null,
    //备注。操作类型为4时使用，其它情况留空
    val remark: String? = null,
)

@Keep
data class WechatInfoListEntity(
    val id: String? = null,
    val name: String? = null,
)