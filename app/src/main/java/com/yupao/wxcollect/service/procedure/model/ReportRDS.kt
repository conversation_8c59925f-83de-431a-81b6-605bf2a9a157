package com.yupao.wxcollect.service.procedure.model

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.yupao.data.net.yupao.BaseData
import com.yupao.data.net.yupao.JavaNetEntity
import com.yupao.wxcollect.net.AppHttpClient
import com.yupao.wxcollect.service.procedure.api.ReportApi
import com.yupao.wxcollect.service.procedure.entity.request.AddFriendsInfoItemEntity
import com.yupao.wxcollect.service.procedure.entity.request.ApplyToAddFriendsParamsModel
import com.yupao.wxcollect.service.procedure.entity.request.DeleteFriendRequestEntity
import com.yupao.wxcollect.service.procedure.entity.request.GroupInfoSyncRequestEntity
import com.yupao.wxcollect.service.procedure.entity.request.GroupListSyncRequestEntity
import com.yupao.wxcollect.service.procedure.entity.request.GroupOperationSyncRequestEntity
import com.yupao.wxcollect.service.procedure.entity.request.GroupRemoveSyncRequestEntity
import com.yupao.wxcollect.service.procedure.entity.request.MessageRequestEntity
import com.yupao.wxcollect.service.procedure.entity.request.UpdateFriendRequestEntity
import com.yupao.wxcollect.util.LogUtil
import com.yupao.yapm.utils.ZipUtil
import com.yupao.ylog.YLog
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.Request
import okhttp3.RequestBody
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 17:09
 * @Description
 */
class ReportRDS {

    val api by lazy {
        AppHttpClient.instanceForC.createApiService(ReportApi::class.java)
    }

    private val gson by lazy { Gson() }

    suspend fun uploadChatHistory(entity: MessageRequestEntity?) = kotlin.run {
        val appType = entity?.config?.appType
        val json = gson.toJson(entity)
        val baseFile = LogUtil.getBaseReportFile(null, "message")
        val file = File(baseFile, "ChatHistory-$appType.json")
        file.takeIf { it.exists() }?.delete()
        file.parentFile.takeUnless { it.exists() }?.mkdirs()
        file.writeText(json)
        val compressFile = File(baseFile, "ChatHistory-$appType-compress.zip")
        val result = ZipUtil.zip(file.absolutePath, compressFile.name)
        val uploadFileName: String
        val uploadFile = if (result != null) {
            uploadFileName = "automatic_collect_wechat_data"
            compressFile
        } else {
            YLog.i("ReportRDS", "uploadChatHistory compress failed: $result")
            uploadFileName = "upload_chat_history_data"
            file
        }
        val builder = MultipartBody.Builder()
        val requestFile = RequestBody.create(null, uploadFile)
        builder.addFormDataPart(uploadFileName, uploadFileName, requestFile)
        YLog.i("ReportRDS", "uploadChatHistory: " +
                "size = ${entity?.list?.size}, " +
                "source-length = ${file.length() / 1024}kb, " +
                "compress-length = ${compressFile.length() / 1024}kb"
        )
        api.uploadChatHistory(builder.build().parts)
    }

    suspend fun syncFriendTabulation(entity: GroupListSyncRequestEntity?) = api.syncFriendTabulation(entity)

    suspend fun syncGroupOperation(entity: GroupOperationSyncRequestEntity?) = api.syncGroupOperation(entity)

    suspend fun syncBootedOut(entity: GroupRemoveSyncRequestEntity?) = api.syncBootedOut(entity)

    suspend fun syncGroup(entity: GroupInfoSyncRequestEntity?) = api.syncGroup(entity)

    private var isRequestByOkHttp: Boolean = false

    suspend fun applyToAddFriends(entity: ApplyToAddFriendsParamsModel?): JavaNetEntity<BaseData> = kotlin.run {
        try {
            if (!isRequestByOkHttp) {
                return@run api.applyToAddFriends(entity)
            }
        } catch (e: IllegalArgumentException) {
            this.isRequestByOkHttp = true
        }
        val targetUrl = ReportApi.applyToAddFriendsUrl
        val url = if (targetUrl.startsWith("/")) targetUrl.substring(1) else targetUrl
        YLog.d("applyToAddFriends request : ${AppHttpClient.instanceForC.baseUrl + url}")
        // 失败降级通过OkHttp直接请求
        val request = Request.Builder()
            .url(AppHttpClient.instanceForC.baseUrl + url)
            .post(RequestBody.create("application/json".toMediaTypeOrNull(), gson.toJson(entity)))
            .build()
        val response =
            AppHttpClient.instanceForC.okHttpClient.newCall(request).execute().body?.string()
        val responseData = gson.fromJson<JavaNetEntity<BaseData>?>(
            response,
            object : TypeToken<JavaNetEntity<BaseData>>() {}.type
        )
        YLog.i("applyToAddFriends responseData : $responseData")
        return@run responseData
    }

    suspend fun addFriendsInfo(
        wxUserId: String?,
        wxNumber: String?,
        phoneNumber: String?,
        waiterNick: String?,
        friendsInfo: List<AddFriendsInfoItemEntity>?
    ): JavaNetEntity<BaseData> = kotlin.run {
        api.addFriendInfo(
            mutableMapOf(
                "waiterAccount" to wxUserId,
                "waiterAlias" to wxNumber,
                "waiterNick" to waiterNick,
                "phoneNumber" to phoneNumber,
                "customers" to friendsInfo
            )
        )
    }

    suspend fun updateFriendsInfo(
        entity: UpdateFriendRequestEntity?
    ): JavaNetEntity<BaseData> = kotlin.run {
        api.updateWeChatCronyInfo(entity)
    }

    suspend fun deleteFriendsInfo(
        entity: DeleteFriendRequestEntity?
    ): JavaNetEntity<BaseData> = kotlin.run {
        api.delWeChatCrony(entity)
    }
}