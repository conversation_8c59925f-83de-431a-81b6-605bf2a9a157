package com.yupao.wxcollect.service.accessibility.process

import android.view.accessibility.AccessibilityEvent
import com.yupao.wxcollect.service.accessibility.filter.*
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.fakeSwitchAccountUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.loginUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.mobileInputUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatDialogClass
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatLauncherUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatPackageName
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatSettingUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatSplashActivityCls
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatSwitchAccountCls
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.ylog.YLog
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/4/28/028 10:39
 * @Description 适配微信V8.0.35版本
 */
class WechatTaskV8(
    private val appType: List<String>?,
    private val historyPage: List<String>,
    private val onTaskCompleted: (() -> Unit)? = null,
    ): IWechatTask {
    private var loginMobileHandler: ReturnPageHandler? = null

    override fun getPageHandler(): MutableList<IPageHandler> {
        val index = if(appType?.contains(ReportConfig.APP_MAIN) == true) 0 else -1
        YLog.i("WechatTaskV8", "getPageHandler: index = $index")
        // 切换账号页面-点击头像登陆账号
        val launcherViewPosition = ViewPosition(
            viewText = "轻触头像以切换帐号",
            brotherFilter = AccessibilityNodeInfoFilter(className = null, offset = 1, isGreedyMode = true),
        )
        // 设置-切换帐号
        val switchViewPosition = ViewPosition(
            viewText = "切换帐号",
            brotherFilter = null,
        )
        return mutableListOf(
            // 选择双开应用中的一个
            PageHandler(
                null,
                listOf(IWechatTask.ResolverActivity),
                mutableListOf(
                    ClickViewHandler("微信", index),
                )
            ),
            // 首页-点击我
            PageHandler(
                packageName = wechatPackageName,
                page = listOf(wechatLauncherUI),
                viewHandlerList = mutableListOf(
                    ClickViewHandler("我", index = IViewHandler.LAST_ITEM),
                    ClickViewHandler("设置"),
                    ScrollViewHandler(offsetY = 200, viewPosition = switchViewPosition),
                    ClickViewHandler(
                        viewText = "切换帐号",
                        delayDispatchTime = 2000,
                        eventType = AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED,
                    ),
                ),
                viewPositionList = listOf(switchViewPosition),
            ),
            // 点击设置-切换账号之后展示如何切换帐号引导弹窗
            PageHandler(
                packageName = wechatPackageName,
                page = listOf(wechatDialogClass),
                viewHandlerList = mutableListOf(
                    // 点击设置-切换账号之后展示如何切换帐号引导弹窗
                    ClickViewHandler("知道了", eventType = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED),
                ),
            ),
            // 切换账号页面 - 添加帐号
            PageHandler(
                packageName = wechatPackageName,
                page = listOf(wechatSwitchAccountCls),
                viewHandlerList = mutableListOf(
                    // 切换账号页面-点击切换账号
                    ClickViewHandler("添加帐号"),
                ),
                viewPositionList = listOf(launcherViewPosition),
            ),
            // 切换账号页面[登录其他帐号, 注册新帐号, 取消]三选一
            PageHandler(
                packageName = wechatPackageName,
                page = listOf(wechatDialogClass),
                viewHandlerList = mutableListOf(
                    // 切换账号之后展示[登录其他帐号, 注册新帐号, 取消]三选一
                    ClickViewHandler("登录其他帐号", eventType = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED),
                ),
            ),
            // 登录页面返回之后执行登录
            PageHandler(
                packageName = wechatPackageName,
                page = listOf(mobileInputUI),
                viewHandlerList = mutableListOf(
                    // 登录页面返回
                    DelayReturnHandler(historyPage = historyPage) {
                        GlobalScope.launch {
                            delay(2000)
                            // 执行登录
                            val result = launcherViewPosition.performClick()
                            YLog.i("WechatTaskV8", "getPageHandler: result = $result")
                        }
                    },
                ),
            ),
            // 需要返回的页面
            ReturnPageHandler(
                packageName = wechatPackageName,
                excludePage = listOf(
                    wechatSplashActivityCls,
                    wechatSwitchAccountCls,
                    wechatLauncherUI,
                    wechatSettingUI,
                    loginUI,
                    mobileInputUI,
                    fakeSwitchAccountUI,
                ),
                historyPage = historyPage,
                viewHandlerList = null,
            ).apply {
                loginMobileHandler = this
            },
        )
    }
}