package com.yupao.wxcollect.service.concurrent

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Semaphore
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/25/025</p>
 *
 * <AUTHOR>
 */
class ConcurrentDispatcher(
    // 最多支持[maxConcurrency]线程并行
    private val maxConcurrency: Int,
) {

    private val semaphore = Semaphore(maxConcurrency)

    private val pool by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        val dispatcher = Executors.newFixedThreadPool(maxConcurrency).asCoroutineDispatcher()
        CoroutineScope(SupervisorJob() + dispatcher)
    }

    fun <T> submit(block: suspend () -> T): Deferred<T> {
        return pool.async {
            semaphore.acquire()
            try {
                block()
            } finally {
                semaphore.release()
            }
        }
    }

    /**
     * 提交多个任务, 解决的问题是：
     * 1. 所有任务共享线程池，最多同时执行[maxConcurrency]个任务，后面提交的会阻塞，等待前面的任务完成
     * 2. 执行该方法会作为一个任务集，其中一个任务执行的失败根据[strategy]会影响该任务集下其他任务的执行，但不会影响其他任务集
     *
     * @param T: 返回值类型
     * @param blocks: 任务集合
     * @param strategy: 异常处理策略
     * @return 执行成功返回的集合
     */
    @Throws(Exception::class)
    suspend fun <T> submitAll(blocks: List<suspend () -> T>, strategy: ExceptionStrategy): List<T?> {
        val list = CopyOnWriteArrayList<T>()
        val taskList = blocks.map {
            submit(it)
        }
        taskList.forEach {
            kotlin.runCatching {
                val result = it.await()
                list.add(result)
            }.onFailure {
                val result = strategy.onError(it, taskList)
                list.add(result)
            }
        }
        return list
    }
}

fun main() {
    val dispatch = ConcurrentDispatcher(3)
    GlobalScope.launch {
        // 包含crash任务
        val job = async {
            taskOne(dispatch, "one")
        }
        // 不包含crash任务
        val job2 = async {
            taskTwo(dispatch, "two")
        }
        val result = job.await()
        val result2 = job2.await()
        println("finished:\nresult: $result \nresult2: $result2")
    }
    Thread.sleep(15_000)
    println("over")
}

private suspend fun taskOne(dispatch: ConcurrentDispatcher, tag: String): Result<List<String?>> {
    val list = List(10) {
        suspend {
            println("$tag start: $it, tid: ${Thread.currentThread().id}")
            delay((it % 3) * 1000L + 1000L)
            if (it == 4) {
                throw RuntimeException("$tag test exp")
            }
            println("$tag end: $it, tid: ${Thread.currentThread().id}")
            "$it"
        }
    }
    val result = runCatching {
        dispatch.submitAll(list, CancelAllStrategy())
    }.onFailure {
        println("$tag catch exp: $it")
    }
    return result
}

private suspend fun taskTwo(dispatch: ConcurrentDispatcher, tag: String): Result<List<String?>> {
    val list = List(10) {
        suspend {
            println("$tag start: $it, tid: ${Thread.currentThread().id}")
            delay((it % 3) * 1000L + 1000L)
            println("$tag end: $it, tid: ${Thread.currentThread().id}")
            "$it"
        }
    }
    val result = runCatching {
        dispatch.submitAll(list, CancelAllStrategy())
    }.onFailure {
        println("$tag catch exp: $it")
    }
    return result
}