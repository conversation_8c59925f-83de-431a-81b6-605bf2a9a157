package com.yupao.wxcollect.service.db

import com.yupao.share.cache.db.kv.KvDbHelper

/**
 * kv工具类
 *
 * <p>创建时间：2024/10/26/026</p>
 *
 * <AUTHOR>
 */
object KVHelper {

    // 20分钟无效
    const val NetSwitchInvalidTime = 1000 * 60 * 20L

    /**
     * 确定执行时不关闭微信网络吗
     *
     * @param isOpen
     */
    suspend fun saveNetSwitchEnable(isOpen: Boolean) {
        KvDbHelper.save("net_switch_enable", isOpen.toString(), invalidTime = NetSwitchInvalidTime)
    }

    /**
     * 确定执行时不关闭微信网络吗
     */
    suspend fun netSwitchEnable(): Boolean {
        return KvDbHelper.get("net_switch_enable")?.fetchValidValue()?.toBoolean() ?: false
    }

    /**
     * 每次拷贝是否使用最近更新的数据库
     */
    suspend fun saveCopyLatestDb(isOpen: Boolean) {
        KvDbHelper.save("is_save_copy_latest_db", isOpen.toString())
    }

    /**
     * 每次拷贝是否使用最近更新的数据库, 默认开启
     */
    suspend fun isCopyLatestDb(): Boolean {
        return KvDbHelper.get("is_save_copy_latest_db")?.fetchValidValue()?.toBoolean() != false
    }

    /**
     * 异常悬浮窗提
     */
    suspend fun saveClashTipsDb(isOpen: Boolean) {
        KvDbHelper.save("is_clash_tips_db", isOpen.toString())
    }

    /**
     * 是否异常悬浮窗提示, 默认关闭
     */
    suspend fun isClashTipsDb(): Boolean {
        return KvDbHelper.get("is_clash_tips_db")?.fetchValidValue()?.toBoolean() == true
    }

    suspend fun set(key: String, value: String) {
        KvDbHelper.save(key, value)
    }

    suspend fun get(key: String): String? {
        return KvDbHelper.get(key)?.fetchValidValue()
    }
}