package com.yupao.wxcollect.service.notice

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import com.yupao.execute.wxcollect.R
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.service.ReportService
import com.yupao.wxcollect.util.PowerUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/10/010 16:04
 * @Description
 */
class ReportNotificationManager {

    companion object {
        val instance = ReportNotificationManager()

        private const val TAG = "ReportNotificationManager"

        private const val UPDATE_INTERVAL = 10 * 1000
    }

    private var config: NotificationConfig? = null

    fun showNotification(context: Context?, config: NotificationConfig?): Notification? {
        YLog.d("showNotification: $config")
        context ?: return null
        config ?: return null
        if (config.noticeId == null) {
            config.noticeId = this.config?.noticeId ?: (System.currentTimeMillis() % Int.MAX_VALUE).toInt()
        }
        this.config = config.copy()
        val intent = Intent(context, ReportService::class.java)
        val flag = PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        val contentPendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            flag
        )
        val channelId = config.channelId
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelName = config.channelName
            if (channelId != null && channelName != null) {
                createNotificationChannel(context, channelId, channelName)
            }
        }
        val builder = NotificationCompat.Builder(context, channelId ?: "")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(config.title ?: context.resources.getString(R.string.application_name))
            .setContentText(config.content ?: "")
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setContentIntent(contentPendingIntent)
            .setDefaults(Notification.DEFAULT_SOUND)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setVibrate(longArrayOf(0L))
        return builder.build()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(context: Context, channelId: String, channelName: String) {
        val channel = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_HIGH)
        channel.lightColor = Color.DKGRAY
        channel.setSound(null, null)
        channel.enableLights(false)
        channel.enableVibration(false)
        channel.importance = NotificationManager.IMPORTANCE_HIGH
        channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }

    fun updateNotice(context: Context?, content: String?, title: String? = null) {
        context ?: return
        content ?: return
        val config = this.config ?: return
        val noticeId = config.noticeId ?: return
        kotlin.runCatching {
            config.content = content
            if (title != null) {
                config.title = title
            }
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val notification = showNotification(context, config)
            notificationManager.notify(noticeId, notification)
            PowerUtil.wakeupScreen()
            YLog.d(TAG, "update notice: $noticeId, $content")
        }.onFailure {
            YLog.printException(TAG, it, "updateNotice")
        }
    }

    fun updateNotice(content: String, isForce: Boolean = false) {
        val config = this.config ?: return
        val currTime = TimeUtil.getCurrTime()
        if (currTime - config.updateTime < UPDATE_INTERVAL && !isForce) {
            YLog.i(TAG, "update ignore: $content")
            return
        }
        config.updateTime = currTime
        updateNotice(AndroidSystemUtil.getContext(), content)
    }
}