package com.yupao.wxcollect.service.accessibility.process

import android.os.Handler
import android.os.Looper
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.wxcollect.App
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/23/023 15:53
 * @Description 删除微信数据库
 */
class DeleteDatabaseTask() {

    private val mainHandler = Handler(Looper.getMainLooper())

    fun perform(databasePath: List<String>?, callback: ((Boolean) -> Unit)? = null) {
        YLog.i("DeleteDatabaseTask", "perform: databasePath = $databasePath")
        if (databasePath.isNullOrEmpty()) {
            return
        }
        /*val list: List<String> = appType.flatMap {
            val appDirName = when (it) {
                ReportConfig.APP_MAIN -> "0"
                ReportConfig.APP_SUB -> "999"
                else -> return@flatMap listOf()
            }
            val baseDir = String.format(WECHAT_DATABASE_BASE_PATH, appDirName)
            WechatFileUtil.getDatabaseName(baseDir).map {
                String.format(WECHAT_DATABASE_PATH, appDirName, it)
            }.asIterable()
        }*/
        if (databasePath.isEmpty()) {
            showToast("未找到微信数据库目录")
            callback?.invoke(false)
            return
        }
        YLog.i("DeleteDatabaseTask", "deleteFile: databasePath = $databasePath")
        performDeleteFile(databasePath, 0, 0, callback)
    }

    /**
     * 按顺序删除，最多重试3次，如果3次都失败，则回调结果，多条数据任一条数据失败都回调结果，只有全部成功才会回调true
     */
    private fun performDeleteFile(
        list: List<String>,
        count: Int = 0,
        deleteIndex: Int = 0,
        callback: ((Boolean) -> Unit)?
    ) {
        val filePath = list.getOrNull(deleteIndex)
        WechatFileUtil.deleteFile(listOfNotNull(filePath)) {
            YLog.i("DeleteDatabaseTask", "perform: delete database " +
                    "result: $it, count = $count, deleteIndex = $deleteIndex")
            if (it) {
                if (deleteIndex >= list.size - 1) {
                    callback?.invoke(true)
                } else {
                    performDeleteFile(list, 0, deleteIndex + 1, callback)
                }
            } else {
                if (count < 3) {
                    performDeleteFile(list, count + 1, deleteIndex, callback)
                } else {
                    showToast("文件删除失败")
                    callback?.invoke(false)
                    return@deleteFile
                }
            }
        }
    }

    private fun showToast(msg: String) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            ToastUtilsAssist.showCustomToast(App.getContext(), msg)
        } else {
            mainHandler.post {
                ToastUtilsAssist.showCustomToast(App.getContext(), msg)
            }
        }
    }
}