package com.yupao.wxcollect.service.procedure.task

import androidx.annotation.WorkerThread
import com.yupao.wxcollect.service.procedure.entity.ITaskResult
import com.yupao.wxcollect.service.procedure.entity.TaskRuntimeEntity

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/4/004</p>
 *
 * <AUTHOR>
 */
interface ITaskExecutor {

   @WorkerThread
   suspend fun perform(runtimeEntity: TaskRuntimeEntity): ITaskResult

}