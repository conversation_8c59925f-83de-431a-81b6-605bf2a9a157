package com.yupao.wxcollect.service.accessibility.process

import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.accessibility.filter.*
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.loginUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatLauncherUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatPackageName
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatSettingUI
import com.yupao.wxcollect.service.accessibility.process.IWechatTask.Companion.wechatSwitchAccountCls

/**
 * <AUTHOR>
 * @Date 2023/4/28/028 10:39
 * @Description 适配微信V7.X版本
 */
class WechatTaskV7(private val appType: List<String>?, private val wechatName: String?): IWechatTask {
    override fun getPageHandler(): MutableList<IPageHandler> {
        val index = if(appType?.contains(ReportConfig.APP_MAIN) == true) 0 else 1
        return mutableListOf(
            // 选择双开应用中的一个
            PageHandler(
                null,
                listOf("com.android.internal.app.ResolverActivity"),
                mutableListOf(
                    ClickViewHandler("微信", index),
                )
            ),
            // 首页-点击我
            PageHandler(
                wechatPackageName,
                listOf(wechatLauncherUI),
                mutableListOf(
                    ClickViewHandler("我", index = IViewHandler.LAST_ITEM),
                    ClickViewHandler("设置"),
                )
            ),
            // 设置-点击切换帐号
            PageHandler(
                wechatPackageName,
                listOf(wechatSettingUI),
                mutableListOf(
                    ClickViewHandler("切换帐号")
                )
            ),
            // 切换账号页面
            PageHandler(
                wechatPackageName,
                listOf(wechatSwitchAccountCls),
                mutableListOf(
                    // 切换账号页面-点击切换账号
                    ClickViewHandler("切换帐号", index = 1, brotherFilter = AccessibilityNodeInfoFilter(
                        className = AccessibilityNodeInfoFilter.IMAGE, offset = 1
                    )
                    ),
                    // 切换账号页面-点击头像登陆账号
                    ClickViewHandler(viewText = wechatName, brotherFilter = AccessibilityNodeInfoFilter(
                        className = AccessibilityNodeInfoFilter.IMAGE, offset = 0
                    )
                    )
                )
            ),
            // 需要返回的页面
            ReturnPageHandler(
                wechatPackageName,
                listOf(wechatSwitchAccountCls, wechatLauncherUI, wechatSettingUI, loginUI),
                null,
                null,
            ),
        )
    }
}