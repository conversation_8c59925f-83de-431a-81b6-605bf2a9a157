package com.yupao.wxcollect.service.procedure.task

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/25/025</p>
 *
 * <AUTHOR>
 */
object TaskCode {

    // 微信群好友
    const val WxGroupFriend = "wxGroupFriend"

    // 微信信息
    const val WxInfo = "wxInfo"

    // 公众号文章
    const val WxArticle = "wxArticle"

    // 群聊天记录
    const val WxGroupChat = "wxGroupChat"

    // 微信群
    const val WxGroup = "wxGroup"

    // 微信群好友(注发)
    const val WxGroupFriendRegister = "wxGroupFriendReg"

    // 微信群(注发)
    const val WxGroupRegister = "wxGroupReg"

}