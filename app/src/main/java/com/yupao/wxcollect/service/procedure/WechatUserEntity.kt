package com.yupao.wxcollect.service.procedure

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * <AUTHOR>
 * @Date 2023/4/26/026 13:36
 * @Description
 */
@Keep
@Entity(tableName = "user")
data class WechatUserEntity(
    // 用户id
    var username: String? = null,
    // 用户名
    var nickname: String? = null,
    // 别名
    var alias: String? = null,
    // 微信数据库路径
    val databasePath: String? = null,
    @PrimaryKey(autoGenerate = true)
    var id: Int? = null
)