package com.yupao.wxcollect.service.procedure.dispatch.transform

import com.yupao.wxcollect.service.procedure.entity.TaskExecutorConfig

/**
 * 对任务分组
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
class TaskTrainGroupTransform: ITaskTrainTransform {
    override fun transform(list: List<TaskExecutorConfig>): List<TaskExecutorConfig> {
        return list.groupBy {
            // 将数据库路径相同的任务合并
            it.databasePath
        }.mapNotNull {
            val taskConfigList = it.value
            if (taskConfigList.isEmpty()) {
                return@mapNotNull null
            }
            val config = taskConfigList.first()
            if (taskConfigList.size == 1) {
                return@mapNotNull config
            }
            val flatList = taskConfigList.flatMap { value -> value.configList }
            TaskExecutorConfig(
                databasePath = it.key,
                password = config.password,
                appType = config.appType,
                configList = flatList,
                isCopyForce = taskConfigList.any { item -> item.isCopyForce },
                // 任务执行超时时间取最大值
                timeoutTime = taskConfigList.maxOf { item -> item.timeoutTime },
                invalidTime = taskConfigList.maxOf { item -> item.invalidTime },
                strategy = config.strategy
            )
        }
    }
}