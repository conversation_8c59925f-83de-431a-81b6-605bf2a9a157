package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/12/13/013</p>
 *
 * <AUTHOR>
 */
@Keep
data class ReportNetModel(
    // 任务id
    val taskId: String,
    // 任务流程
    val execStep: String,
    // 任务执行数据
    val execData: Any?,
    // 单次任务执行唯一标识
    val recordUuid: String?,
    // 任务执行数据数量
    val execDataNum: Int?,
    // 上报数据的版本，非必须，diff上报的时候校验数据完整性
    val version: Int? = null,
) {
    companion object {
        const val Start = "start"

        const val CopyDb = "copyDb"

        const val Upload = "uploadOss"

        const val Succeed = "succeed"

        const val Failed = "failed"

        fun statusTask(taskId: String, status: String, recordUuid: String?): ReportNetModel {
            return ReportNetModel(taskId, status, null, recordUuid, null)
        }

        fun reportTask(taskId: String, data: Any?, totalCount: Int?): ReportNetModel {
            return ReportNetModel(taskId, Succeed, data, null, totalCount)
        }

        fun failedTask(taskId: String, message: String, recordUuid: String?): ReportNetModel {
            return ReportNetModel(taskId, Failed, message, recordUuid, null)
        }
    }
}