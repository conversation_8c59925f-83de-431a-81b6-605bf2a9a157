package com.yupao.wxcollect.service.accessibility.filter

import android.view.accessibility.AccessibilityEvent

/**
 * <AUTHOR>
 * @Date 2023/4/23/023 16:16
 * @Description
 */
interface IViewHandler: IHandlerStatus {
    companion object {
        // 无效时间
        const val INVALID_TIME = -1L

        // 默认延迟时间
        const val DEFAULT_DELAY_TIME = 500L

        // 默认长延迟时间
        const val DEFAULT_DELAY_LONG_TIME = 2000L

        // 匹配到的最后一个item
        const val LAST_ITEM = -1

        // 匹配到的当前item
        const val CURR_OFFSET = 0
    }

    // 上一个ViewHandler执行之后多久开始直接执行本ClickViewHandler
    val delayTimeAfterPrev: Long

    // 最大调用次数
    val maxPerformCount: Int

    var pageHandler: IPageHandler?

    suspend fun acceptEventType(event: AccessibilityEvent?): Boolean

    suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean

    fun isRemove(): Boolean

    fun getWillPerformCount(): Int
}