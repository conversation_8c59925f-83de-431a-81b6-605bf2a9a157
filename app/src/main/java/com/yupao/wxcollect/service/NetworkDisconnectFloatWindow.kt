package com.yupao.wxcollect.service

import android.view.Gravity
import android.widget.TextView
import com.lzf.easyfloat.EasyFloat
import com.lzf.easyfloat.anim.AppFloatDefaultAnimator
import com.lzf.easyfloat.enums.ShowPattern
import com.lzf.easyfloat.enums.SidePattern
import com.lzf.easyfloat.interfaces.OnInvokeView
import com.yupao.execute.wxcollect.R
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.ylog.YLog

/**
 * 网络断开提示悬浮窗
 *
 * <p>创建时间：2024/12/20</p>
 *
 * <AUTHOR>
 */
class NetworkDisconnectFloatWindow {
    
    companion object {
        private const val TAG = "NetworkDisconnectFloat"
        private const val FLOAT_TAG = "network-disconnect"
    }
    
    /**
     * 显示网络断开提示悬浮窗
     */
    fun showNetworkDisconnectFloat() {
        if (EasyFloat.appFloatIsShow(FLOAT_TAG)) {
            YLog.i(TAG, "网络断开悬浮窗已在显示")
            return
        }
        
        try {
            EasyFloat.with(AndroidSystemUtil.getContext())
                // 设置浮窗布局
                .setLayout(R.layout.layout_float_network_disconnect, OnInvokeView { view ->
                    // 设置提示文本
                    val textView = view.findViewById<TextView>(R.id.tv_network_status)
                    textView?.text = "⚠️ 网络已断开"
                })
                // 设置浮窗显示类型，一直显示
                .setShowPattern(ShowPattern.ALL_TIME)
                // 设置吸附方式，水平居中
                .setSidePattern(SidePattern.RESULT_HORIZONTAL)
                // 设置浮窗标签
                .setTag(FLOAT_TAG)
                // 设置浮窗不可拖拽（网络提示应该固定位置）
                .setDragEnable(false)
                // 不包含EditText
                .hasEditText(false)
                // 设置浮窗位置（屏幕顶部居中）
                .setGravity(Gravity.TOP or Gravity.CENTER_HORIZONTAL, 0, 100)
                // 设置宽高
                .setMatchParent(widthMatch = false, heightMatch = false)
                // 设置动画
                .setAppFloatAnimator(AppFloatDefaultAnimator())
                // 设置回调
                .registerCallback {
                    createResult { isCreated, msg, view ->
                        YLog.i(TAG, "网络断开悬浮窗创建结果: $isCreated, $msg")
                    }
                    show {
                        YLog.i(TAG, "网络断开悬浮窗显示")
                    }
                    hide { 
                        YLog.i(TAG, "网络断开悬浮窗隐藏")
                    }
                    dismiss {
                        YLog.i(TAG, "网络断开悬浮窗销毁")
                    }
                    touchEvent { view, motionEvent -> 
                        // 可以在这里处理点击事件，比如点击后隐藏
                    }
                }
                .show()
                
            YLog.i(TAG, "显示网络断开悬浮窗")
            
        } catch (e: Exception) {
            YLog.printException(TAG, e, "显示网络断开悬浮窗失败")
        }
    }
    
    /**
     * 显示网络恢复提示悬浮窗
     */
    fun showNetworkConnectedFloat() {
        if (EasyFloat.appFloatIsShow(FLOAT_TAG)) {
            YLog.i(TAG, "更新悬浮窗为网络已连接")
            
            // 更新现有悬浮窗的文本
            EasyFloat.getAppFloatView(FLOAT_TAG)?.let { view ->
                val textView = view.findViewById<TextView>(R.id.tv_network_status)
                textView?.text = "✅ 网络已恢复"
            }
            return
        }
        
        try {
            EasyFloat.with(AndroidSystemUtil.getContext())
                .setLayout(R.layout.layout_float_network_disconnect, OnInvokeView { view ->
                    val textView = view.findViewById<TextView>(R.id.tv_network_status)
                    textView?.text = "✅ 网络已恢复"
                })
                .setShowPattern(ShowPattern.ALL_TIME)
                .setSidePattern(SidePattern.RESULT_HORIZONTAL)
                .setTag(FLOAT_TAG)
                .setDragEnable(false)
                .hasEditText(false)
                .setGravity(Gravity.TOP or Gravity.CENTER_HORIZONTAL, 0, 100)
                .setMatchParent(widthMatch = false, heightMatch = false)
                .setAppFloatAnimator(AppFloatDefaultAnimator())
                .registerCallback {
                    createResult { isCreated, msg, view ->
                        YLog.i(TAG, "网络恢复悬浮窗创建结果: $isCreated, $msg")
                    }
                    show {
                        YLog.i(TAG, "网络恢复悬浮窗显示")
                    }
                    dismiss {
                        YLog.i(TAG, "网络恢复悬浮窗销毁")
                    }
                }
                .show()
                
            YLog.i(TAG, "显示网络恢复悬浮窗")
            
        } catch (e: Exception) {
            YLog.printException(TAG, e, "显示网络恢复悬浮窗失败")
        }
    }
    
    /**
     * 更新悬浮窗文本
     */
    fun updateFloatText(text: String) {
        try {
            EasyFloat.getAppFloatView(FLOAT_TAG)?.let { view ->
                val textView = view.findViewById<TextView>(R.id.tv_network_status)
                textView?.text = text
                YLog.i(TAG, "更新悬浮窗文本: $text")
            }
        } catch (e: Exception) {
            YLog.printException(TAG, e, "更新悬浮窗文本失败")
        }
    }
    
    /**
     * 隐藏悬浮窗
     */
    fun dismissFloatWindow() {
        try {
            if (EasyFloat.appFloatIsShow(FLOAT_TAG)) {
                EasyFloat.dismissAppFloat(FLOAT_TAG)
                YLog.i(TAG, "隐藏网络状态悬浮窗")
            }
        } catch (e: Exception) {
            YLog.printException(TAG, e, "隐藏悬浮窗失败")
        }
    }
    
    /**
     * 检查悬浮窗是否正在显示
     */
    fun isShowing(): Boolean {
        return try {
            EasyFloat.appFloatIsShow(FLOAT_TAG)
        } catch (e: Exception) {
            YLog.printException(TAG, e, "检查悬浮窗状态失败")
            false
        }
    }
}
