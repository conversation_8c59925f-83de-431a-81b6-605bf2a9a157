package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity

/**
 * <AUTHOR>
 * @Date 2023/4/17/017 16:05
 * @Description
 */
@Keep
data class GroupListSyncRequestEntity(
    val list: List<FriendUserEntity>?,
    @Transient
    val queryMin: String?,
    @Transient
    val userEntity: UserEntity?,
    @Transient
    val config: ReportConfig?,
): CommReportEntity(queryMin, userEntity, config)

@Keep
data class FriendUserEntity(
    // 微信好友昵称
    val friend_name: String?,
    //  添加时间（毫秒时间戳）
    val friend_id: String?,
    // 添加时间
    var add_time: String?,
    // 备注
    val conRemark: String?,
)