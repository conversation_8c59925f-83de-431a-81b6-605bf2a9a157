package com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.group

import com.yupao.utils.lang.json.GsonUtil
import com.yupao.wxcollect.database.DiffDatabaseModule
import com.yupao.wxcollect.service.db.IDiffDao
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.ICommProvider

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/11/11/011</p>
 *
 * <AUTHOR>
 */
class GroupDiffProvider(
    override val uniqueKey: String = "chatroomname",
    override val uniqueIgnoreKey: List<String>? = listOf("displayname"),
) : ICommProvider<GroupDiffLocalModel> {

    private val mainDao: GroupDiffDao by lazy {
        DiffDatabaseModule.mainDiffDatabase.getGroupDiffDao()
    }

    private val subDao: GroupDiffDao by lazy {
        DiffDatabaseModule.subDiffDatabase.getGroupDiffDao()
    }

    private val mainBackupDao: GroupDiffDao by lazy {
        DiffDatabaseModule.mainDiffBackupDatabase.getGroupDiffDao()
    }

    private val subBackupDao: GroupDiffDao by lazy {
        DiffDatabaseModule.subDiffBackupDatabase.getGroupDiffDao()
    }

    override fun getDao(appType: String, isBackup: Boolean): IDiffDao<GroupDiffLocalModel> {
        return if (isBackup) {
            if (appType == ReportConfig.APP_SUB) subBackupDao else mainBackupDao
        } else {
            if (appType == ReportConfig.APP_SUB) subDao else mainDao
        }
    }

    override fun clearTable(appType: String, isBackup: Boolean) {
        getDao(appType, isBackup).clearTable()
    }

    override fun createLocalModel(
        id: Int?,
        uniqueValue: String,
        uniqueCode: Int,
        map: Map<String, Any?>,
        updateTime: Long,
        version: Int
    ): GroupDiffLocalModel {
        return GroupDiffLocalModel().apply {
            this.id = id
            this.uniqueId = uniqueValue
            this.updateTime = updateTime
            this.version = version
            this.uniqueCode = uniqueCode
            this.ext = GsonUtil.toJson(map)
        }
    }
}