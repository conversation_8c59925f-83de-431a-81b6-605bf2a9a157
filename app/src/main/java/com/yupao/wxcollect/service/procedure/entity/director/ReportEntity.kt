package com.yupao.wxcollect.service.procedure.entity.director

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/15/015</p>
 *
 * <AUTHOR>
 */
@Keep
data class ReportEntity(
    val files: List<OssFileEntity>,
)

data class OssFileEntity(
    val filePath: String?,
    val fileId: String?,
    val fileType: String? = FileTypeCSV,
) {
    companion object {
        private const val FileTypeCSV = "COMPRESSED_CSV"
    }
}