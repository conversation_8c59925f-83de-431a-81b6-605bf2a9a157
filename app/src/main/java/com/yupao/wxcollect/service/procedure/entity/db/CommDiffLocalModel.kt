package com.yupao.wxcollect.service.procedure.entity.db

/**
 * 通用diff 实体
 *
 * <p>创建时间：2024/11/9/009</p>
 *
 * <AUTHOR>
 */
abstract class CommDiffLocalModel {
    // 唯一标识
    var uniqueId: String? = null
    // 更新时间
    var updateTime: Long? = null
    // 更新版本
    var version: Int? = null
    // 唯一标识
    var uniqueCode: Int? = null
    // 扩展字段
    var ext: String? = null
    abstract var id: Int?

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (other !is CommDiffLocalModel) return false

        if (uniqueId != other.uniqueId) return false

        return true
    }

    override fun hashCode(): Int {
        return uniqueId.hashCode()
    }
}