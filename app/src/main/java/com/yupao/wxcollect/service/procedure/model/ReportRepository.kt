package com.yupao.wxcollect.service.procedure.model

import com.google.gson.Gson
import com.yupao.data.net.media.MediaEntity
import com.yupao.data.net.yupao.JavaNetEntity
import com.yupao.data.protocol.Resource
import com.yupao.net.core.pro.ProMultipartBody
import com.yupao.net.core.pro.UploadProgressListener
import com.yupao.wxcollect.database.CommDatabase
import com.yupao.wxcollect.net.AppBusinessClient
import com.yupao.wxcollect.net.AppHttpClient
import com.yupao.wxcollect.service.procedure.api.OfficialAccountApi
import com.yupao.wxcollect.service.procedure.api.UploadApi
import com.yupao.wxcollect.service.procedure.api.WechatApi
import com.yupao.wxcollect.service.procedure.entity.PhoneNoNetEntity
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.ReportResultEntity
import com.yupao.wxcollect.service.procedure.entity.TaskConfig
import com.yupao.wxcollect.service.procedure.entity.WechatNetEntity
import com.yupao.wxcollect.service.procedure.entity.query.ArticleEntity
import com.yupao.wxcollect.service.procedure.entity.request.*
import com.yupao.wxcollect.service.procedure.entity.response.ValidIdsResponseEntity
import com.yupao.wxcollect.service.procedure.task.ReportType
import com.yupao.wxcollect.ui.config.model.ReportConfigLDS
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File
import javax.inject.Inject
import kotlin.math.ceil

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 14:40
 * @Description
 */
class ReportRepository @Inject constructor(
    private val reportConfigLDS: ReportConfigLDS?,
    private val commDatabase: CommDatabase? = null,
)  {
    companion object {
        private const val TAG = "ReportRepository"
    }

    private val reportRDS by lazy { ReportRDS() }

    private val articleLDS by lazy { OfficialArticleLDS(commDatabase) }

    private val reportResultDao by lazy {
        commDatabase?.getReportResultDao()
    }

    private val officialAccountApi by lazy {
        AppHttpClient.instanceForB.createApiService(OfficialAccountApi::class.java)
    }

    private val officialAccountApiForC by lazy {
        AppHttpClient.instanceForC.createApiService(OfficialAccountApi::class.java)
    }

    private val wechatApi by lazy {
        AppHttpClient.instanceShortTimeout.createApiService(WechatApi::class.java)
    }

    private val gson by lazy { Gson() }

    /**
     * 找到不同类型最后一次的结果
     */
    private fun queryLastResult(): List<ReportResultEntity> = kotlin.run {
        return@run ReportConfig.getAllAppType().flatMap { appType ->
            val mainList = ReportType.getAllReportType().mapNotNull {
                reportResultDao?.query(
                    size = 1,
                    appType = listOf(appType),
                    taskTypeList = ReportType.getTaskTypeList(it.key)
                )?.firstOrNull()
            }
            mainList.asIterable()
        }
    }

    /**
     * 查询所有配置, 同时重置为最新结果的dispatchTime
     */
    fun queryAllConfig() = reportConfigLDS?.queryAll()

    /**
     * 更新普通消息
     */
    suspend fun uploadChatHistory(entity: MessageRequestEntity?) = reportRDS.uploadChatHistory(entity)

    /**
     * 上传微信群信息-同步好友列表
     */
    suspend fun syncFriendTabulation(entity: GroupListSyncRequestEntity?) = reportRDS.syncFriendTabulation(entity)

    /**
     * 上传微信群信息-同步群操作
     */
    suspend fun syncGroupOperation(entity: GroupOperationSyncRequestEntity?) = reportRDS.syncGroupOperation(entity)

    /**
     *  上传微信群信息-同步被踢信息
     */
    suspend fun syncBootedOut(entity: GroupRemoveSyncRequestEntity?) = reportRDS.syncBootedOut(entity)

    /**
     * 上传微信群信息-同步群信息
     */
    suspend fun syncGroup(entity: GroupInfoSyncRequestEntity?) = reportRDS.syncGroup(entity)

    /**
     * 上传微信好友申请相关信息
     */
    suspend fun applyToAddFriends(entity: ApplyToAddFriendsParamsModel?) = reportRDS.applyToAddFriends(entity)

    /**
     * 上传添加好友
     */
    suspend fun addFriendsInfo(
        wxUserId: String?,
        wxNumber: String?,
        phoneNumber: String?,
        waiterNick: String?,
        friendsInfo: List<AddFriendsInfoItemEntity>?
    ) = reportRDS.addFriendsInfo(wxUserId, wxNumber, phoneNumber, waiterNick, friendsInfo)

    /**
     * 更新好友
     */
    suspend fun updateFriendsInfo(
        entity: UpdateFriendRequestEntity?
    ) = reportRDS.updateFriendsInfo(entity)

    /**
     * 删除好友
     */
    suspend fun deleteFriendsInfo(
        entity: DeleteFriendRequestEntity?
    ) = reportRDS.deleteFriendsInfo(entity)

    /**
     * 查询开始时间
     */
    suspend fun queryStartTime(
        entity: StartTimeRequestEntity?
    ) = officialAccountApi.queryStartTime(entity)

    /**
     * 微信号变更
     */
    suspend fun changeWechat(
        entity: ChangeWechatRequestModel?
    ) = reportRDS.api.changeWechat(entity)

    /**
     * 批量校验文章id是否有效
     */
    suspend fun validIds(
        entity: ValidIdsRequestEntity?
    ): JavaNetEntity<List<ValidIdsResponseEntity>>? {
        val size = entity?.articles?.size ?: 0
        var index = 0
        val count = 200
        YLog.i("ReportRepository", "validIds: size: $size, request count: ${ceil(size.toDouble() / count)}")
        val resultList = mutableListOf<ValidIdsResponseEntity>()
        var succeedEntity: JavaNetEntity<List<ValidIdsResponseEntity>>? = null
        var failedEntity: JavaNetEntity<List<ValidIdsResponseEntity>>? = null
        // 每200条请求一次，循环请求，忽略失败的请求，只保留成功的
        while (index < size) {
            val toIndex = size.coerceAtMost(index + count)
            if (toIndex > index) {
                val list = entity?.articles?.subList(index, toIndex)
                val realEntity = entity?.copy(
                    articles = list
                )
                val result: JavaNetEntity<List<ValidIdsResponseEntity>>? = try {
                    officialAccountApi.validIds(realEntity)
                } catch (e: Exception) {
                    e.printStackTrace()
                    YLog.printException(e)
                    null
                }
                if (result?.isOK() == true) {
                    resultList.addAll(result.getData() ?: listOf())
                    succeedEntity = result
                } else {
                    YLog.i("ReportRepository", "validIds failed: size: ${result?.getData()?.size}")
                    failedEntity = result
                }
            }
            index += count
        }
        YLog.i("ReportRepository", "validIds: result: ${succeedEntity != null}, size: ${resultList.size}")
        return succeedEntity?.copy(data = resultList) ?: failedEntity
    }

    /**
     * 采集数据上报
     */
    suspend fun collect(
        entity: OfficialArticleRequestData?
    ) = officialAccountApi.collect(entity)

    /**
     * 查询黑名单数据
     */
    suspend fun fetchBlackList(
        entity: BlackListRequestEntity?
    ) = officialAccountApiForC.fetchBlackList(entity)


    suspend fun urlContent(url: String, timeoutSecond: Int): String? {
        return wechatApi.urlContent(url, timeoutSecond).execute().body()?.string()
    }

    private val uploadService: UploadApi = AppBusinessClient.createApiService(UploadApi::class.java)

    /**
     * 请求上传资源
     * @param fileUploadSimple 参数实体
     */
    internal suspend fun upload(
        appId: String,
        fileUploadSimple: FileUploadParam
    ): MediaEntity<UploadEntity> {
        val format = WechatFileUtil.guessFormat(fileUploadSimple)
        val uploadRequestEntity = UploadRequestEntity(
            entryId = fileUploadSimple.entryId,
            type = fileUploadSimple.type,
            format = format,
            processType = fileUploadSimple.processType,
            takeTime = fileUploadSimple.takeTime,
            videoTime = fileUploadSimple.videoTime,
            city = fileUploadSimple.city,
            address = fileUploadSimple.address,
            wmId = fileUploadSimple.wmId,
            wmcInfo = fileUploadSimple.wmcInfo,
            uid = fileUploadSimple.uid,
            albumType = fileUploadSimple.albumType,
            busId = fileUploadSimple.busId,
            httpMethod = fileUploadSimple.httpMethod,
            extraInfo = fileUploadSimple.extraInfo
        )
        return uploadService.upload(appId, uploadRequestEntity)
    }

    suspend fun uploadFile(
        entity: YuPaoCloudRequestEntity,
        extHeaderMap: Map<String, String>,
        progress: ((Int, String?) -> Unit)? = null
    ): MediaEntity<UploadFileEntity> {
        val file = File(entity.fileInfo?.path.toString())
        val filePart = MultipartBody.Part.create(
            RequestBody.create(null, file)
        )
        val body = filePart.body
        val proRequestBody = ProMultipartBody(body, object : UploadProgressListener {
            override fun onProgress(pro: Long) {
                progress?.invoke(pro.toInt(), entity.fileInfo?.fileId)
            }
        })
        return uploadService.uploadFile(
            entity.remoteInfo?.extraInfo?.uploadUrlInfo?.uploadUrl ?: "",
            proRequestBody,
            (entity.remoteInfo?.extraInfo?.uploadUrlInfo?.headers ?: emptyMap()) + extHeaderMap
        )
    }

    suspend fun insertOfficialArticle(appType: String?, username: String?, phoneNum: String?, list: List<OfficialArticleRequestEntity>?)
            = articleLDS.insertOfficialArticle(appType, username, phoneNum, list)

    suspend fun fetchCacheOfficialArticle(appType: String?, username: String?): List<ArticleEntity>?
            = articleLDS.fetchCacheOfficialArticle(appType, username)

    suspend fun deleteArticle(appType: String?, username: String?, list: List<ArticleEntity>?) = articleLDS.deleteArticle(appType, username, list)

    suspend fun removeAllInvalidArticle() = articleLDS.removeAllInvalidArticle()

    /**
     * 通用的数据上报
     *
     * @param model
     * @return
     */
    suspend fun commReport(model: ReportNetModel): JavaNetEntity<Any> {
        return reportRDS.api.commonReport(model)
    }

    /**
     * 通用的数据上报
     *
     * @param model
     * @return
     */
    suspend fun queryByPhoneNo(phoneNumber: String): JavaNetEntity<PhoneNoNetEntity> {
        return reportRDS.api.queryByPhoneNo(WechatAccountModel(phoneNumber))
    }
}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface ReportRepositoryEntryPoint {
    fun getReportRepository(): ReportRepository
}