package com.yupao.wxcollect.service

import com.yupao.keepalive.KeepAliveFloatWindow
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 系统状态管理器
 * 管理网络状态、内存状态等系统状态变化，并通过悬浮窗进行提示
 *
 * <p>创建时间：2024/12/20</p>
 *
 * <AUTHOR>
 */
object SystemStatusManager {

    private const val TAG = "SystemStatusManager"

    // 悬浮窗显示时长（毫秒）
    private const val FLOAT_SHOW_DURATION = 3000L

    private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())
    private val floatWindow by lazy { KeepAliveFloatWindow() }

    // 状态检查任务
    private var statusCheckJob: Job? = null

    // 是否正在显示悬浮窗
    private val isShowingFloat = AtomicBoolean(false)

    // 待处理的状态消息队列
    private val statusMessageQueue = ConcurrentLinkedQueue<StatusMessage>()


    /**
     * 状态消息数据类
     */
    data class StatusMessage(
        val type: StatusType,
        val message: String,
        val priority: Int = 0, // 优先级，数字越大优先级越高
        val timestamp: Long = System.currentTimeMillis()
    )

    /**
     * 状态类型枚举
     */
    enum class StatusType(val displayName: String) {
        NETWORK("网络状态"),
    }

    /**
     * 初始化状态管理器
     */
    fun initialize() {
        YLog.i(TAG, "初始化系统状态管理器")

        // 设置悬浮窗点击回调
        floatWindow.setOnClickListener {
            YLog.i(TAG, "悬浮窗被点击，显示下一条消息")
            showNextMessage()
        }
    }

    /**
     * 销毁状态管理器
     */
    fun destroy() {
        YLog.i(TAG, "销毁系统状态管理器")

        // 停止状态检查
        statusCheckJob?.cancel()

        // 清空消息队列
        statusMessageQueue.clear()

        // 隐藏悬浮窗
        floatWindow.resetFloatWindow()

        YLog.i(TAG, "系统状态管理器销毁完成")
    }

    /**
     * 添加状态消息
     */
    fun addStatusMessage(type: StatusType, message: String, priority: Int = 0) {
        val statusMessage = StatusMessage(type, message, priority)

        // 检查是否已存在相同类型的消息
        if (statusMessageQueue.any { it.type == type }) {
            return
        }
        statusMessageQueue.offer(statusMessage)

        YLog.i(TAG, "添加状态消息: ${type.displayName} - $message")

        // 如果当前没有显示悬浮窗，立即显示
        if (!isShowingFloat.get()) {
            showNextStatusMessage()
        }
    }

    /**
     * 移除指定类型的状态消息
     */
    fun removeStatusMessage(type: StatusType) {
        val removed = statusMessageQueue.removeAll { it.type == type }
        if (removed) {
            YLog.i(TAG, "移除状态消息: ${type.displayName}")
        }
    }

    /**
     * 清空所有状态消息
     */
    fun clearAllStatusMessages() {
        statusMessageQueue.clear()
        YLog.i(TAG, "清空所有状态消息")
    }

    /**
     * 显示下一条状态消息
     */
    private fun showNextStatusMessage() {
        coroutineScope.launch {
            // 检查是否开启提示
            val isTipsEnabled = withContext(Dispatchers.IO) {
                KVHelper.isClashTipsDb()
            }

            if (!isTipsEnabled) {
                YLog.i(TAG, "悬浮窗提示已关闭，跳过显示")
                return@launch
            }

            // 获取优先级最高的消息
            val nextMessage = getNextPriorityMessage()
            if (nextMessage == null) {
                YLog.i(TAG, "没有待显示的状态消息")
                // 如果没有消息了，隐藏悬浮窗
                floatWindow.resetFloatWindow()
                isShowingFloat.set(false)
                return@launch
            }
            // 设置显示状态
            isShowingFloat.set(true)
            try {
                // 显示悬浮窗
                floatWindow.showFloatWindow()
                floatWindow.updateFloatWindow(nextMessage.message)

                YLog.i(
                    TAG,
                    "显示状态消息: ${nextMessage.type.displayName} - ${nextMessage.message}"
                )

                // 移除已显示的消息
                statusMessageQueue.remove(nextMessage)

            } catch (e: Exception) {
                YLog.printException(TAG, e, "显示状态消息失败")
                isShowingFloat.set(false)
            }
        }
    }

    /**
     * 获取下一个优先级最高的消息
     */
    private fun getNextPriorityMessage(): StatusMessage? {
        return statusMessageQueue.maxByOrNull { it.priority }
    }

    /**
     * 手动触发显示下一条消息（点击悬浮窗时调用）
     */
    fun showNextMessage() {
        coroutineScope.launch {
            // 检查是否开启提示
            val isTipsEnabled = withContext(Dispatchers.IO) {
                KVHelper.isClashTipsDb()
            }

            if (!isTipsEnabled) {
                YLog.i(TAG, "悬浮窗提示已关闭，隐藏悬浮窗")
                floatWindow.resetFloatWindow()
                isShowingFloat.set(false)
                return@launch
            }

            // 获取下一条消息
            val nextMessage = getNextPriorityMessage()
            if (nextMessage == null) {
                YLog.i(TAG, "没有更多待显示的消息，隐藏悬浮窗")
                floatWindow.resetFloatWindow()
                isShowingFloat.set(false)
                return@launch
            }

            try {
                // 更新悬浮窗内容
                floatWindow.updateFloatWindow(nextMessage.message)
                YLog.i(
                    TAG,
                    "点击显示下一条消息: ${nextMessage.type.displayName} - ${nextMessage.message}"
                )

                // 移除已显示的消息
                statusMessageQueue.remove(nextMessage)

            } catch (e: Exception) {
                YLog.printException(TAG, e, "显示下一条消息失败")
            }
        }
    }

    /**
     * 获取当前待处理消息数量
     */
    fun getPendingMessageCount(): Int {
        return statusMessageQueue.size
    }

    /**
     * 获取所有待处理消息
     */
    fun getAllPendingMessages(): List<StatusMessage> {
        return statusMessageQueue.toList().sortedByDescending { it.priority }
    }
//
//    /**
//     * 手动添加自定义状态消息
//     */
//    fun addCustomMessage(message: String, priority: Int = 5) {
//        addStatusMessage(StatusType.SERVICE, message, priority)
//    }
//
//    /**
//     * 添加权限相关消息
//     */
//    fun addPermissionMessage(message: String) {
//        addStatusMessage(StatusType.PERMISSION, message, priority = 9)
//    }
//
//    /**
//     * 添加服务相关消息
//     */
//    fun addServiceMessage(message: String) {
//        addStatusMessage(StatusType.SERVICE, message, priority = 8)
//    }

    /**
     * 强制显示消息（忽略开关设置）
     */
    fun forceShowMessage(type: StatusType, message: String, priority: Int = 10) {
        val statusMessage = StatusMessage(type, message, priority)
        statusMessageQueue.removeAll { it.type == type }
        statusMessageQueue.offer(statusMessage)

        YLog.i(TAG, "强制显示消息: ${type.displayName} - $message")

        // 强制显示，不检查开关
        coroutineScope.launch {
            isShowingFloat.set(true)
            try {
                floatWindow.showFloatWindow()
                floatWindow.updateFloatWindow(message)
                statusMessageQueue.remove(statusMessage)

                if (statusMessageQueue.isEmpty()) {
                    delay(FLOAT_SHOW_DURATION)
                    floatWindow.resetFloatWindow()
                    isShowingFloat.set(false)
                }
            } catch (e: Exception) {
                YLog.printException(TAG, e, "强制显示消息失败")
                isShowingFloat.set(false)
            }
        }
    }
}
