package com.yupao.wxcollect.service

import com.yupao.keepalive.KeepAliveFloatWindow
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 状态管理器
 * <p>创建时间：2024/12/20</p>
 *
 * <AUTHOR>
 */
object SystemStatusManager {

    private const val TAG = "SystemStatusManager"

    private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())

    // 状态检查任务
    private var statusCheckJob: Job? = null

    // 是否正在显示悬浮窗
    private val isShowingFloat = AtomicBoolean(false)

    // 待处理的状态消息队列
    private val statusMessageQueue = ConcurrentLinkedQueue<StatusMessage>()


    /**
     * 状态消息数据类
     */
    data class StatusMessage(
        val type: StatusType,
        val message: String,
        val priority: Int = 0, // 优先级，数字越大优先级越高
        val timestamp: Long = System.currentTimeMillis()
    )

    /**
     * 状态类型枚举
     */
    enum class StatusType(val displayName: String) {
        NETWORK("网络状态"),
        STORAGE_FULL("En过大(大于2G)"),
        STORAGE_LOW("内存不足(不足2G)"),
        TASK_TIMEOUT("任务超时"),//群聊消息，好友申请；
        FILE_CORRUPTED("文件破损"),
        APP_WAKEUP_FAILED("导演/执行app唤醒失败"),//对应"导演/执行app唤醒失败"
        ROOT_PERMISSION_DENIED("Root权限不足"),//对应"Root未权限"
        DATABASE_COPY_FAILED("数据库拷贝失败"),//对应"数据库拷贝失败"
    }

    /**
     * 初始化状态管理器
     */
    fun initialize() {
        YLog.i(TAG, "初始化系统状态管理器")

        KeepAliveFloatWindow.initWindowInitCallback {
            showNextStatusMessage()
        }
        // 设置悬浮窗点击回调
        KeepAliveFloatWindow.setOnClickListener {
            YLog.i(TAG, "=== 悬浮窗被点击 ===")
            YLog.i(TAG, "当前待处理消息数量: ${statusMessageQueue.size}")
            YLog.i(TAG, "悬浮窗显示状态: ${isShowingFloat.get()}")
            showNextMessage()
        }
    }

    /**
     * 销毁状态管理器
     */
    fun destroy() {
        YLog.i(TAG, "销毁系统状态管理器")

        // 停止状态检查
        statusCheckJob?.cancel()

        // 清空消息队列
        statusMessageQueue.clear()

        // 隐藏悬浮窗
        KeepAliveFloatWindow.resetFloatWindow()

        YLog.i(TAG, "系统状态管理器销毁完成")
    }

    /**
     * 添加状态消息
     */
    fun addStatusMessage(type: StatusType, message: String, priority: Int = 0) {
        val statusMessage = StatusMessage(type, message, priority)

        // 检查是否已存在相同类型的消息
        if (statusMessageQueue.any { it.type == type }) {
            return
        }
        statusMessageQueue.offer(statusMessage)

        YLog.i(TAG, "添加状态消息: ${type.displayName} - $message")

        // 如果当前没有显示悬浮窗，立即显示
        if (!isShowingFloat.get()) {
            showNextStatusMessage()
        }
    }

    /**
     * 移除指定类型的状态消息
     */
    fun removeStatusMessage(type: StatusType) {
        val removed = statusMessageQueue.removeAll { it.type == type }
        if (removed) {
            YLog.i(TAG, "移除状态消息: ${type.displayName}")
        }
    }

    /**
     * 清空所有状态消息
     */
    fun clearAllStatusMessages() {
        statusMessageQueue.clear()
        YLog.i(TAG, "清空所有状态消息")
    }

    /**
     * 显示下一条状态消息
     */
    private fun showNextStatusMessage() {
        //判断是否初始化显示了布局
        if (!KeepAliveFloatWindow.isInitialized()) {
            return
        }
        coroutineScope.launch {
            // 检查是否开启提示
            val isTipsEnabled = withContext(Dispatchers.IO) {
                KVHelper.isClashTipsDb()
            }

            if (!isTipsEnabled) {
                YLog.i(TAG, "悬浮窗提示已关闭，跳过显示")
                return@launch
            }

            // 获取优先级最高的消息
            val nextMessage = getNextPriorityMessage()
            if (nextMessage == null) {
                YLog.i(TAG, "没有待显示的状态消息")
                // 如果没有消息了，隐藏悬浮窗
                withContext(Dispatchers.Main) {
                    KeepAliveFloatWindow.resetFloatWindow()
                    isShowingFloat.set(false)
                }
                return@launch
            }
            // 设置显示状态
            isShowingFloat.set(true)
            try {
                // 显示悬浮窗
                withContext(Dispatchers.Main) {
                    KeepAliveFloatWindow.showFloatWindow()
                    KeepAliveFloatWindow.updateFloatWindow(nextMessage.message)
                }
                YLog.i(
                    TAG,
                    "显示状态消息: ${nextMessage.type.displayName} - ${nextMessage.message}"
                )
                // 移除已显示的消息
                statusMessageQueue.remove(nextMessage)

            } catch (e: Exception) {
                YLog.printException(TAG, e, "显示状态消息失败")
                isShowingFloat.set(false)
            }
        }
    }

    /**
     * 获取下一个优先级最高的消息
     */
    private fun getNextPriorityMessage(): StatusMessage? {
        return statusMessageQueue.maxByOrNull { it.priority }
    }

    /**
     * 手动触发显示下一条消息（点击悬浮窗时调用）
     */
    fun showNextMessage() {
        YLog.i(TAG, "点击手动触发显示下一条消息")

        coroutineScope.launch {
            try {
                // 检查是否开启提示
                val isTipsEnabled = withContext(Dispatchers.IO) {
                    KVHelper.isClashTipsDb()
                }

                if (!isTipsEnabled) {
                    YLog.i(TAG, "悬浮窗提示已关闭，隐藏悬浮窗")
                    KeepAliveFloatWindow.resetFloatWindow()
                    isShowingFloat.set(false)
                    return@launch
                }

                // 获取下一条消息
                val nextMessage = getNextPriorityMessage()
                YLog.i(TAG, "获取到的下一条消息: $nextMessage")

                if (nextMessage == null) {
                    YLog.i(TAG, "没有更多待显示的消息，隐藏悬浮窗")
                    KeepAliveFloatWindow.resetFloatWindow()
                    isShowingFloat.set(false)
                    return@launch
                }

                // 移除已显示的消息（先移除，避免重复显示）
                statusMessageQueue.remove(nextMessage)

                // 更新悬浮窗内容
                KeepAliveFloatWindow.updateFloatWindow(nextMessage.message)
                YLog.i(
                    TAG,
                    "点击显示下一条消息: ${nextMessage.type.displayName} - ${nextMessage.message}"
                )
//                // 如果没有更多消息，设置自动隐藏
//                if (statusMessageQueue.isEmpty()) {
//                    YLog.i(TAG, "这是最后一条消息，${FLOAT_SHOW_DURATION}ms 后自动隐藏")
//                    delay(FLOAT_SHOW_DURATION)
//                    KeepAliveFloatWindow.resetFloatWindow()
//                    isShowingFloat.set(false)
//                }

            } catch (e: Exception) {
                YLog.printException(TAG, e, "显示下一条消息失败")
                isShowingFloat.set(false)
            }
        }
    }
}
