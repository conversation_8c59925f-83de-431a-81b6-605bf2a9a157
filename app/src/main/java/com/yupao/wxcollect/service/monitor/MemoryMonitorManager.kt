package com.yupao.wxcollect.service.monitor

import com.yupao.wxcollect.service.SystemStatusManager
import com.yupao.ylog.YLog

/**
 * 内存监控管理器
 * 集成内存监控和系统状态管理
 *
 * <p>创建时间：2024/12/20</p>
 *
 * <AUTHOR>
 */
object MemoryMonitorManager {
    
    private const val TAG = "MemoryMonitorManager"
    
    private val memoryMonitor = MemoryMonitor()
    private var isInitialized = false
    
    /**
     * 初始化内存监控管理器
     */
    fun initialize() {
        if (isInitialized) {
            YLog.w(TAG, "内存监控管理器已初始化")
            return
        }
        
        YLog.i(TAG, "初始化内存监控管理器")
        
        // 添加内存监听器
        memoryMonitor.addMemoryListener(object : MemoryMonitor.MemoryStatusListener {
            override fun onMemoryStatusUpdate(memoryInfo: MemoryMonitor.MemoryInfo) {
                // 记录内存状态更新
                YLog.d(TAG, "内存状态更新: $memoryInfo")
            }
            
            override fun onMemoryWarning(memoryInfo: MemoryMonitor.MemoryInfo) {
                YLog.w(TAG, "内存警告: $memoryInfo")
                // 通过 SystemStatusManager 显示悬浮窗提示
                SystemStatusManager.addStatusMessage(
                    SystemStatusManager.StatusType.STORAGE_LOW,
                    "内存使用率较高(${String.format("%.1f", memoryInfo.usagePercentage * 100)}%)，建议清理后台应用",
                    priority = 8
                )
            }

            override fun onMemoryNormal(memoryInfo: MemoryMonitor.MemoryInfo) {
                YLog.i(TAG, "内存状态正常: $memoryInfo")
                
                // 如果之前有警告，现在恢复正常，移除警告消息
                SystemStatusManager.removeStatusMessage(SystemStatusManager.StatusType.STORAGE_LOW)
                YLog.i(TAG, "内存状态已恢复正常，移除警告消息")
            }
        })
        
        isInitialized = true
        YLog.i(TAG, "内存监控管理器初始化完成")
    }
    
    /**
     * 启动内存监控
     */
    fun startMonitoring() {
        if (!isInitialized) {
            initialize()
        }
        
        memoryMonitor.startMonitoring()
        YLog.i(TAG, "内存监控已启动")
    }
    
    /**
     * 停止内存监控
     */
    fun stopMonitoring() {
        memoryMonitor.stopMonitoring()
        YLog.i(TAG, "内存监控已停止")
    }
    
    /**
     * 手动检查内存状态
     */
    suspend fun checkMemoryNow(): MemoryMonitor.MemoryInfo? {
        return memoryMonitor.checkMemoryStatus()
    }
    
    /**
     * 获取当前监控状态
     */
    fun isMonitoring(): Boolean {
        return memoryMonitor.isMonitoring()
    }

    /**
     * 添加自定义内存监听器
     */
    fun addMemoryListener(listener: MemoryMonitor.MemoryStatusListener) {
        memoryMonitor.addMemoryListener(listener)
    }
    
    /**
     * 移除自定义内存监听器
     */
    fun removeMemoryListener(listener: MemoryMonitor.MemoryStatusListener) {
        memoryMonitor.removeMemoryListener(listener)
    }
    
    /**
     * 销毁管理器
     */
    fun destroy() {
        memoryMonitor.destroy()
        isInitialized = false
        YLog.i(TAG, "内存监控管理器已销毁")
    }
}
