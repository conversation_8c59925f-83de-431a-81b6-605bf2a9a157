package com.yupao.wxcollect.service.procedure.entity.query

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.WechatUserEntity
import com.yupao.wxcollect.service.procedure.entity.request.CommonReportEntity2

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/1/3/003</p>
 *
 * <AUTHOR>
 */
@Keep
data class CustomizeUploadRequestEntity(
    @Transient
    val userEntity: UserEntity?,
    val mobileNumber: String?,
    @Transient
    val appType: String?,
    val taskUnicode: String?,
    val list: List<String>?
): CommonReportEntity2(userEntity, appType)