package com.yupao.wxcollect.service.procedure.entity.query

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 17:28
 * @Description 需要查询消息的实体 不要轻易修改属性名称
 */
@Keep
data class MessageQueryEntity(
    // 群名
    val nickname: String? = null,
    // 聊天内容
    val content: String? = null,
    // 创建时间
    val createTime: String? = null,
    // 群ID
    val talker: String? = null,
//    val memberCount: String? = null,
)