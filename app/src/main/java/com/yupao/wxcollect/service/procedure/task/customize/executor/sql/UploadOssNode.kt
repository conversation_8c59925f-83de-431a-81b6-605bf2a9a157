package com.yupao.wxcollect.service.procedure.task.customize.executor.sql

import androidx.annotation.Keep
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.App
import com.yupao.wxcollect.constant.UPLOAD_APP_ID
import com.yupao.wxcollect.constant.UPLOAD_ENTRY_ID
import com.yupao.wxcollect.net.HttpConstant
import com.yupao.wxcollect.service.procedure.RetryHelper
import com.yupao.wxcollect.service.procedure.entity.director.OssFileEntity
import com.yupao.wxcollect.service.procedure.entity.request.FileType
import com.yupao.wxcollect.service.procedure.entity.request.FileUploadParam
import com.yupao.wxcollect.service.procedure.entity.request.YuPaoCloudRequestEntity
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.service.procedure.notNullResult
import com.yupao.wxcollect.util.NetworkUtil
import com.yupao.ylog.YLog
import java.io.File

/**
 * 文件上传到oss
 *
 * <p>创建时间：2024/1/3/003</p>
 *
 * <AUTHOR>
 */
class UploadOssNode (
    private val resp: ReportRepository,
    private val tag: String?,
) {

    suspend fun perform(pathList: List<String>, timeoutSecond: Int?): List<UploadEntity> {
        if (pathList.isEmpty()) {
            YLog.e(tag, "performUpload, path is empty.")
            return emptyList()
        }
        val extHeaderMap = if(timeoutSecond != null) mapOf(HttpConstant.Key.Timeout to timeoutSecond.toString()) else emptyMap()
        val result = pathList.mapNotNull { path ->
            // 如果非网络错误上传失败，重试一次
            val retryHelper = RetryHelper<UploadEntity>(1) { _, _ ->
                !NetworkUtil.isNetworkConnected(App.getContext())
            }
            retryHelper.notNullResult {
                val param = FileUploadParam(
                    entryId = UPLOAD_ENTRY_ID,
                    type = FileType.FILE,
                    path = path,
                    fileId = path,
                    uid = null,
                )
                val upload = resp.upload(UPLOAD_APP_ID, param)
                val resp = resp.uploadFile(YuPaoCloudRequestEntity(param, upload.getData()), extHeaderMap)
                YLog.i(tag, "oss upload result, code: ${resp.getCode()}, msg: ${resp.getMsg()}, path: $path")
                if (resp.isOK()) {
                    // 上传成功就删除对应的文件
                    kotlin.runCatching {
                        File(path).delete()
                    }
                    UploadEntity(upload.getData()?.url, upload.getData()?.resourceId)
                } else
                    null
            }?.onFailure {
                YLog.printException(tag, it, path)
                // 如果上传失败直接将错误抛出去
                if (!NetworkUtil.isNetworkConnected(AndroidSystemUtil.getContext())) {
                    throw RuntimeException("上传失败: 网络断开连接")
                } else {
                    throw RuntimeException("上传失败: ${it.message}")
                }
            }?.getOrNull()
        }
        return result
    }
}

@Keep
data class UploadEntity(
    val url: String?,
    val resourceId: String?
) {
    fun toOssFileEntity() : OssFileEntity {
        return OssFileEntity(url, resourceId)
    }
}