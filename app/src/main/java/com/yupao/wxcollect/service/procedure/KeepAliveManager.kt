package com.yupao.wxcollect.service.procedure

import com.yupao.transmit.MessageConstant
import com.yupao.transmit.MessageManager
import com.yupao.transmit.handle.heartbeat.HeartBeatBackMessageHandler
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.wxcollect.util.KVUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.launch

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/17/017</p>
 *
 * <AUTHOR>
 */
object KeepAliveManager {
    private const val TAG = "KeepAliveManager"

    private val heartBeatSendMessageHandler by lazy {
        HeartBeatBackMessageHandler {
            val host = it.host
            YLog.w(TAG, "message transmit interrupt: $host, " +
                    "dispatch time: ${TimeUtil.formatCurrTime(it.dispatchTime)}, drop count: ${it.dropCount}")
            if (host == CoreClient.serviceHost) {
                CoroutinePool.coreTask.launch {
                    val isOpenAlive = KVUtil.isOpenKeepAlive()
                    // 超过5次则启动页面，否则启动服务
                    if (isOpenAlive && it.dropCount >= 5) {
                        CmdUtil.execute("am start -n ${CoreClient.servicePackageName}/${CoreClient.activityName}")
                    } else {
                        CmdUtil.execute("am startservice -n ${CoreClient.servicePackageName}/${CoreClient.serviceName}")
                    }
                }
            } else {
                // 待添加
            }
        }
    }

    fun wakeup() {
        kotlin.runCatching {
            heartBeatSendMessageHandler.checkHeartBeat()
        }.onFailure {
            YLog.printException(TAG, it)
        }
    }

    fun perform() {
        MessageManager.handler.registerHandler(MessageConstant.Code.HeartbeatSend, heartBeatSendMessageHandler)
    }

}