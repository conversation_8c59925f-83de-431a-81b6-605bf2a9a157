package com.yupao.wxcollect.service.accessibility.filter

import android.view.accessibility.AccessibilityNodeInfo
import com.yupao.ylog.YLog
import java.lang.StringBuilder

/**
 * <AUTHOR>
 * @Date 2023/5/4/004 18:42
 * @Description
 */
object AccessibilityNodeInfoUtil {

    fun findAccessibilityNodeByIndex(index: Int, list: List<AccessibilityNodeInfo>): AccessibilityNodeInfo? {
        return if (index == IViewHandler.LAST_ITEM) {
            list.lastOrNull()
        } else {
            list.getOrNull(index)
        }
    }

    private fun getAllChildNodeInfo(nodeInfo: AccessibilityNodeInfo?, isGreedyMode: Boolean): List<AccessibilityNodeInfo> {
        val list = mutableListOf<AccessibilityNodeInfo>()
        if (nodeInfo == null) {
            return list
        }
        val childCount = nodeInfo.childCount
        if (childCount == 0) {
            list.add(nodeInfo)
            return list
        }
        repeat(childCount) {
            val child = nodeInfo.getChild(it)
            if (!isGreedyMode) {
                list.add(child)
            } else {
                val childList = getAllChildNodeInfo(child, true)
                list.addAll(childList)
            }
        }
        return list
    }

    fun findAccessibilityNodeByBrotherIndex(nodeInfo: AccessibilityNodeInfo?, brotherFilter: AccessibilityNodeInfoFilter?): AccessibilityNodeInfo? {
        if (nodeInfo == null) {
            return null
        }
        if(brotherFilter == null) {
            return nodeInfo
        }
        val className = brotherFilter.className
        val offset = brotherFilter.offset ?: 0
        nodeInfo.parent?.let { info ->
            val prevList = mutableListOf<AccessibilityNodeInfo>()
            val nextList = mutableListOf<AccessibilityNodeInfo>()
            var isFoundCurrNodeInfo = false
            val list = getAllChildNodeInfo(info, brotherFilter.isGreedyMode)
            YLog.i("ClickViewHandler", "found size = ${list.size}")
            list.forEach { child ->
                if (child == nodeInfo) {
                    isFoundCurrNodeInfo = true
                } else if (!isFoundCurrNodeInfo) {
                    if (className == null || className == child.className) {
                        prevList.add(child)
                    }
                } else {
                    if (className == null || className == child.className) {
                        nextList.add(child)
                    }
                }
            }
            val entity = if (offset > 0) {
                nextList.getOrNull(offset)
            } else if(offset < 0){
                prevList.getOrNull(offset)
            } else nodeInfo
            return entity
        }
        return null
    }

    fun findScrollableView(nodeInfo: AccessibilityNodeInfo?): List<AccessibilityNodeInfo>? {
        if (nodeInfo == null) {
            return null
        }
        val list = mutableListOf<AccessibilityNodeInfo>()
        if (isAcceptScrollNode(nodeInfo)) {
            list.add(nodeInfo)
        }
        val childCount = nodeInfo.childCount
        if (childCount == 0) {
            return list
        }
        repeat(childCount) {
            val child = nodeInfo.getChild(it)
            if (isAcceptScrollNode(child)) {
                list.add(child)
            }
            val childList = findScrollableView(child)
            if (childList != null && childList.isNotEmpty()) {
                list.addAll(childList)
            }
        }
        return list
    }

    private fun isAcceptScrollNode(nodeInfo: AccessibilityNodeInfo?): Boolean {
        nodeInfo ?: return false
        if (nodeInfo.isScrollable && nodeInfo.className != "com.tencent.mm.ui.mogic.WxViewPager") {
            return true
        }
        return false
    }

    fun printNodeInfo(nodeInfo: AccessibilityNodeInfo?) {
        val sb = StringBuilder()
        printNodeInfo(nodeInfo, "   ", sb)
        YLog.i("AccessibilityNodeInfoUtil", "printNodeInfo: \n$sb")
    }

    private fun printNodeInfo(nodeInfo: AccessibilityNodeInfo?, level: String, sb: StringBuilder) {
        nodeInfo ?: return
        sb.append(level).append(nodeInfo.className).append("(").append(nodeInfo.text ?: "").append(")").append("\n")
        val childCount = nodeInfo.childCount
        if (childCount == 0) {
            return
        }
        repeat(childCount) {
            val child = nodeInfo.getChild(it)
            printNodeInfo(child, level + level, sb)
        }
    }
}