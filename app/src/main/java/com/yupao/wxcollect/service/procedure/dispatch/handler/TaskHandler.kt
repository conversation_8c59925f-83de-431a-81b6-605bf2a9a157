package com.yupao.wxcollect.service.procedure.dispatch.handler

import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.procedure.dispatch.TaskExecutorFactory
import com.yupao.wxcollect.service.procedure.dispatch.status.IStatusChanged
import com.yupao.wxcollect.service.procedure.dispatch.status.TrainExecutorStatus
import com.yupao.wxcollect.service.procedure.entity.ITaskConfig
import com.yupao.wxcollect.service.procedure.entity.ITaskResult
import com.yupao.wxcollect.service.procedure.entity.SimpleResultEntity
import com.yupao.wxcollect.service.procedure.entity.TaskRuntimeEntity
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.WriteToFileNode
import com.yupao.wxcollect.service.procedure.task.official.OfficialArticleExecutor
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.Collections
import java.util.concurrent.CancellationException
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 任务处理
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
class TaskHandler(
    flag: String,
    private val statusHandler: IStatusChanged,
) {

    private val tag = "TaskHandler:$flag"

    private val factory by lazy { TaskExecutorFactory() }

    // 任务列表, key: unique, value: 任务执行器
    private val taskList = mutableMapOf<String, Job>()

    // 取消任务Unique列表
    private val cancelTaskUniqueList = Collections.synchronizedSet(mutableSetOf<CancelEntity>())

    // 是否取消所有任务
    private val isCancelAllTask = AtomicReference<CancelEntity?>(null)

    // 任务配置列表
    private val configList = mutableListOf<ITaskConfig>()

    // 已完成任务列表，包括成功和失败
    private val completedTaskList = Collections.synchronizedList(mutableListOf<ITaskConfig>())

    // 是否完成
    private val isFinished = AtomicBoolean(false)

    private fun findCancelEntity(unique: String): CancelEntity? {
        return cancelTaskUniqueList.find { it.unique == unique }
    }

    suspend fun perform(list: List<ITaskConfig>, runtimeEntity: TaskRuntimeEntity) {
        if (isFinished.get()) return
        val taskCount = list.size
        configList.clear()
        configList.addAll(configList)
        runBlocking {
            val resultList = mutableListOf<ITaskResult>()
            list.map { config ->
                launch (Dispatchers.IO) {
                    val cancelAllEntity = isCancelAllTask.get()
                    if (cancelAllEntity != null) {
                        resultList.add(complete(config, SimpleResultEntity.canceled(cancelAllEntity.reason), true))
                        return@launch
                    }
                    val cancelEntity = findCancelEntity(config.unique)
                    if (cancelEntity != null) {
                        resultList.add(complete(config, SimpleResultEntity.canceled(cancelEntity.reason), true))
                        return@launch
                    }
                    val executor = factory.getExecutor(config)
                        ?: kotlin.run {
                            YLog.w(tag, "${config.name}|${config.type}|${config.unique} not support")
                            resultList.add(complete(config, SimpleResultEntity.failed("不支持的任务类型"), true))
                            return@launch
                        }
                    YLog.i(tag, "execute task: ${config.name}|${config.type}|${config.unique}: ${executor.javaClass.simpleName}")
                    val result = kotlin.runCatching {
                        val result = executor.perform(runtimeEntity)
                        // 完成单次任务
                        complete(config, result, false)
                    }.getOrElse {
                        if (it is CancellationException) {
                            val result = SimpleResultEntity.canceled(it.message)
                            // 必须单独启一个协程，否则取消之后不会执行！
                            CoroutinePool.coreTask.launch(Dispatchers.IO) {
                                complete(config, result, false)
                            }
                            result
                        } else {
                            YLog.printException(tag, it, "task-execute")
                            complete(config, SimpleResultEntity.exception(it.message), true)
                        }
                    }
                    resultList.add(result)
                }.apply {
                    if (!this.isCancelled) {
                        taskList[config.unique] = this
                    }
                }
            }.forEach {
                it.join()
            }
            finish()
            // 对结果分组，成功 or 失败
            val resultMap = resultList.groupBy {
                it.isOk()
            }
            // 完成所有任务
            statusHandler.updateStatus(
                TrainExecutorStatus.Finished(
                totalCount = taskCount,
                succeed = resultMap[true] ?: emptyList(),
                failed = resultMap[false] ?: emptyList()
            ))
        }
    }

    private suspend fun complete(
        config: ITaskConfig,
        result: ITaskResult,
        isNotifyDirectorIfFailed: Boolean,
    ): ITaskResult {
        completedTaskList.add(config)
        statusHandler.updateStatus(TrainExecutorStatus.TaskCompleted(config, result, isNotifyDirectorIfFailed))
        WriteToFileNode.deleteCacheFile()
        OfficialArticleExecutor.clearCacheDir()
        return result
    }

    private fun finish() {
        isFinished.set(true)
    }

    suspend fun cancelTask(config: ITaskConfig, reason: String?) {
        if (isFinished.get()) return
        cancelTaskByUnique(config.unique, reason)
    }

    fun getCompletedTaskList(): List<ITaskConfig> {
        return completedTaskList.toList()
    }

    fun getRunningTaskList(): List<ITaskConfig> {
        return configList.filter {
            !completedTaskList.contains(it)
        }
    }

    private fun cancelTaskByUnique(unique: String, reason: String?) {
        if (isFinished.get()) return
        kotlin.runCatching {
            taskList[unique]?.takeUnless {
                it.isCancelled
            }?.cancel(CancellationException(reason))
            cancelTaskUniqueList.add(CancelEntity(unique, reason))
            YLog.i(tag, "cancelTaskByUnique $unique succeed")
        }.onFailure {
            YLog.printException(tag, it, "closeTask: $unique")
        }
    }

    fun cancelAllTask(uniqueList: List<String>?, reason: String?) {
        if (isFinished.get()) {
            YLog.i(tag, "cancelAllTask ignore, isFinished")
            return
        }
        val closeAllTask = isCancelAllTask.get()
        if (closeAllTask != null) {
            YLog.i(tag, "cancelAllTask ignore, done")
            return
        }
        YLog.i(tag, "cancelAllTask $uniqueList")
        if (uniqueList == null) {
            isCancelAllTask.set(CancelEntity("", reason))
        }
        uniqueList?.forEach {
            cancelTaskByUnique(it, reason)
        }
    }
}

private data class CancelEntity(
    val unique: String,
    val reason: String?,
)

fun main() {
    runBlocking {
        val list = listOf(1, 2, 3)
        val map = mutableMapOf<Int, Job>()
        val resultList = mutableListOf<String>()
        launch(Dispatchers.IO) {
            val result = list.map { index ->
                launch(Dispatchers.IO) {
                    kotlin.runCatching {
                        println("start: $index ${Thread.currentThread().id}")
                        if (index == 2) {
                            delay(500)
                        } else {
                            delay(50)
                        }
//                        if (index == 2) {
//                            throw Exception("error")
//                        }
                        println("end: $index ${Thread.currentThread().id}")
                        resultList.add("result: $index succeed")
                    }.onFailure {
                        println("failed: $index ${Thread.currentThread().id}")
                        GlobalScope.launch(Dispatchers.IO) {
                            delay(100)
                            println("failed delay: $index ${Thread.currentThread().id}")
                        }
                        resultList.add("result: $index failed")
                    }.getOrNull()
                }.apply {
                    map.put(index, this)
                }
            }.map {
                it.join()
            }
            println("completed: $resultList ${Thread.currentThread().id}")
        }
        launch(Dispatchers.IO) {
            delay(300)
            println("cancel 2 ${Thread.currentThread().id}")
            map[2]?.cancel()
        }
        delay(800)
    }
    println("end ${Thread.currentThread().id}")
}