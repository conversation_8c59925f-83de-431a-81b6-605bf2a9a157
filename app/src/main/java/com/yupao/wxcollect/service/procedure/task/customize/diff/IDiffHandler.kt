package com.yupao.wxcollect.service.procedure.task.customize.diff

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/21/021</p>
 *
 * <AUTHOR>
 */
interface IDiffHandler {

    suspend fun transform(
        entity: DiffEntity
    ): DiffResultEntity

    suspend fun dispatchFinished()
}

data class DiffEntity(
    val currTime: Long,
    val prevVersion: Int,
    val list: List<Map<String, Any?>>,
)

data class DiffResultEntity(
    val version: Int,
    val list: List<Map<String, Any?>>
)