package com.yupao.wxcollect.service.procedure.task.official

import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog

/**
 * 请求超时帮助类
 *
 * <p>创建时间：2025/2/25/025</p>
 *
 * <AUTHOR>
 */
class RequestTimeoutHelper(
    /**
     * 开始时间
     */
    private val startTime: Long = TimeUtil.getCurrTime()
) {

    companion object {
        /**
         * 任务期望执行总时长
         */
        private const val Duration = 10 * 60 * 1000L

        private const val MIN_TOTAL = 3 * 60 * 1000L
        private const val MAX_TOTAL = 5 * 60 * 1000L

        private const val MIN_TIMEOUT = 20
        private const val MAX_TIMEOUT = 60
    }

    private val availableProcessors by lazy {
        kotlin.runCatching {
            Runtime.getRuntime().availableProcessors()
        }.getOrElse { 8 }.coerceAtLeast(4)
    }

    /**
     * 计算下载超时时间
     * 1. 下载总时长：（10分钟 -（当前时间 - 任务开始时间））/ 5 * 3，最小为3分钟，最大为5分钟。
     * 2. 单个请求超时时间：下载总时长 * 60 / 待处理文章总数  * 8
     *
     * @param count
     * @return
     */
    fun calcDownloadTimeout(count: Int): Int {
        if (count <= 0) return MAX_TIMEOUT
        val totalSecond = ((Duration - (TimeUtil.getCurrTime() - startTime)) * 3 / 5)
            .coerceIn(MIN_TOTAL, MAX_TOTAL) / 1000
        return (totalSecond / count * availableProcessors).toInt()
            .coerceIn(MIN_TIMEOUT, MAX_TIMEOUT).apply {
                YLog.i("DownloadTimeout totalSecond: $totalSecond, availableProcessors: $availableProcessors, count: $count, timeout: $this")
            }
    }

    /**
     * 计算上传超时时间
     * 1. 上传总时长：（（10分钟 -（当前时间 - 任务开始时间））/ 5 * 2，最小为3分钟，最大为5分钟。
     * 2. 单个请求超时时间：上传总时长 * 60 / 待处理文章总数  * 8
     *
     * @param count
     * @return
     */
    fun calcUploadTimeout(count: Int): Int {
        if (count <= 0) return MAX_TIMEOUT
        val totalSecond = ((Duration - (TimeUtil.getCurrTime() - startTime)) * 2 / 5)
            .coerceIn(MIN_TOTAL, MAX_TOTAL) / 1000
        return (totalSecond / count * availableProcessors).toInt()
            .coerceIn(MIN_TIMEOUT, MAX_TIMEOUT).apply {
                YLog.i("UploadTimeout totalSecond: $totalSecond, availableProcessors: $availableProcessors, count: $count, timeout: $this")
            }
    }
}