package com.yupao.wxcollect.service.db

import androidx.room.*
import com.yupao.wxcollect.service.procedure.entity.OfficialArticleReportEntity

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 14:45
 * @Description 公众号上报结果
 */
@Dao
interface OfficialArticleReportDao {

    @Query("SELECT COUNT(*) FROM official_article_report")
    suspend fun totalCount(): Int

    @Query("select * from official_article_report")
    suspend fun queryAll(): List<OfficialArticleReportEntity>?

    @Query("select * from official_article_report limit 1")
    suspend fun first(): List<OfficialArticleReportEntity>?

    @Query("select * from official_article_report where :appType = appType AND :wechatId = wechatId ORDER by reportTime DESC limit :size")
    suspend fun query(appType: String?, wechatId: String?, size: Int?): List<OfficialArticleReportEntity>?

    @Query("select * from official_article_report where :appType = appType AND :wechatId = wechatId AND :url = url ORDER by reportTime DESC")
    suspend fun query(appType: String?, wechatId: String?, url: String?): List<OfficialArticleReportEntity>?

    @Insert
    suspend fun insert(entity: OfficialArticleReportEntity?)

    @Insert
    suspend fun insertAll(list: List<OfficialArticleReportEntity>?)

    /**
     * 根据Id查询
     */
    @Query("select * from official_article_report where id = :id LIMIT 1")
    suspend fun queryById(id: Int?): OfficialArticleReportEntity

    /**
     * 更新
     */
    @Update
    suspend fun update(config: OfficialArticleReportEntity?)

    /**
     * 删除
     */
    @Delete
    suspend fun delete(entity: OfficialArticleReportEntity?)

    /**
     * 删除
     */
    @Delete
    suspend fun delete(entity: List<OfficialArticleReportEntity>?)
}