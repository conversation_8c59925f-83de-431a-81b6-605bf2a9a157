package com.yupao.wxcollect.service.procedure.task.customize.executor.sql

import com.yupao.wxcollect.constant.AppPath
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.util.KVUtil
import com.yupao.wxcollect.util.LogUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import java.io.File
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.ceil

/**
 * 写入文件
 *
 * <p>创建时间：2024/6/13/013</p>
 *
 * <AUTHOR>
 */
class WriteToFileNode(
    private val tag: String?,
    private val limitSize: Int? = null,
) {
    companion object {
        private const val PATH_SOURCE = "source"

        private const val PATH_COMPRESS = "compress"


        private val uploadCount = AtomicInteger(0)

        fun getBaseFile(appType: String?, tag: String): File {
            return File(AppPath.baseFileDir, "${appType}_${tag}")
        }

        suspend fun deleteCacheFile() {
            val sourceDir = KVUtil.getUploadSourceDir().ifBlank { PATH_SOURCE }
            val compressDir = KVUtil.getUploadCompressDir().ifBlank { PATH_COMPRESS }
            LogUtil.deleteFileSync(getBaseFile(ReportConfig.APP_MAIN, sourceDir))
            LogUtil.deleteFileSync(getBaseFile(ReportConfig.APP_SUB, sourceDir))
            LogUtil.deleteFileSync(getBaseFile(ReportConfig.APP_MAIN, compressDir))
            LogUtil.deleteFileSync(getBaseFile(ReportConfig.APP_SUB, compressDir))
        }
    }

    private suspend fun getFile(appType: String?, isSource: Boolean): File {
        val newDir = if (isSource) {
            KVUtil.getUploadSourceDir()
        } else {
            KVUtil.getUploadCompressDir()
        }
        return if (newDir.isBlank()) {
            getBaseFile(appType, if (isSource) { PATH_SOURCE } else { PATH_COMPRESS })
        } else {
            File(newDir)
        }
    }

    suspend fun performSaveToFile(appType: String?, list: List<Map<String, Any?>>?): List<String> {
        val sourceDir = getFile(appType, true)
        val compressDir = getFile(appType, false)
        YLog.i("$tag performSaveToFile $appType: $sourceDir, $compressDir")
        kotlin.runCatching {
            if (list.isNullOrEmpty()) {
                return emptyList()
            }
            val len = list.size
            val step = limitSize ?: Int.MAX_VALUE
            val result = mutableListOf<String>()
            val totalCount = ceil(len.toDouble() / step).toInt()
            // 根据limitSize限制，分批上传
            for ((count, i) in (list.indices step step).withIndex()) {
                val uploadList = list.subList(i, (i + step).coerceAtMost(len))
                if (uploadList.isEmpty()) {
                    continue
                }
                val fileName = "${TimeUtil.fileNameFormat()}-${uploadCount.incrementAndGet()}-part${totalCount}-${count + 1}"
                // 源文件地址
                val filePath = File(sourceDir, "$fileName.csv").absolutePath
                val csvFile = WriteCsvNode(tag, uploadList, filePath).perform()
                // 压缩文件
                val compressName = File(compressDir, "$fileName.7z").absolutePath
                val compressFilePath = CompressNode(csvFile.absolutePath, compressName).perform()
                // 删除源文件
                kotlin.runCatching {
                    csvFile.delete()
                }
                result.add(compressFilePath)
            }
            return result
        }.onFailure {
            FileCorruptionHelper.checkFileCorruption(sourceDir, "${appType}_${PATH_SOURCE}")?.let {
                KVUtil.setUploadSourceDir(it)
            }
            FileCorruptionHelper.checkFileCorruption(compressDir, "${appType}_${PATH_COMPRESS}")?.let {
                KVUtil.setUploadCompressDir(it)
            }
        }.getOrThrow()
    }
}