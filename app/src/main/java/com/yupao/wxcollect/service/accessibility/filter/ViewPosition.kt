package com.yupao.wxcollect.service.accessibility.filter

import android.graphics.Rect
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import androidx.annotation.Keep
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/5/4/004 17:49
 * @Description 获取View的位置信息
 */
class ViewPosition(
    private val viewText: String,
    private val index: Int = 0,
    private val brotherFilter: AccessibilityNodeInfoFilter? = null,
) {

    var positionEntity: PositionEntity? = null
        private set

    fun transform(source: AccessibilityNodeInfo?) {
        val list = source?.findAccessibilityNodeInfosByText(viewText) ?: return
        val info = AccessibilityNodeInfoUtil.findAccessibilityNodeByIndex(index, list)
        val bound = Rect()
        val entity = AccessibilityNodeInfoUtil.findAccessibilityNodeByBrotherIndex(info, brotherFilter)
            ?.getBoundsInScreen(bound)?.let {
                PositionEntity(
                    x = (bound.left + bound.right) / 2,
                    y = (bound.top + bound.bottom) / 2,
                    bound = bound
                )
            }
        if (entity != null) {
            positionEntity = entity
            YLog.i("ViewPosition", "transform: positionEntity = $entity")
        } else {
            YLog.i("ViewPosition", "transform: not found node info")
            AccessibilityNodeInfoUtil.printNodeInfo(source)
        }
    }

    fun performClick(): Boolean {
        YLog.i("ViewPosition", "performClick: positionEntity = $positionEntity")
        val entity = positionEntity ?: return false
        val cmd = "input tap ${entity.x} ${entity.y}"
        val result = CmdUtil.execCmd(cmd)
        return result == null
    }

    fun isShown(): Boolean {
        val entity = positionEntity ?: return false
        YLog.i("ViewPosition", "isShown:  = $entity")
        if (entity.bound.top < 0) {
            return false
        }
        if (entity.bound.bottom < 0) {
            return false
        }
        return true
    }
}

@Keep
data class PositionEntity(
    val x: Int,
    val y: Int,
    val bound: Rect,
)