package com.yupao.wxcollect.service.procedure.task

import androidx.annotation.IntDef
import com.yupao.wxcollect.service.db.IDatabaseHelper
import com.yupao.wxcollect.service.procedure.entity.TaskConfig
import com.yupao.wxcollect.service.procedure.entity.TaskResultEntity
import com.yupao.wxcollect.ui.config.model.ReportTypeUpgradeStrategy

/**
 * <AUTHOR>
 * @Date 2023/4/6/006 9:40
 * @Description
 */
interface ITask {

    companion object {
        //  额外查询时间
        const val EXTRA_QUERY_MIN = 60
    }

    @TaskType
    fun taskType(): Int

    suspend fun perform(config: TaskConfig?, databaseHelper: IDatabaseHelper?): TaskResultEntity

}

@IntDef(
    TaskType.BASE,
    TaskType.GROUP_INFO_SYNC,
    TaskType.FRIEND_LIST_SYNC,
    TaskType.GROUP_OPERATION_SYNC,
    TaskType.GROUP_REMOVE_SYNC,
    TaskType.MESSAGE_REPORT,
    TaskType.APPLY_TO_ADD_FRIENDS,
    TaskType.ADD_FRIEND_INFO,
    TaskType.UPDATE_FRIEND_REMARK,
    TaskType.UPDATE_FRIEND_LAST_TIME,
    TaskType.DELETE_FRIEND,
    TaskType.OFFICIAL_ACCOUNT,
    TaskType.CUSTOMIZE,
)
@Retention(AnnotationRetention.SOURCE)
annotation class TaskType {
    companion object {
        internal const val BASE = 1
        // 同步群信息
        const val GROUP_INFO_SYNC = BASE
        // 好友列表
        const val FRIEND_LIST_SYNC = BASE.shl(1)
        // 群操作
        const val GROUP_OPERATION_SYNC = BASE.shl(2)
        // 被踢信息
        const val GROUP_REMOVE_SYNC = BASE.shl(3)
        // 聊天
        const val MESSAGE_REPORT = BASE.shl(4)
        // 好友申请
        const val APPLY_TO_ADD_FRIENDS = BASE.shl(5)
        // 获取微信帐号信息
        const val FETCH_WECHAT_ACCOUNT = BASE.shl(6)
        // 添加好友
        const val ADD_FRIEND_INFO = BASE.shl(7)
        // 更新好友备注
        const val UPDATE_FRIEND_REMARK = BASE.shl(8)
        // 更新好友最新聊天时间
        const val UPDATE_FRIEND_LAST_TIME = BASE.shl(9)
        // 上报(被)删除好友
        const val DELETE_FRIEND = BASE.shl(10)
        // 公众号
        const val OFFICIAL_ACCOUNT = BASE.shl(11)
        // 自定义任务
        const val CUSTOMIZE = BASE.shl(12)

        fun getMessage(@TaskType taskType: Int?): String? {
            return when (taskType) {
                GROUP_INFO_SYNC -> "同步群信息"
                FRIEND_LIST_SYNC -> "好友列表"
                GROUP_OPERATION_SYNC -> "群操作"
                GROUP_REMOVE_SYNC -> "被踢信息"
                MESSAGE_REPORT -> "聊天"
                APPLY_TO_ADD_FRIENDS -> "好友申请"
                ADD_FRIEND_INFO -> "添加好友"
                UPDATE_FRIEND_REMARK -> "好友备注"
                UPDATE_FRIEND_LAST_TIME -> "好友最后聊天时间"
                DELETE_FRIEND -> "删除好友"
                OFFICIAL_ACCOUNT -> "公众号"
                CUSTOMIZE -> "自定义"
                else -> null
            }
        }
    }
}

@Retention(AnnotationRetention.SOURCE)
annotation class ReportType {
    companion object {
        /**
         * 上报消息, 修改需要同步修改[ReportTypeUpgradeStrategy]
         */
        const val REPORT_MESSAGE = TaskType.MESSAGE_REPORT or
                TaskType.APPLY_TO_ADD_FRIENDS or
                TaskType.ADD_FRIEND_INFO or
                TaskType.UPDATE_FRIEND_LAST_TIME or
                TaskType.DELETE_FRIEND

        /**
         * 上报群组, 修改需要同步修改[ReportTypeUpgradeStrategy]
         */
        const val REPORT_GROUP = TaskType.GROUP_INFO_SYNC or
                TaskType.FRIEND_LIST_SYNC or
                TaskType.GROUP_OPERATION_SYNC or
                TaskType.GROUP_REMOVE_SYNC

        /**
         * 公众号
         */
        const val REPORT_OFFICIAL_ACCOUNT = TaskType.OFFICIAL_ACCOUNT

        /**
         * 附属任务，不需要额外配置，只有配置有任务，即可同步上报
         */
        const val REPORT_ATTACH = TaskType.UPDATE_FRIEND_REMARK

        /**
         * 自定义任务，只有配置有任务才会生效
         */
        const val REPORT_CUSTOMIZE = TaskType.CUSTOMIZE

        /**
         * 获取所有ReportType
         */
        fun getAllReportType() = mapOf(
            REPORT_MESSAGE to "信息",
            REPORT_GROUP to "微信群",
            REPORT_ATTACH to "附属",
            REPORT_CUSTOMIZE to "自定义",
            REPORT_OFFICIAL_ACCOUNT to "公众号",
        )

        /**
         * 所有ReportType合并
         */
        private fun getReportTypeTotal(): Int {
            return getAllReportType().keys
                .takeIf { it.isNotEmpty() }
                ?.reduce { acc, i ->
                    acc or i
                } ?: 0
        }

        /**
         * 将type转为[消息|删除好友]
         */
        fun getMessage(type: Int, isMathAll: Boolean = true): List<String> {
            val map = getAllReportType()
            val result = mutableListOf<String>()
            var calcType = 0
            findAllReportTypeList(type, isMathAll).mapNotNull {
                calcType = calcType or it
                map[it]
            }.run {
                result.addAll(this)
            }
            val remain = type and calcType.inv()
            if (remain > 0) {
                getTaskTypeList(remain).mapNotNull {
                    TaskType.getMessage(it)
                }.run {
                    result.addAll(this)
                }
            }
            return result
        }

        fun getFormatMessage(type: Int, isMathAll: Boolean = true): String  {
            val list = getMessage(type, isMathAll)
            return "[${list.joinToString("|")}]"
        }


        /**
         * 根据typeType获取对应的ReportType
         */
        fun getReportType(@TaskType taskType: Int?): Int? {
            taskType ?: return null
            val list = getAllReportType().keys
            return list.find {
                container(taskType, it)
            }
        }

        /**
         * 判断reportType是否包含taskType
         */
        fun container(@TaskType taskType: Int, reportType: Int) = kotlin.run {
            (reportType and taskType) == taskType
        }

        /**
         * 判断allTaskTypes是否包含reportType所有或者其中一项
         * @param isMathAll ture: 包含reportType所有， false: 包含reportType其中一项
         */
        fun containerReportType(allTaskTypes: Int, reportType: Int, isMathAll: Boolean = true) = kotlin.run {
            if (isMathAll) {
                (allTaskTypes and reportType) == reportType
            } else {
                (allTaskTypes and reportType) != 0
            }
        }

        /**
         * 根据reportType获取所有的任务
         */
        fun getTaskTypeList(reportType: Int): List<Int> {
            val list = mutableListOf<Int>()
            if (reportType <= 0) {
                return list
            }
            var curr = TaskType.BASE
            while (curr <= reportType) {
                if (container(curr, reportType)) {
                    list.add(curr)
                }
                curr = curr.shl(1)
            }
            return list
        }

        /**
         * 根据taskTypes查找所有ReportType
         */
        fun findAllReportTypeList(taskTypes: Int, isMathAll: Boolean = true): List<Int> {
            val allReportType = getAllReportType().keys
            return if (isMathAll) {
                allReportType.filter { it and taskTypes == it }
            } else {
                allReportType.filter { it and taskTypes != 0 }
            }
        }
    }
}