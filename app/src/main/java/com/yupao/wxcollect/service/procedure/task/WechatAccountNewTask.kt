package com.yupao.wxcollect.service.procedure.task

import com.yupao.wxcollect.service.WechatUserManager
import com.yupao.wxcollect.service.db.IDatabaseHelper
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity
import com.yupao.wxcollect.service.procedure.entity.query.UserIdQueryEntity
import com.yupao.wxcollect.service.procedure.entity.query.UsernameQueryEntity
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/21/021 16:18
 * @Description 获取微信账号
 */
class WechatAccountNewTask(val databasePath: String) {
    companion object {
        private const val TAG = "WechatAccountTask"
    }

    private val userEntity by lazy {
        WechatUserManager.getUser(databasePath)?.let {
            UserEntity(
                alias = it.alias,
                nickname = it.nickname,
                username = it.username
            )
        }
    }

    fun taskType(): Int {
        return TaskType.FETCH_WECHAT_ACCOUNT
    }

    private suspend fun queryAlias(databaseHelper: IDatabaseHelper): String? {
        return databaseHelper.query(
            "select value from userinfo where id=42 and type=3",
            UsernameQueryEntity::class,
            TAG,
        )?.firstOrNull()?.value
    }

    /**
     * 提交查询，先查询本地数据库
     */
    suspend fun performAliasUser(databaseHelper: IDatabaseHelper?): UserEntity? {
        val databaseHelp = databaseHelper ?: return null
        val queryAlias = queryAlias(databaseHelper)
        val userIdEntity = if (queryAlias != null) {
            queryByAlias(queryAlias, databaseHelp)
        } else {
            null
        }
        if (userIdEntity == null) {
            return null
        }
        val currUserEntity = userEntity
        val entity = UserEntity(
            username = userIdEntity.username ?: currUserEntity?.username,
            nickname = userIdEntity.nickname ?: currUserEntity?.nickname,
            alias = (userIdEntity.alias ?: "").ifEmpty { userIdEntity.nickname?:"" }.ifEmpty { currUserEntity?.alias } ,
        )
        if (currUserEntity == null ||
            currUserEntity.username != entity.username ||
            currUserEntity.nickname != entity.nickname ||
            currUserEntity.alias != entity.alias
        ) {
            WechatUserManager.updateUser(databasePath, entity)
        }
        WechatUserManager.saveUserEntityForOtherProcess(databasePath, entity)
        YLog.i(TAG, "query by db user: $entity")
        return entity
    }

    /**
     * 任务查询
     */
    suspend fun performTaskUser(databaseHelper: IDatabaseHelper?): UserEntity? {
        val databaseHelp = databaseHelper ?: return null
        val queryAlias = queryAlias(databaseHelper)
        val userIdEntity = if (queryAlias != null) {
            queryByAlias(queryAlias, databaseHelp)
        } else {
            null
        }
        val currUserEntity = userEntity
        val entity = if (userIdEntity == null) {
            return currUserEntity
        }else{
          UserEntity(
                username = userIdEntity.username ?: currUserEntity?.username,
                nickname = userIdEntity.nickname ?: currUserEntity?.nickname,
                alias = (userIdEntity.alias ?: "").ifEmpty { userIdEntity.nickname?:"" }.ifEmpty { currUserEntity?.alias } ,
            )
        }
        if (currUserEntity == null ||
            currUserEntity.username != entity.username ||
            currUserEntity.nickname != entity.nickname ||
            currUserEntity.alias != entity.alias
        ) {
            WechatUserManager.updateUser(databasePath, entity)
        }
        WechatUserManager.saveUserEntityForOtherProcess(databasePath, entity)
        YLog.i(TAG, "query by task user: $entity")
        return entity
    }

    /**
     * 接口查询
     */
    suspend fun perform(
        databaseHelper: IDatabaseHelper?,
        alias: String
    ): UserEntity? {
        val databaseHelp = databaseHelper ?: return null
        val userIdEntity = queryByAlias(alias, databaseHelp)

        if (userIdEntity == null) {
            return null
        }
        val entity = UserEntity(
            username = userIdEntity.username,
            nickname = userIdEntity.nickname,
            alias = (userIdEntity.alias ?: "").ifEmpty { alias }.ifEmpty { userIdEntity.nickname ?: "" },
        )
        val currUserEntity = userEntity
        if (currUserEntity == null ||
            currUserEntity.username != entity.username ||
            currUserEntity.nickname != entity.nickname ||
            currUserEntity.alias != entity.alias
        ) {
            WechatUserManager.updateUser(databasePath, entity)
        }
        WechatUserManager.saveUserEntityForOtherProcess(databasePath, entity)
        YLog.i(TAG, "query by api user: $entity")
        return entity
    }

    private suspend fun queryByAlias(
        alias: String,
        databaseHelp: IDatabaseHelper
    ): UserIdQueryEntity? {
        YLog.i(TAG, "query by alias: $alias")
        var result = databaseHelp.query(
            "select * from rcontact where alias='$alias' and verifyFlag = 0 and type != 4",
            UserIdQueryEntity::class,
            TAG,
        )?.firstOrNull()
        if (result == null && alias.startsWith("wxid_")) {
            YLog.i(TAG, "query by username: $alias")
            result = databaseHelp.query(
                "select * from rcontact where username='$alias' and verifyFlag = 0 and type != 4",
                UserIdQueryEntity::class,
                TAG,
            )?.firstOrNull()
        }
        return result
    }
}