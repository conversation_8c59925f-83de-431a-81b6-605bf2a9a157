# SystemStatusManager 使用指南

## 概述

`SystemStatusManager` 是一个系统状态管理器，用于监控和提示各种系统状态变化，包括网络状态、内存状态、存储状态、电池状态等。通过悬浮窗的形式向用户展示重要的状态信息。

## 主要功能

### 1. 状态监控
- **网络状态**：监控网络连接状态，断网时提示"网络不佳，请检查网络连接"
- **内存状态**：监控内存使用情况，内存不足时提示"内存不足，建议清理后台应用"
- **存储状态**：监控存储空间，空间不足时提示"存储空间不足，建议清理缓存文件"
- **电池状态**：监控电池电量，电量不足时提示"电量不足(XX%)，建议及时充电"

### 2. 悬浮窗提示
- 使用 `KeepAliveFloatWindow` 进行悬浮窗提示
- 每次只显示一条消息
- 点击悬浮窗显示下一条待处理消息
- 支持消息优先级排序

### 3. 智能控制
- 通过 `KVHelper.isClashTipsDb()` 控制是否显示提示
- 自动去重相同类型的消息
- 支持强制显示重要消息

## 使用方法

### 1. 初始化

```kotlin
// 在 Application 或 Service 中初始化
SystemStatusManager.initialize()
```

### 2. 基本使用

```kotlin
// 添加自定义消息
SystemStatusManager.addCustomMessage("服务启动异常，请重试")

// 添加权限相关消息
SystemStatusManager.addPermissionMessage("缺少存储权限，请授权后重试")

// 添加服务相关消息
SystemStatusManager.addServiceMessage("后台服务已停止")

// 强制显示消息（忽略开关设置）
SystemStatusManager.forceShowMessage(
    SystemStatusManager.StatusType.NETWORK,
    "网络连接失败，请检查网络设置",
    priority = 10
)
```

### 3. 状态查询

```kotlin
// 获取待处理消息数量
val count = SystemStatusManager.getPendingMessageCount()

// 获取所有待处理消息
val messages = SystemStatusManager.getAllPendingMessages()

// 手动触发显示下一条消息
SystemStatusManager.showNextMessage()
```

### 4. 清理操作

```kotlin
// 移除指定类型的消息
SystemStatusManager.removeStatusMessage(SystemStatusManager.StatusType.NETWORK)

// 清空所有消息
SystemStatusManager.clearAllStatusMessages()

// 销毁管理器
SystemStatusManager.destroy()
```

## 状态类型

```kotlin
enum class StatusType(val displayName: String) {
    NETWORK("网络状态"),      // 优先级: 10
    MEMORY("内存状态"),       // 优先级: 8
    STORAGE("存储状态"),      // 优先级: 7
    BATTERY("电池状态"),      // 优先级: 6
    PERMISSION("权限状态"),   // 优先级: 9
    SERVICE("服务状态")       // 优先级: 8
}
```

## 消息优先级

- **10**: 网络状态（最高优先级）
- **9**: 权限状态
- **8**: 内存状态、服务状态
- **7**: 存储状态
- **6**: 电池状态
- **5**: 自定义消息（默认）

## 配置说明

### 1. 提示开关控制

通过 `KVHelper.isClashTipsDb()` 控制是否显示悬浮窗提示：

```kotlin
// 在数据库中设置
KVHelper.set("is_clash_tips_db", "true")  // 开启提示
KVHelper.set("is_clash_tips_db", "false") // 关闭提示
```

### 2. 检查间隔配置

```kotlin
// 状态检查间隔（默认5秒）
private const val STATUS_CHECK_INTERVAL = 5000L

// 悬浮窗显示时长（默认3秒）
private const val FLOAT_SHOW_DURATION = 3000L
```

### 3. 阈值配置

```kotlin
// 内存使用超过85%认为内存不足
val isMemoryLow = memoryUsagePercent > 85

// 存储使用超过90%认为存储不足
val isStorageLow = storageUsagePercent > 90

// 电量低于20%且未充电认为电量不足
val isBatteryLow = batteryLevel < 20 && !isCharging
```

## 完整使用示例

```kotlin
class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化状态管理器
        SystemStatusManager.initialize()
        
        // 模拟添加各种状态消息
        testStatusMessages()
    }
    
    private fun testStatusMessages() {
        // 网络问题
        SystemStatusManager.addStatusMessage(
            SystemStatusManager.StatusType.NETWORK,
            "网络不佳，请检查网络连接",
            priority = 10
        )
        
        // 权限问题
        SystemStatusManager.addPermissionMessage("缺少悬浮窗权限，请前往设置开启")
        
        // 服务问题
        SystemStatusManager.addServiceMessage("数据同步服务异常")
        
        // 自定义消息
        SystemStatusManager.addCustomMessage("发现新版本，建议更新")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        SystemStatusManager.destroy()
    }
}
```

## 注意事项

1. **权限要求**：需要悬浮窗权限才能正常显示提示
2. **性能考虑**：状态检查在后台线程执行，不会阻塞主线程
3. **内存管理**：及时调用 `destroy()` 方法清理资源
4. **消息去重**：相同类型的消息会自动去重，只保留最新的
5. **开关控制**：用户可以通过设置关闭悬浮窗提示功能

## 扩展功能

可以根据需要添加更多状态检查：

```kotlin
// 添加新的状态类型
enum class StatusType {
    // ... 现有类型
    TEMPERATURE("温度状态"),
    DISK_IO("磁盘IO状态"),
    CPU_USAGE("CPU使用率")
}

// 实现对应的检查方法
private suspend fun checkTemperatureStatus() {
    // 温度检查逻辑
}
```
