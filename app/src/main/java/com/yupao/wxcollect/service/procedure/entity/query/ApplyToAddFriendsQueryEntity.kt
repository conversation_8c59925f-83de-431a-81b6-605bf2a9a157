package com.yupao.wxcollect.service.procedure.entity.query

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @Date 2023/4/20/020 16:50
 * @Description
 */
@Keep
data class ApplyToAddFriendsQueryEntity(
    @SerializedName("wechat_id")
    val talker: String? = null,
    @SerializedName("wechat_name")
    val displayName: String? = null,
    @SerializedName("last_modified_time")
    val lastModifiedTime: String? = null,
    @SerializedName("type")
    val isSend: String? = null,
    @SerializedName("status")
    val state: String? = null,
    @SerializedName("add_scene")
    val addScene: String? = null,
    @SerializedName("create_time")
    val createTime: String? = null,
)