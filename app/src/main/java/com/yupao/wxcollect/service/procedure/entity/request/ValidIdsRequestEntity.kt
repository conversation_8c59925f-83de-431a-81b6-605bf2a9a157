package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/10/20/020</p>
 *
 * <AUTHOR>
 */
@Keep
data class ValidIdsRequestEntity(
    val wechatOriginId: String?,
    val phoneNum: String?,
    val articles: List<ArticlesEntity>?,
)

@Keep
data class ArticlesEntity(
    val articleUnique: String?,
    val officialAccountOriginId: String?,
)