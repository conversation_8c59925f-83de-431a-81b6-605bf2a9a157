package com.yupao.wxcollect.service.procedure.task.customize

import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.concurrent.CancelAllStrategy
import com.yupao.wxcollect.service.procedure.DurationHelper
import com.yupao.wxcollect.service.procedure.entity.ITaskResult
import com.yupao.wxcollect.service.procedure.entity.SimpleResultEntity
import com.yupao.wxcollect.service.procedure.entity.TaskRuntimeEntity
import com.yupao.wxcollect.service.procedure.entity.director.CollectTaskEntity
import com.yupao.wxcollect.service.procedure.entity.director.ICollectTaskStatus
import com.yupao.wxcollect.service.procedure.entity.director.ReportData
import com.yupao.wxcollect.service.procedure.entity.director.ReportEntity
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatus
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.service.procedure.task.ConfigException
import com.yupao.wxcollect.service.procedure.task.DatabaseCorruptException
import com.yupao.wxcollect.service.procedure.task.ITaskExecutor
import com.yupao.wxcollect.service.procedure.task.NetworkHelper
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffHelper
import com.yupao.wxcollect.service.procedure.task.customize.diff.DiffTaskDispatcher
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.CustomizeQueryNode
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.ReportNode
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.UploadEntity
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.UploadOssNode
import com.yupao.wxcollect.service.procedure.task.customize.executor.sql.WriteToFileNode
import com.yupao.wxcollect.util.MemoryUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicInteger

/**
 * 采集任务执行器
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
class CollectTaskExecutor(private val taskEntity: CollectTaskEntity): ITaskExecutor {

    private val resp by lazy { ReportRepository(null) }

    private val diffDispatcher by lazy {
        DiffTaskDispatcher(taskEntity.entity.condition.appType, taskEntity.entity.taskCode)
    }

    private val queryNode by lazy {
        val queryEntity = taskEntity.entity.query
        CustomizeQueryNode(queryEntity.keyList, queryEntity.sql, queryEntity.limitLine, taskEntity.entity.getTag())
    }

    private suspend fun updateStatus(status: ICollectTaskStatus) {
        taskEntity.notifyStatusChanged(status)
    }

    override suspend fun perform(runtimeEntity: TaskRuntimeEntity): ITaskResult {
        return kotlin.runCatching {
            val entity = taskEntity.entity
            val databaseHelper = runtimeEntity.databaseHelper
            val tagFlag = entity.getTag()
            val tag = "CollectTask[$tagFlag]"
            val unique = entity.unique
            updateStatus(TaskStatus.Query(unique))
            if (!NetworkHelper().checkNetwork()) {
                val errorMsg = "网络异常"
                updateStatus(TaskStatus.Failed(unique, TaskStatus.Failed.Unknown, errorMsg))
                return@runCatching SimpleResultEntity.failed(errorMsg)
            }
            val uploadFileList = CopyOnWriteArrayList<String>()
            val queryTotalCount = AtomicInteger(0)
            val appType = entity.condition.appType
            val currTime = TimeUtil.getCurrTime()
            // 上一次上报的版本
            val prevReportVersion = DiffHelper.getReportVersion(appType, entity.taskCode)
            // 本次上报的版本
            var currReportVersion: Int = prevReportVersion
            // sql查询
            queryNode.perform(databaseHelper) { queryList ->
                val currTime2 = TimeUtil.getCurrTime()
                val result = diffDispatcher.dispatch(
                    taskCode = entity.taskCode,
                    prevVersion = prevReportVersion,
                    appType = appType,
                    list = queryList
                )
                val diffDuration = TimeUtil.getCurrTime() - currTime2
                YLog.i(tag, "diff duration: ${diffDuration}ms")
                val reportList = result.list
                // 此处使用单线程的目的，是为了避免一次查询数据量太大，导致OOM
                val querySize = reportList.size
                queryTotalCount.getAndAdd(querySize)
                YLog.i(tag, "query result size: $querySize")
                // 写入文件
                val fileListRes = WriteToFileNode(tagFlag, entity.query.limitLine)
                    .performSaveToFile(entity.condition.appType, reportList)
                if (fileListRes.isNotEmpty()) {
                    uploadFileList.addAll(fileListRes)
                }
                currReportVersion = result.version
            }
            this.diffDispatcher.dispatchFinished()
            YLog.i(tag, "write file succeed, size: ${uploadFileList.size}, " +
                    "duration: ${(TimeUtil.getCurrTime() - currTime) / 1000}s")
            updateStatus(TaskStatus.Upload(unique))
            // oss上传
            val ossUrlList = performUpload(uploadFileList, tagFlag)
            val totalCount = queryTotalCount.get()
            YLog.i(tag, "upload oss result size: ${ossUrlList.size}, query size: $totalCount")
            // 上报
            val data = ReportData(
                count = totalCount,
                data = ReportEntity(ossUrlList.map { it.toOssFileEntity() }),
                version = currReportVersion,
            )
            val result = ReportNode(tag).performReport(taskEntity, data)
            updateStatus(result.status)
            return result.result
        }.getOrElse {
            reportException(it, "perform")
        }
    }

    private suspend fun reportException(exp: Throwable, ext: String?): ITaskResult {
        val errorMsg = exp.message
        return if (exp is ConfigException) {
            YLog.i(taskEntity.entity.getTag(), errorMsg)
            updateStatus(TaskStatus.Failed(taskEntity.unique, TaskStatus.Failed.Config, errorMsg))
            SimpleResultEntity.config(errorMsg)
        } else if(exp is DatabaseCorruptException) {
            YLog.printException(taskEntity.entity.getTag(), exp, ext)
            updateStatus(TaskStatus.Failed(taskEntity.unique, TaskStatus.Failed.DatabaseCorrupt, errorMsg))
            SimpleResultEntity.config(errorMsg)
        } else {
            YLog.printException(taskEntity.entity.getTag(), exp, ext)
            updateStatus(TaskStatus.Failed(taskEntity.unique, TaskStatus.Failed.Exception, errorMsg))
            if (exp is OutOfMemoryError) {
                // 尝试dump
                MemoryUtil.checkMemory(isDumpSync = false, isDumpForce = true)
            }
            SimpleResultEntity.exception(errorMsg)
        }
    }

    @Throws
    private suspend fun performUpload(list: List<String>, tagFlag: String): List<UploadEntity> {
        // 应后端要求，强制最大延迟1s
        return DurationHelper(AppConstant.IntervalTime.uploadTime).perform {
            val taskList = list.map {  filePath ->
                suspend {
                    UploadOssNode(resp, tagFlag).perform(listOf(filePath), null)
                }
            }
            CoroutinePool.ossUpload.submitAll(taskList, CancelAllStrategy()).flatMap {
                it ?: emptyList()
            }
        }
    }
}