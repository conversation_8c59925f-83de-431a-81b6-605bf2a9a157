package com.yupao.wxcollect.service.procedure.entity.db

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 主号联系人本地数据
 *
 * <p>创建时间：2024/6/21/021</p>
 *
 * <AUTHOR>
 */
@Keep
@Entity(tableName = "main_contact")
data class MainContactLocalModel(
    override val updateTime: Long,
    override val version: Int,
    override val uniqueCode: Int,
    override val ext: String?,
    override val username: String?,
    override val nickname: String?,
    override val alias: String?,
    override val conRemark: String?,
    @PrimaryKey(autoGenerate = true)
    override var id: Int? = null,
): ContactLocalModel()