package com.yupao.wxcollect.service

import com.yupao.ylog.YLog

/**
 * SystemStatusManager 测试类
 * 用于测试悬浮窗点击下一步功能
 */
object SystemStatusManagerTest {
    
    private const val TAG = "SystemStatusManagerTest"
    
    /**
     * 测试多条消息的显示和点击切换
     */
    fun testMultipleMessages() {
        YLog.i(TAG, "开始测试多条消息显示")
        
        // 初始化管理器
        SystemStatusManager.initialize()
        
        // 添加多条不同优先级的消息
        SystemStatusManager.addStatusMessage(
            SystemStatusManager.StatusType.NETWORK,
            "网络不佳，请检查网络连接",
            priority = 10
        )
        
        // 模拟添加更多消息类型（需要先在 StatusType 中添加）
        // SystemStatusManager.addStatusMessage(
        //     SystemStatusManager.StatusType.MEMORY,
        //     "内存不足，建议清理后台应用",
        //     priority = 8
        // )
        
        // SystemStatusManager.addStatusMessage(
        //     SystemStatusManager.StatusType.STORAGE,
        //     "存储空间不足，建议清理缓存文件",
        //     priority = 7
        // )
        
        // SystemStatusManager.addStatusMessage(
        //     SystemStatusManager.StatusType.BATTERY,
        //     "电量不足(15%)，建议及时充电",
        //     priority = 6
        // )
        
        YLog.i(TAG, "已添加 ${SystemStatusManager.getPendingMessageCount()} 条消息")
        YLog.i(TAG, "点击悬浮窗的'下一步'按钮来查看下一条消息")
    }
    
    /**
     * 测试强制显示消息
     */
    fun testForceShowMessage() {
        YLog.i(TAG, "测试强制显示消息")
        
        SystemStatusManager.forceShowMessage(
            SystemStatusManager.StatusType.NETWORK,
            "这是一条强制显示的重要消息",
            priority = 10
        )
    }
    
    /**
     * 测试消息队列管理
     */
    fun testMessageQueueManagement() {
        YLog.i(TAG, "测试消息队列管理")
        
        // 添加消息
        SystemStatusManager.addStatusMessage(
            SystemStatusManager.StatusType.NETWORK,
            "第一条网络消息",
            priority = 5
        )
        
        SystemStatusManager.addStatusMessage(
            SystemStatusManager.StatusType.NETWORK,
            "第二条网络消息（应该替换第一条）",
            priority = 8
        )
        
        YLog.i(TAG, "当前消息数量: ${SystemStatusManager.getPendingMessageCount()}")
        
        // 获取所有消息
        val messages = SystemStatusManager.getAllPendingMessages()
        messages.forEach { message ->
            YLog.i(TAG, "消息: ${message.type.displayName} - ${message.message} (优先级: ${message.priority})")
        }
    }
    
    /**
     * 测试清理功能
     */
    fun testCleanup() {
        YLog.i(TAG, "测试清理功能")
        
        // 清空所有消息
        SystemStatusManager.clearAllStatusMessages()
        YLog.i(TAG, "清空后消息数量: ${SystemStatusManager.getPendingMessageCount()}")
        
        // 销毁管理器
        SystemStatusManager.destroy()
        YLog.i(TAG, "管理器已销毁")
    }
    
    /**
     * 完整测试流程
     */
    fun runFullTest() {
        YLog.i(TAG, "=== 开始完整测试 ===")
        
        // 1. 测试多条消息
        testMultipleMessages()
        
        // 等待用户交互
        YLog.i(TAG, "请点击悬浮窗测试切换功能...")
        
        // 2. 延迟后测试强制显示
        Thread.sleep(5000)
        testForceShowMessage()
        
        // 3. 测试队列管理
        Thread.sleep(3000)
        testMessageQueueManagement()
        
        // 4. 最后清理
        Thread.sleep(5000)
        testCleanup()
        
        YLog.i(TAG, "=== 测试完成 ===")
    }
}
