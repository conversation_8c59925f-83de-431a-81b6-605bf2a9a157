package com.yupao.wxcollect.service.monitor

import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.lifecycle.lifecycleScope
import com.yupao.ylog.YLog
import kotlinx.coroutines.launch

/**
 * 内存监控使用示例
 * 展示如何在不同场景下使用内存监控功能
 */
class MemoryMonitorUsageExample {
    
    companion object {
        private const val TAG = "MemoryMonitorExample"
    }
    
    /**
     * 示例1：在 Application 中使用
     */
    class ExampleApplication : android.app.Application() {
        
        override fun onCreate() {
            super.onCreate()
            
            // 初始化内存监控管理器
            MemoryMonitorManager.initialize()
            
            // 启动内存监控
            MemoryMonitorManager.startMonitoring()
            
            YLog.i(TAG, "应用启动，内存监控已开始")
        }
        
        override fun onTerminate() {
            super.onTerminate()
            
            // 销毁内存监控
            MemoryMonitorManager.destroy()
            
            YLog.i(TAG, "应用结束，内存监控已销毁")
        }
    }
    
    /**
     * 示例2：在 Service 中使用
     */
    class ExampleService : Service() {
        
        override fun onCreate() {
            super.onCreate()
            
            // 在服务中启动内存监控
            MemoryMonitorManager.startMonitoring()
            
            YLog.i(TAG, "服务启动，内存监控已开始")
        }
        
        override fun onDestroy() {
            super.onDestroy()
            
            // 停止内存监控
            MemoryMonitorManager.stopMonitoring()
            
            YLog.i(TAG, "服务停止，内存监控已停止")
        }
        
        override fun onBind(intent: Intent?): IBinder? = null
    }
    
    /**
     * 示例3：在 Activity 中使用
     */
    class ExampleActivity : androidx.appcompat.app.AppCompatActivity() {
        
        override fun onCreate(savedInstanceState: android.os.Bundle?) {
            super.onCreate(savedInstanceState)
            
            // 添加自定义内存监听器
            val customListener = object : MemoryMonitor.MemoryStatusListener {
                override fun onMemoryStatusUpdate(memoryInfo: MemoryMonitor.MemoryInfo) {
                    YLog.d(TAG, "Activity 收到内存更新: $memoryInfo")
                }
                
                override fun onMemoryWarning(memoryInfo: MemoryMonitor.MemoryInfo) {
                    YLog.w(TAG, "Activity 收到内存警告")
                    // 可以在这里执行一些清理操作
                    performMemoryCleanup()
                }
                
                override fun onMemoryDanger(memoryInfo: MemoryMonitor.MemoryInfo) {
                    YLog.e(TAG, "Activity 收到内存危险警告")
                    // 执行紧急内存清理
                    performEmergencyCleanup()
                }
                
                override fun onMemoryNormal(memoryInfo: MemoryMonitor.MemoryInfo) {
                    YLog.i(TAG, "Activity 收到内存正常通知")
                }
            }
            
            MemoryMonitorManager.addMemoryListener(customListener)
            
            // 手动检查内存状态
            lifecycleScope.launch {
                val memoryInfo = MemoryMonitorManager.checkMemoryNow()
                if (memoryInfo != null) {
                    YLog.i(TAG, "当前内存状态: $memoryInfo")
                }
            }
        }
        
        override fun onDestroy() {
            super.onDestroy()
            
            // 移除监听器（如果需要）
            // MemoryMonitorManager.removeMemoryListener(customListener)
        }
        
        private fun performMemoryCleanup() {
            // 执行内存清理操作
            YLog.i(TAG, "执行内存清理操作")
            
            // 例如：清理缓存、释放不必要的资源等
            System.gc()
        }
        
        private fun performEmergencyCleanup() {
            // 执行紧急内存清理
            YLog.i(TAG, "执行紧急内存清理")
            
            // 例如：关闭非必要功能、清理大量数据等
            performMemoryCleanup()
            
            // 可以考虑重启应用或服务
        }
    }
    
    /**
     * 示例4：独立使用内存监控
     */
    class StandaloneMemoryMonitor {
        
        private val memoryMonitor = MemoryMonitor()
        
        fun startCustomMonitoring() {
            // 添加自定义监听器
            memoryMonitor.addMemoryListener(object : MemoryMonitor.MemoryStatusListener {
                override fun onMemoryStatusUpdate(memoryInfo: MemoryMonitor.MemoryInfo) {
                    // 自定义处理逻辑
                    handleMemoryUpdate(memoryInfo)
                }
                
                override fun onMemoryWarning(memoryInfo: MemoryMonitor.MemoryInfo) {
                    // 自定义警告处理
                    handleMemoryWarning(memoryInfo)
                }
                
                override fun onMemoryDanger(memoryInfo: MemoryMonitor.MemoryInfo) {
                    // 自定义危险处理
                    handleMemoryDanger(memoryInfo)
                }
                
                override fun onMemoryNormal(memoryInfo: MemoryMonitor.MemoryInfo) {
                    // 自定义正常状态处理
                    handleMemoryNormal(memoryInfo)
                }
            })
            
            // 启动监控
            memoryMonitor.startMonitoring()
        }
        
        fun stopCustomMonitoring() {
            memoryMonitor.stopMonitoring()
        }
        
        private fun handleMemoryUpdate(memoryInfo: MemoryMonitor.MemoryInfo) {
            YLog.d(TAG, "自定义内存更新处理: $memoryInfo")
            
            // 可以在这里实现自定义的内存状态处理逻辑
            // 例如：记录到数据库、发送到服务器等
        }
        
        private fun handleMemoryWarning(memoryInfo: MemoryMonitor.MemoryInfo) {
            YLog.w(TAG, "自定义内存警告处理")
            
            // 自定义警告处理逻辑
            // 例如：发送通知、记录日志、执行清理等
        }
        
        private fun handleMemoryDanger(memoryInfo: MemoryMonitor.MemoryInfo) {
            YLog.e(TAG, "自定义内存危险处理")
            
            // 自定义危险处理逻辑
            // 例如：强制清理、重启服务、发送紧急通知等
        }
        
        private fun handleMemoryNormal(memoryInfo: MemoryMonitor.MemoryInfo) {
            YLog.i(TAG, "自定义内存正常处理")
            
            // 自定义正常状态处理逻辑
        }
    }
    
    /**
     * 示例5：测试和调试
     */
    object MemoryMonitorTester {
        
        suspend fun runTests() {
            YLog.i(TAG, "=== 开始内存监控测试 ===")
            
            // 1. 测试手动检查
            YLog.i(TAG, "测试1: 手动检查内存状态")
            val memoryInfo = MemoryMonitorManager.checkMemoryNow()
            YLog.i(TAG, "内存信息: $memoryInfo")
            
            // 2. 测试内存摘要
            YLog.i(TAG, "测试2: 获取内存摘要")
            val summary = MemoryMonitorManager.getMemorySummary()
            YLog.i(TAG, "内存摘要: $summary")
            
            // 3. 测试强制检查
            YLog.i(TAG, "测试3: 强制内存检查")
            MemoryMonitorManager.forceMemoryCheck()
            
            // 4. 测试监控状态
            YLog.i(TAG, "测试4: 检查监控状态")
            val isMonitoring = MemoryMonitorManager.isMonitoring()
            val currentStatus = MemoryMonitorManager.getCurrentMemoryStatus()
            YLog.i(TAG, "监控状态: $isMonitoring, 当前内存状态: $currentStatus")
            
            YLog.i(TAG, "=== 内存监控测试完成 ===")
        }
    }
}
