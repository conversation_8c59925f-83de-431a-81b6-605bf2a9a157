package com.yupao.wxcollect.service.accessibility.filter

import android.os.Build
import android.os.Bundle
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.yupao.wxcollect.service.accessibility.process.AutoSwitchAccountTask
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.absoluteValue


/**
 * <AUTHOR>
 * @Date 2023/4/21/021 14:54
 * @Description 滚动
 */
open class ScrollViewHandler(
    private val offsetX: Int = 0,
    private val offsetY: Int = 0,
    private val viewPosition: ViewPosition? = null,
    override val delayTimeAfterPrev: Long = IViewHandler.INVALID_TIME,
    override val maxPerformCount: Int = 1,
    override var pageHandler: IPageHandler? = null
): IViewHandler {
    override var status: Int = HandlerStatus.IDLE

    private var performCount: Int = 0

    override suspend fun acceptEventType(event: AccessibilityEvent?): Boolean {
        return AutoSwitchAccountTask.supportEventType(event?.eventType)
    }

    private var isRunning = false

    override suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean {
        event ?: return false
        status = HandlerStatus.DISPATCH
        val position = viewPosition
        YLog.i("ScrollViewHandler", "dispatchEvent: position = $position")
        if (position != null) {
            if (position.isShown()) {
                status = HandlerStatus.SUCCEED
                performCount++
                return true
            }
            (pageHandler as? PageHandler)?.cacheEvent?.source?.let {
                position.transform(it)
                if (position.isShown()) {
                    status = HandlerStatus.SUCCEED
                    performCount++
                    return true
                }
            }
        }
        val source = event.source
//        AccessibilityNodeInfoUtil.printNodeInfo(source)
        val list = AccessibilityNodeInfoUtil.findScrollableView(source)
//        list?.map { it.className }?.forEach {
//            YLog.i("ScrollViewHandler", "scroll-view: $it")
//        }
        val nodeInfo = list?.lastOrNull()
        val scrollable = nodeInfo?.isScrollable ?: false
        YLog.i("ScrollViewHandler", "dispatchEvent: scrollable = $scrollable, nodeInfo = $nodeInfo")
        if (scrollable && !isRunning) {
            status = HandlerStatus.WAIT_RUNNING
            performCount++
            GlobalScope.launch {
                isRunning = true
                delay(1000)
                status = HandlerStatus.RUNNING
                val arguments = Bundle()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    if (offsetX != 0) {
                        arguments.putInt(AccessibilityNodeInfo.ACTION_ARGUMENT_MOVE_WINDOW_X, offsetX.absoluteValue)
                    }
                    if (offsetY != 0) {
                        arguments.putInt(AccessibilityNodeInfo.ACTION_ARGUMENT_MOVE_WINDOW_Y, offsetY.absoluteValue)
                        arguments.putInt(AccessibilityNodeInfo.ACTION_ARGUMENT_MOVE_WINDOW_Y, offsetY.absoluteValue)
                    }
                }
                var result = if (offsetY > 0) {
                    nodeInfo?.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
                } else if (offsetY < 0) {
                    nodeInfo?.performAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD)
                } else false
                if (result != true) {
                    result = performScroll()
                }
                YLog.i("ScrollViewHandler", "dispatchEvent: scrollY = $offsetY, scrollX = $offsetX, result = $result")
                status = if (!result) {
                    HandlerStatus.FAILED
                } else {
                    HandlerStatus.SUCCEED
                }
                isRunning = false
            }
            return false
        }
        return false
    }

    private fun performScroll(): Boolean {
        val sx = 500
        val sy = 800
        val ex = sx - offsetX
        val ey = sy - offsetY
        val result = CmdUtil.execCmd("input swipe $sx $sy $ex $ey 500")
        return result == null
    }

    override fun isRemove(): Boolean {
        return performCount >= maxPerformCount
    }

    override fun getWillPerformCount(): Int {
        return performCount
    }
}