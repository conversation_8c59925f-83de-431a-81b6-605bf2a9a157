package com.yupao.wxcollect.service.procedure.entity

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
data class TaskRecordEntity(
    // 任务唯一标识
    val unique: String,
    // 名称
    val name: String,
    // 主副系统
    val appType: String,
    // 密码
    val password: String?,
    // 间隔时间，单位：秒
    val interval: Int? = null,
    // 查询时间，单位：秒
    val queryTime: Int? = null,
    // 任务排序
    val taskSort: Int? = null,
    // 任务Code
    val taskCode: String? = null,
)