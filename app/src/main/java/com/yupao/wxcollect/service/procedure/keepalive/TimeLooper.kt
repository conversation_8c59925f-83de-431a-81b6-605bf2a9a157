package com.yupao.wxcollect.service.procedure.keepalive

import android.os.Handler
import android.os.Looper
import androidx.annotation.IntRange

/**
 * 时间轮询
 *
 * <p>创建时间：2024/5/17/017</p>
 *
 * <AUTHOR>
 */
class TimeLooper(
    private val callback: (secondInterval: Int) -> Unit
) {

    companion object {
        private const val TAG = "TimeLooper"

        private const val stopInterval = 0
    }

    private val mainHandler = Handler(Looper.getMainLooper())

    // 间隔分钟
    @Volatile
    private var secondInterval = stopInterval

    private val runnable = Runnable {
        this.callback.invoke(secondInterval)
        postTask()
    }

    private fun postTask() {
        val second = secondInterval
        if (second <= stopInterval) {
            return
        }
        mainHandler.postDelayed(runnable, second * 1000L)
    }

    fun start(@IntRange(from = 1, to = Int.MAX_VALUE.toLong()) secondInterval: Int) {
        val isRunning = isRunning()
        this.secondInterval = secondInterval
        if (!isRunning) {
            postTask()
        }
    }

    fun stop() {
        this.secondInterval = stopInterval
        mainHandler.removeCallbacks(runnable)
    }

    fun isRunning() = secondInterval > stopInterval
}