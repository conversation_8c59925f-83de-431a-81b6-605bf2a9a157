package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.yupao.wxcollect.service.procedure.entity.ReportConfig

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 17:00
 * @Description 普通消息请求数据
 */
@Keep
data class MessageRequestEntity(
    //群聊记录内容
    val list: List<ContentEntity>?,
    //聊天用户列表（message表+bizchatmessage表里的用户）
    val msg_user_list: List<UserEntity>?,
    @Transient
    val queryMin: String?,
    @Transient
    val userEntity: com.yupao.wxcollect.service.procedure.entity.query.UserEntity?,
    @Transient
    val config: ReportConfig?,
): CommReportEntity(queryMin, userEntity, config)

@Keep
data class ContentEntity(
    //微信群名
    val group_name: String?,
    //微信群ID
    val group_id: String?,
    //发送人微信ID
    val msg_user_id: String?,
    //消息详情
    val msg_content: String?,
    //消息发送时间（毫秒时间戳）
    val msg_time: String?,
)

@Keep
data class UserEntity(
    // 微信ID
    @SerializedName("id")
    val username: String? = null,
    // 微信名称
    @SerializedName("name")
    val nickname: String? = null,
)