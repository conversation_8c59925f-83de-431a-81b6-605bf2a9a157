package com.yupao.wxcollect.service.procedure.task.customize.executor.sql

import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import java.io.File

/**
 * 文件损坏Helper
 * 不要设备偶现文件夹不能创建文件，即使在外部创建也不行。导致任务持续失败，所以当文件创建失败时，尝试替换文件目录
 *
 * <p>创建时间：2025/4/15/015</p>
 *
 * <AUTHOR>
 */
object FileCorruptionHelper {
    const val TAG = "FileCorruptionHelper"

    private const val tempName = "temp.txt"

    fun checkFileCorruption(targetDir: File, fileName: String): String? {
        // 尝试创建一个新文件
        if (createFileTry(targetDir)) {
            return null
        }
        // 替换成新文件目录
        val newDir = File(targetDir.parentFile ?: return null, createNewFileName(fileName))
        if (newDir.mkdirs()) {
            YLog.w(TAG, "replace dir: ${newDir.absolutePath}")
            return newDir.absolutePath
        } else {
            YLog.w(TAG, "replace dir mkdirs failed: ${newDir.absolutePath}")
        }
        return null
    }

    private fun createNewFileName(fileName: String): String {
        return "${fileName}_${TimeUtil.fileNameFormat()}"
    }

    private fun createFileTry(targetDir: File): Boolean {
        val tempFile = File(targetDir, tempName)
        val isCreated = kotlin.runCatching {
            tempFile.parentFile.takeUnless { it.exists() }?.mkdirs()
            tempFile.delete()
            tempFile.createNewFile()
        }.getOrElse {
            YLog.printException(TAG, it, "createFileTry")
            false
        }
        tempFile.delete()
        if (!isCreated) {
            YLog.w(TAG, "createFileTry failed: ${tempFile.absolutePath}")
        }
        return isCreated
    }
}