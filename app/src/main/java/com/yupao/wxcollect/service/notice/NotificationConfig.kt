package com.yupao.wxcollect.service.notice

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * @Date 2023/4/10/010 16:10
 * @Description
 */
@Keep
@Parcelize
data class NotificationConfig(
    // 通知渠道
    val channelId: String? = "report",

    val channelName: String? = "上报",

    // 点击通知栏着陆页
    val pendingActivityPath: String? = null,

    // 通知ID
    var noticeId: Int? = null,

    // 通知标题
    var title: String? = null,

    // 通知内容
    var content: String? = null,

    // 更新时间
    var updateTime: Long = 0,
): Parcelable