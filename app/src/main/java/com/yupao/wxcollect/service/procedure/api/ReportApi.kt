package com.yupao.wxcollect.service.procedure.api

import com.yupao.data.net.yupao.BaseData
import com.yupao.data.net.yupao.JavaNetEntity
import com.yupao.data.net.yupao.NetRequestInfo
import com.yupao.wxcollect.service.procedure.entity.PhoneNoNetEntity
import com.yupao.wxcollect.service.procedure.entity.WechatNetEntity
import com.yupao.wxcollect.service.procedure.entity.request.*
import okhttp3.MultipartBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.*


/**
 * <AUTHOR>
 * @Date 2023/4/13/013 16:59
 * @Description https://w3nu1yaadv.feishu.cn/docx/OOMZd4DtaoqKFhxFM8Oc40cQnWb
 */
interface ReportApi {
    companion object {
        const val applyToAddFriendsUrl = "/crm-collect/v1/wechat/friend/applyReport"
    }

    /**
     * 微信号变更 http://yapi.3pvr.com/project/1685/interface/api/84006
     */
    @POST("/crm-collect/v1/collectWechat/regWechat")
    suspend fun changeWechat(@Body entity: ChangeWechatRequestModel?): JavaNetEntity<BaseData>

    /**
     * 上传普通聊天记录
     */
    @Multipart
    @POST("/backend/collect/wechat/uploadChatHistory")
    suspend fun uploadChatHistory(@Part file: List<MultipartBody.Part>?): NetRequestInfo<BaseData>

    /**
     * 上传微信群信息-同步好友列表
     */
    @POST("/backend/collect/wechat/syncFriendTabulation")
    suspend fun syncFriendTabulation(@Body entity: GroupListSyncRequestEntity?): NetRequestInfo<BaseData>

    /**
     * 上传微信群信息-同步群操作
     */
    @POST("/backend/collect/wechat/syncGroupOperation")
    suspend fun syncGroupOperation(@Body entity: GroupOperationSyncRequestEntity?): NetRequestInfo<BaseData>

    /**
     * 上传微信群信息-同步被踢信息
     */
    @POST("/backend/collect/wechat/syncBootedOut")
    suspend fun syncBootedOut(@Body entity: GroupRemoveSyncRequestEntity?): NetRequestInfo<BaseData>

    /**
     * 上传微信群信息-同步群信息
     */
    @POST("/backend/collect/wechat/syncGroup")
    suspend fun syncGroup(@Body entity: GroupInfoSyncRequestEntity?): NetRequestInfo<BaseData>

    /**
     * 上传微信群信息-同步邀请信息
     */
    @POST(applyToAddFriendsUrl)
    suspend fun applyToAddFriends(@Body entity: ApplyToAddFriendsParamsModel?): JavaNetEntity<BaseData>

    /**
     * 添加微信好友
     */
    @POST("/crm/v1/waiterCustomer/add")
    suspend fun addFriendInfo(@Body map: MutableMap<String, Any?>): JavaNetEntity<BaseData>

    /**
     * 更新好友信息
     */
    @POST("/crm/v1/waiterCustomer/updateWeChatCronyInfo")
    suspend fun updateWeChatCronyInfo(@Body entity: UpdateFriendRequestEntity?): JavaNetEntity<BaseData>

    /**
     * 更新好友信息
     */
    @POST("/crm/v1/waiterCustomer/delWeChatCrony")
    suspend fun delWeChatCrony(@Body entity: DeleteFriendRequestEntity?): JavaNetEntity<BaseData>

    @GET
    fun urlContent(@Url url: String): Call<ResponseBody>

    /**
     * 通用的数据上报
     * http://yapi.3pvr.com/project/1685/interface/api/100813
     * @param model [ReportNetModel] 上传参数
     */
    @POST("/crm-collect/v1/device-task/reportFile")
    suspend fun commonReport(
        @Body model: ReportNetModel
    ): JavaNetEntity<Any>

    /**
     * 根据手机号查询资产信息
     * https://yapi.3pvr.com/project/1340/interface/api/128392
     * @param entity [WechatAccountModel]
     */
    @POST("/crm-aggr/v1/asset/queryByPhoneNo")
    suspend fun queryByPhoneNo(
        @Body entity: WechatAccountModel
    ): JavaNetEntity<PhoneNoNetEntity>

}