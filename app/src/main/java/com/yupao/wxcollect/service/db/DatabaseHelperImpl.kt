package com.yupao.wxcollect.service.db

import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.SystemStatusManager
import com.yupao.wxcollect.service.procedure.FixedDelayStrategy
import com.yupao.wxcollect.service.procedure.RetryHelper
import com.yupao.wxcollect.service.procedure.task.DatabaseCorruptException
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.sync.Semaphore
import net.sqlcipher.Cursor
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteDatabaseCorruptException
import net.sqlcipher.database.SQLiteDatabaseHook
import net.sqlcipher.database.SQLiteException
import java.io.File
import java.lang.reflect.Field
import java.util.concurrent.atomic.AtomicReference
import kotlin.reflect.KClass

/**
 * <AUTHOR>
 * @Date 2023/4/6/006 9:44
 * @Description 数据库帮助类
 */
class DatabaseHelperImpl(
    private val retryCountIfFailed: Int = 3,
    // 最多支持[maxConcurrency]线程并行查询
    private val maxConcurrency: Int = 5,
): IDatabaseHelper {
    companion object {
        private const val TAG = "DatabaseHelper"
    }

    private val semaphore = Semaphore(maxConcurrency)

    private var database: SQLiteDatabase? = null

    private var databasePath: String? = null

    private var password: String? = null

    // 数据库状态
    private val status = AtomicReference<DatabaseStatus>(DatabaseStatus.Init)

    // 首次失败原因
    private var firstFailedExp: Throwable? = null

    override suspend fun <T: Any> query(
        script: String?,
        cls: KClass<T>?,
        tag: String?,
        interceptor: ((Cursor, String) -> Any?)?
    ): List<T>? {
        cls ?: return null
        val map = parseField(cls)
        val keyList = map.keys.toList()
        val result = query(script, keyList, tag, interceptor)
        return result?.map {
            val instance = cls.java.newInstance()
            it.forEach { entry ->
                val key = entry.key
                val value = entry.value
                val field = map[key]
                if (value is Number? || value is Boolean?) {
                    field?.set(instance, value)
                } else {
                    field?.set(instance, value?.toString())
                }
            }
            instance
        }
    }

    override suspend fun query(
        script: String?,
        keyList: List<String>?,
        tag: String?,
        interceptor: ((Cursor, String) -> Any?)?
    ): List<Map<String, Any?>>? {
        if (isClosed()) {
            YLog.i(tag, "database closed: $firstFailedExp")
            throw firstFailedExp ?: RuntimeException("数据库已关闭")
        }
        val result = mutableListOf<Map<String, Any?>>()
        val startTime = TimeUtil.getCurrTime()
        if ((script?.length ?: 0) > 500) {
            YLog.d(TAG, "$tag query: script = $script")
            YLog.i(TAG, "$tag query: script = ${script?.substring(0, 500)} ...")
        } else {
            YLog.i(TAG, "$tag query: script = $script")
        }
        YLog.i(TAG, "$tag query: keyList = $keyList")
        try {
            semaphore.acquire()
            // 查询数据库，如果出现了异常，尝试重新打开数据库，如果还是失败则抛出异常
            val retryHelper = RetryHelper<Unit>(1, FixedDelayStrategy(1000)) { count, _ ->
                YLog.i(tag, "query failed, retry count: $count")
                if (isClosed()) return@RetryHelper true
                delay(2000)
                if (isClosed()) return@RetryHelper true
                // 尝试重新打开数据库
                val openResult = openRetry()
                YLog.i(tag, "open retry result: ${openResult.code}")
                openResult.code != DatabaseOpenResultEntity.CODE_OK
            }
            retryHelper.perform {
                kotlin.runCatching {
                    performQuery(script, tag, keyList, interceptor, result)
                }
            }.getRecently()?.onFailure { exp ->
                YLog.i(tag, "onFailure: $exp")
                // 重试之后仍然失败，抛出首次失败的异常
                throwException(exp)
            }
        } finally {
            semaphore.release()
        }
        YLog.i(TAG, "$tag query result: size: ${result.size}, " +
                "duration = ${TimeUtil.getCurrTime() - startTime}ms")
        result.take(2).forEach {
            YLog.d(TAG, "$tag $it")
        }
        return result
    }

    @Synchronized
    private fun throwException(exp: Throwable) {
        if (this.firstFailedExp == null) {
            this.firstFailedExp = if (exp is SQLiteDatabaseCorruptException) {
                DatabaseCorruptException()
            } else {
                exp
            }
        }
        val throwExp = firstFailedExp ?: exp
        //文件破损
        if(firstFailedExp is DatabaseCorruptException){
            SystemStatusManager.addStatusMessage(SystemStatusManager.StatusType.FILE_CORRUPTED,"等待下一次执行或重新执行任务")
        }
        throw throwExp
    }

    private suspend fun performQuery(
        script: String?,
        tag: String?,
        keyList: List<String>?,
        interceptor: ((Cursor, String) -> Any?)?,
        result: MutableList<Map<String, Any?>>
    ) {
        val db = database ?: kotlin.run {
            throw RuntimeException("database empty")
        }
        var currCursor: Cursor? = null
        try {
            val cursor = db.rawQuery(script, null)
            currCursor = cursor
            val count = cursor?.count ?: 0
            YLog.i(TAG, "$tag query: count: $count")
            val notFoundMap = mutableMapOf<String, Int>()
            while (cursor.moveToNext()) {
                val instance = mutableMapOf<String, Any?>()
                if (keyList.isNullOrEmpty()) {
                    for (i in 0 until cursor.columnCount) {
                        kotlin.runCatching {
                            val key = cursor.getColumnName(i)
                            val value = cursor.getString(i)
                            instance[key] = value
                        }
                    }
                } else {
                    keyList.forEach looper@{ key ->
                        val value = interceptor?.invoke(cursor, key) ?: kotlin.run {
                            val columnIndex = cursor.getColumnIndex(key)
                            if (columnIndex >= 0) {
                                cursor.getString(columnIndex)
                            } else {
                                val size = notFoundMap[key] ?: 0
                                notFoundMap[key] = size + 1
                                null
                            }
                        }
                        instance[key] = value
                    }
                }
                result.add(instance)
            }
            if (notFoundMap.isNotEmpty()) {
                notFoundMap.forEach {
                    YLog.w(TAG, "$tag query: ${it.key} not found, count: ${it.value}")
                }
            }
            // 05-20 10:10:10.497 15228 15286 E DefaultDatabaseErrorHandler: Corruption reported by sqlite on database, deleting: /data/user/0/com.yupao.wxcollect/files/wechat/a6462c42c2c9e4e43b3e064efd9692a9/EnMicroMsg.db
            // 05-20 10:10:10.497 15228 15286 E DefaultDatabaseErrorHandler: Database object for corrupted database is already open, closing
            // 05-20 10:10:10.505 15228 15286 E DefaultDatabaseErrorHandler: deleting the database file: /data/user/0/com.yupao.wxcollect/files/wechat/a6462c42c2c9e4e43b3e064efd9692a9/EnMicroMsg.db
            // 05-20 10:10:10.902 15228 15286 W System.err: net.sqlcipher.database.SQLiteDatabaseCorruptException: database disk image is malformed
            // 如果发生了SQLiteDatabaseCorruptException异常，可能会直接删除数据库文件
        } catch (e: Exception) {
            val exist = checkDatabaseFileExist()
            YLog.printException(tag, e, "query, exist: $exist")
            // 出现异常，往外面抛出去
            throw e
        } finally {
            kotlin.runCatching {
                currCursor?.close()
            }
        }
    }

    @Synchronized
    private suspend fun onQueryFailed(exp: Throwable) {

    }

    override fun checkDatabaseFileExist(): Boolean {
        val path = databasePath ?: return false
        return kotlin.runCatching {
            File(path).exists()
        }.getOrNull() ?: false
    }

    override fun databaseFilePath(): String? {
        return databasePath
    }

    private fun parseField(entity: KClass<*>): Map<String, Field?> {
        val map = mutableMapOf<String, Field>()
        entity.java.declaredFields.forEach {
            it.isAccessible = true
            val ignoreAnnot = it?.annotations?.find { annotation ->
                annotation is Ignore
            }
            if (ignoreAnnot == null) {
                map[it.name] = it
            }
        }
        return map
    }

    private fun isClosed(): Boolean {
        return status.get() is DatabaseStatus.Closed
    }

    private fun isOpen(): Boolean {
        return status.get() is DatabaseStatus.Ready && database?.isOpen == true
    }

    @Synchronized
    override fun open(databasePath: String?, password: String?): DatabaseOpenResultEntity {
        if (isOpen()) {
            YLog.i(TAG, "open already, ignore.")
            return DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_OK)
        }
        databasePath ?: return DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, "数据库路径为空")
        password ?: return DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, "数据库密码为空")
        val databaseFile = File(databasePath)
        if (!databaseFile.exists()) {
            YLog.i(TAG, "open: databasePath: $databasePath not exist")
            return DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, "数据库文件不存在")
        }
        var retryCount = 0
        var result: DatabaseOpenResultEntity? = null
        status.set(DatabaseStatus.Opening)
        while (retryCount < retryCountIfFailed) {
            result = (performOpen(databasePath, password) ?: kotlin.run {
                status.set(DatabaseStatus.Ready)
                return DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_OK)
            })
            YLog.i(TAG, "open retryCount: $retryCount")
            retryCount++
        }
        status.set(DatabaseStatus.Failed)
        return result ?: DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, "数据库打开失败")
    }

    private fun performOpen(databasePath: String, password: String): DatabaseOpenResultEntity? {
        return kotlin.runCatching {
            YLog.i(TAG, "performOpen")
            val startTime = TimeUtil.getCurrTime()
            SQLiteDatabase.loadLibs(App.getContext())
            val hook = object : SQLiteDatabaseHook {
                override fun preKey(database: SQLiteDatabase) {}

                override fun postKey(database: SQLiteDatabase) {
                    database.rawExecSQL("PRAGMA cipher_migrate;")  //最关键的一句！！！
                }
            }
            this.databasePath = databasePath
            this.password = password
            database = SQLiteDatabase.openDatabase(
                databasePath,
                password,
                null,
                SQLiteDatabase.NO_LOCALIZED_COLLATORS,
                hook,
                CommDatabaseErrorHandler()
            )
            YLog.i(TAG, "open finished: ${TimeUtil.getCurrTime() - startTime}ms")
        }.exceptionOrNull()?.apply {
            YLog.e(TAG, "open failed: filePath: ${databasePath}, password: ${password}, exp: $this")
            this.printStackTrace()
        }?.let {
            if (it is SQLiteException && it.toString().contains("file is not a database")) {
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_PASSWORD_ERROR, "密码错误")
            } else if (it is SQLiteException && it.toString().contains("error code 14")) {
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_PATH_ERROR, "存储路径不能打开，请在配置中重新提交")
            } else {
                DatabaseOpenResultEntity(DatabaseOpenResultEntity.CODE_FAILED, "异常: $it")
            }
        }
    }

    private fun openRetry(): DatabaseOpenResultEntity {
        return open(this.databasePath, this.password)
    }

    override fun close() {
        kotlin.runCatching {
            YLog.i(TAG, "close database: $databasePath")
            status.set(DatabaseStatus.Closed)
            database?.close()
        }.onFailure {
            YLog.printException(TAG, it, "closeDb: $databasePath")
        }
    }

    fun rawQuery(script: String?, tag: String?, maxCount: Int = 10) {
        val cursor = database?.rawQuery(script, null) ?: return
        val count = cursor.count
        YLog.i(TAG, "$tag query: count: $count")
        var index = 0
        while (cursor.moveToNext() && index < maxCount) {
            val columnCount = cursor.columnCount
            val sb = StringBuilder()
            for (i in 0 until columnCount) {
                val key = kotlin.runCatching {
                    cursor.getColumnName(i)
                }.getOrNull()
                val value = kotlin.runCatching {
                    cursor.getString(i)
                }.getOrNull()
                sb.append(", ").append(key).append("=").append(value)
            }
            if (sb.isNotEmpty()) {
                YLog.i(TAG, "$tag ${sb.substring(1)}")
            }
            index++
        }
        cursor.close()
    }
}