package com.yupao.wxcollect.service.procedure.keepalive

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 15:02
 * @Description 每分钟定时闹钟唤醒广播
 */
class TimeReceiver(
    private val callback: () -> Unit
): BroadcastReceiver() {

    private var isRegister = false

    companion object {
        private const val ACTION = Intent.ACTION_TIME_TICK
    }

    fun register(context: Context?) {
        if (isRegister) {
            return
        }
        isRegister = true
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION)
        context?.registerReceiver(this, intentFilter)
    }

    fun unregister(context: Context?) {
        if (!isRegister) {
            return
        }
        isRegister = false
        context?.unregisterReceiver(this)
    }

    override fun onReceive(p0: Context?, p1: Intent?) {
        if (p1?.action != ACTION) {
            return
        }
        callback.invoke()
    }
}