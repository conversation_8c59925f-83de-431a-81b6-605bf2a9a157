package com.yupao.wxcollect.service.procedure.dispatch.handler

import com.yupao.wxcollect.service.db.DatabaseHelperImpl
import com.yupao.wxcollect.service.db.DatabaseOpenResultEntity
import com.yupao.wxcollect.service.db.IDatabaseHelper
import com.yupao.wxcollect.service.procedure.dispatch.status.IStatusChanged
import com.yupao.wxcollect.service.procedure.dispatch.status.TrainExecutorStatus
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity
import com.yupao.wxcollect.service.procedure.task.WechatAccountNewTask
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/5/005</p>
 *
 * <AUTHOR>
 */
class DatabaseHandler constructor(
    flag: String,
    wechatDatabasePath: String,
    private val password: String,
    private val statusHandler: IStatusChanged,
) {

    private val tag = "DatabaseHandler:${flag}"

    val databaseHelp: IDatabaseHelper = DatabaseHelperImpl()

    private val wechatAccountTask = WechatAccountNewTask(wechatDatabasePath)

    suspend fun open(databasePath: String): UserEntity? {
        val result = databaseHelp.open(databasePath = databasePath, password = password)
        if (result.code != DatabaseOpenResultEntity.CODE_OK) {
            val errorMsg =
                "${result.message ?: "打开数据库失败"}, databasePath: $databasePath, password: $password"
            statusHandler.updateStatus(TrainExecutorStatus.Failed(errorMsg))
            return null
        }
        val userEntity = wechatAccountTask.performTaskUser(databaseHelp)
        YLog.i(tag, "user info: $userEntity")
        if (userEntity == null) {
            statusHandler.updateStatus(TrainExecutorStatus.Failed("未获取到用户信息"))
        }
        return userEntity
    }

}