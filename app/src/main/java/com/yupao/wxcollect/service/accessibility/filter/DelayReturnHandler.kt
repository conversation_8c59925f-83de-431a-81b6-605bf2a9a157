package com.yupao.wxcollect.service.accessibility.filter

import android.os.SystemClock
import android.view.accessibility.AccessibilityEvent
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.yupao.wxcollect.service.accessibility.process.AutoSwitchAccountTask
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/4/28/028 17:46
 * @Description
 */
class DelayReturnHandler(
    // 历史页面
    private val historyPage: List<String>?,
    private val delayTime: Long = IViewHandler.DEFAULT_DELAY_TIME,
    override val delayTimeAfterPrev: Long = IViewHandler.INVALID_TIME,
    override val maxPerformCount: Int = 1,
    override var pageHandler: IPageHandler? = null,
    // 任务执行的时候
    private val onTaskPerformed: (suspend () -> Unit)? = null,
) : IViewHandler {
    override var status: Int = HandlerStatus.IDLE

    private var currReturnPage: String? = null

    private var performCount: Int = 0

    private var willPerformCount: Int = 0

    override fun getWillPerformCount(): Int {
        return willPerformCount
    }

    override suspend fun acceptEventType(event: AccessibilityEvent?): Boolean {
        return AutoSwitchAccountTask.supportEventType(event?.eventType)
    }

    override suspend fun dispatchEvent(event: AccessibilityEvent?): Boolean {
        if (acceptEventType(event)) {
            status = HandlerStatus.DISPATCH
            val className = event?.className?.toString()
            willPerformCount++
            status = HandlerStatus.WAIT_RUNNING
            SystemClock.sleep(delayTime)
            status = HandlerStatus.RUNNING
            val result = finishPage(className) == null
            if (result) {
                performCount++
                checkPageReturn(2000, className)
                onTaskPerformed?.invoke()
                status = HandlerStatus.SUCCEED
            } else {
                status = HandlerStatus.FAILED
            }
            return result
        }
        return false
    }

    private fun finishPage(page: String?): String? {
        page ?: return "ReturnPageHandler, failed page: $page"
        val list = CmdUtil.execCmdWithBack("dumpsys window | grep mCurrentF")
        for (item in list) {
            if (item.contains(page)) {
                return CmdUtil.execCmd("input keyevent 4")
            }
        }
        return "ReturnPageHandler, not found page: $page, list = $list"
    }

    private suspend fun checkPageReturn(delayTime: Long, page: String?) {
        currReturnPage = page
        if (historyPage == null) {
            return
        }
        ProcessLifecycleOwner.get().lifecycleScope.launch(Dispatchers.IO) {
            delay(delayTime)
            val finishPage = currReturnPage
            if (historyPage.lastOrNull() == finishPage && finishPage != null) {
                YLog.i("ReturnPageHandler", "delay ${delayTime}ms finish page $finishPage retry")
                val result = finishPage(finishPage)
                if (result == null) {
                    onTaskPerformed?.invoke()
                }
            }
        }
    }

    override fun isRemove(): Boolean {
        return performCount >= maxPerformCount
    }

}