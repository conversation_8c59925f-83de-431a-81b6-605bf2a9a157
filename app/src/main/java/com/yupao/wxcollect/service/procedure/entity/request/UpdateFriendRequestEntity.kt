package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/6/21/021 9:10
 * @Description
 */
@Keep
data class UpdateFriendRequestEntity(
    // 客服微信id
    val waiterAccount: String?,
    // 客户微信信息
    val customerInfos: List<CustomerInfo>?,
)

@Keep
data class CustomerInfo(
    // 客户微信id
    val customerAccount: String?,
    // 最近聊天时间
    val recentChatDate: String?,
    // 客户备注
    val remark: String?,
)