package com.yupao.wxcollect.service.procedure.entity.query

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 * <AUTHOR>
 * @Date 2023/4/17/017 16:33
 * @Description 需要移除的实体 不要轻易修改属性名称
 */
@Keep
data class GroupInfoQueryEntity(
    //微信群ID
    @SerializedName("group_id")
    val chatroomname: String? = null,
    //微信群名
    @SerializedName("group_name")
    val nickname: String? = null,
    //群成员数量
    @SerializedName("group_member_count")
    val memberCount: String? = null,
    //群主ID
    @SerializedName("group_master_id")
    val roomowner: String? = null,
    //加入时间（毫秒时间戳）
    @SerializedName("join_time")
    val createTime: String? = null,
)