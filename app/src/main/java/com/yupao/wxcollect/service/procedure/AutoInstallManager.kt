package com.yupao.wxcollect.service.procedure

import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.liulishuo.filedownloader.BaseDownloadTask
import com.liulishuo.filedownloader.FileDownloadListener
import com.liulishuo.filedownloader.FileDownloader
import com.yupao.data.protocol.data
import com.yupao.scafold.ktx.filterNotLoading
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.App
import com.yupao.wxcollect.ui.config.model.ReportConfigRepoEntryPoint
import com.yupao.wxcollect.ui.setup.SetupRepoEntryPoint
import com.yupao.wxcollect.ui.setup.VersionEntity
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import java.io.File

/**
 * <AUTHOR>
 * @Date 2023/6/26/026 18:24
 * @Description 静默升级管理者
 */
object AutoInstallManager {
    private const val TAG = "AutoInstallManager"

    // 每隔10分钟检测一次
    private const val checkInterval = 10 * 60 * 1000L

    private var lastCheckTime = 0L

    private suspend fun phoneNumber(): String? {
        return EntryPointAccessors.fromApplication(
            App.getContext() ?: return null,
            ReportConfigRepoEntryPoint::class.java
        ).getRepo().queryPhoneNumber().firstOrNull()
    }

    fun checkVersion() {
        val currTime = TimeUtil.getCurrTime()
        if (currTime - lastCheckTime < checkInterval) {
            return
        }
        lastCheckTime = currTime
        ProcessLifecycleOwner.get().lifecycleScope.launch {
            try {
                val phoneNumber = phoneNumber()
                if (phoneNumber.isNullOrEmpty()) {
                    return@launch
                }
                EntryPointAccessors.fromApplication(
                    App.getContext() ?: return@launch,
                    SetupRepoEntryPoint::class.java
                ).getSetupRepo()
                    .checkVersion(phoneNumber)
                    .filterNotLoading()
                    .catch { ex ->
                        YLog.printException("$TAG-checkVersion", ex)
                    }
                    .collectLatest {
                        checkUpgrade(it.data?.getData())
                    }
            } catch (e: Exception) {
                YLog.printException(TAG, e)
            }
        }
    }

    private fun checkUpgrade(entity: VersionEntity?) {
        val version = entity?.version ?: return
        val currVersion = AndroidSystemUtil.getVersionCode()
        val newVersion = version.toIntOrNull() ?: return
        YLog.i(TAG, "checkUpgrade: currVersion = $currVersion, newVersion = $newVersion")
        if (newVersion > currVersion && !entity.download_link.isNullOrEmpty()) {
            performUpgrade(entity.download_link)
        }
    }

    fun performUpgrade(url: String) {
        val appFile = File(App.getContext()?.filesDir, "app.apk")
        if (appFile.exists()) {
            appFile.delete()
        }
        appFile.parentFile?.takeUnless { it.exists() }?.mkdirs()
        val appFilePath = appFile.absolutePath
        YLog.i(TAG, "start download")
        FileDownloader.setup(App.getContext())
        FileDownloader.getImpl().create(url)
            .setPath(appFilePath)
            .apply {
                listener = object : FileDownloadListener() {
                    override fun pending(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {

                    }

                    override fun connected(
                        task: BaseDownloadTask?,
                        etag: String?,
                        isContinue: Boolean,
                        soFarBytes: Int,
                        totalBytes: Int
                    ) {
                        YLog.i(
                            TAG,
                            "connected: etag = $etag, isContinue = $isContinue, soFarBytes = $soFarBytes, totalBytes = $totalBytes"
                        )
                    }

                    override fun progress(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {
                    }

                    override fun blockComplete(task: BaseDownloadTask?) {
                    }

                    override fun retry(task: BaseDownloadTask?, ex: Throwable?, retryingTimes: Int, soFarBytes: Int) {
                    }

                    override fun completed(task: BaseDownloadTask) {
                        YLog.i(TAG, "download completed, start install")
                        installApk(appFilePath)
                    }

                    override fun paused(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {

                    }

                    override fun error(task: BaseDownloadTask, e: Throwable) {
                        e.printStackTrace()
                        YLog.printException("app-upgrade", e)
                    }

                    override fun warn(task: BaseDownloadTask) {

                    }
                }
            }
            .start()
    }

    private fun installApk(appFilePath: String) {
        YLog.i(TAG, "start install apk")
        CmdUtil.executeAsync("pm install -r $appFilePath") { result ->
            YLog.i(TAG, "installApk: appFilePath = $appFilePath, result = ${result.result}")
        }
    }
}