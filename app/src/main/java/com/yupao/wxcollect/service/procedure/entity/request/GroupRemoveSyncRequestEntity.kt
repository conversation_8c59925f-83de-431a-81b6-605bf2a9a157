package com.yupao.wxcollect.service.procedure.entity.request

import androidx.annotation.Keep
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.query.RemoveQueryEntity
import com.yupao.wxcollect.service.procedure.entity.query.UserEntity

/**
 * <AUTHOR>
 * @Date 2023/4/17/017 16:05
 * @Description
 */
@Keep
data class GroupRemoveSyncRequestEntity(
    @Transient
    val queryMin: String?,
    @Transient
    val userEntity: UserEntity?,
    @Transient
    val config: ReportConfig?,
    val list: List<RemoveQueryEntity>?,
): CommReportEntity(queryMin, userEntity, config)