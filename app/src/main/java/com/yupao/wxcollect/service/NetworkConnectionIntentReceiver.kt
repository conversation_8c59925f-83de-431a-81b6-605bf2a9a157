package com.yupao.wxcollect.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import com.yupao.keepalive.KeepAliveFloatWindow
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.db.KVHelper.isClashTipsDb
import com.yupao.wxcollect.util.NetworkUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 网络连接状态监听器，支持网络断开时显示悬浮窗提示
 *
 * <p>创建时间：2024/5/24/024</p>
 *
 * <AUTHOR>
 */
class NetworkConnectionIntentReceiver(
    private val onNetworkConnected: () -> Unit = {},
    private val onNetworkDisconnected: () -> Unit = {},
) : BroadcastReceiver() {

    companion object {
        private const val TAG = "NetworkConnection"
        private const val action = ConnectivityManager.CONNECTIVITY_ACTION

        // 网络断开提示显示时长（毫秒）
        private const val NETWORK_DISCONNECT_SHOW_DURATION = 5000L
    }

    private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())
    private var disconnectJob: Job? = null

    override fun onReceive(p0: Context?, p1: Intent?) {
        if (p1?.action != action) {
            return
        }
        val connected = NetworkUtil.isNetworkConnected(App.getContext())
        YLog.i(TAG, "网络状态变化: $connected")

        if (connected) {
            handleNetworkConnected()
        } else {
            handleNetworkDisconnected()
        }
    }

    /**
     * 处理网络连接
     */
    private fun handleNetworkConnected() {
        YLog.i(TAG, "网络已连接")

        // 取消断网提示任务
        disconnectJob?.cancel()
        
        // 执行连接回调
        onNetworkConnected()
    }

    /**
     * 处理网络断开
     */
    private fun handleNetworkDisconnected() {
        YLog.w(TAG, "网络已断开，显示悬浮窗提示")

        // 取消之前的任务
        disconnectJob?.cancel()

        // 显示断网提示悬浮窗
        coroutineScope.launch {
            try {
                networkFloatWindow.showNetworkDisconnectFloat()

                // 延迟后自动隐藏
                delay(NETWORK_DISCONNECT_SHOW_DURATION)
                networkFloatWindow.dismissFloatWindow()

            } catch (e: Exception) {
                YLog.printException(TAG, e, "显示网络断开悬浮窗失败")
            }
        }.also { disconnectJob = it }

        // 执行断开回调
        onNetworkDisconnected()
    }

    fun register(context: Context) {
        runCatching {
            context.registerReceiver(this, IntentFilter(action))
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun unregister(context: Context) {
        runCatching {
            context.unregisterReceiver(this)
        }.onFailure {
            it.printStackTrace()
        }

        // 清理资源
        disconnectJob?.cancel()
        networkFloatWindow.dismissFloatWindow()
    }
}