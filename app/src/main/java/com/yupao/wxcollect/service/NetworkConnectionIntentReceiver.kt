package com.yupao.wxcollect.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import com.yupao.keepalive.KeepAliveFloatWindow
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.db.KVHelper.isClashTipsDb
import com.yupao.wxcollect.util.NetworkUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 网络连接状态监听器，支持网络断开时显示悬浮窗提示
 *
 * <p>创建时间：2024/5/24/024</p>
 *
 * <AUTHOR>
 */
class NetworkConnectionIntentReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "NetworkConnection"
        private const val action = ConnectivityManager.CONNECTIVITY_ACTION
    }


    override fun onReceive(p0: Context?, p1: Intent?) {
        if (p1?.action != action) {
            return
        }
        val connected = NetworkUtil.isNetworkConnected(App.getContext())
        YLog.i(TAG, "网络状态变化: $connected")
        if (connected) {
            SystemStatusManager.removeStatusMessage(
                SystemStatusManager.StatusType.NETWORK,
            )
        } else {
            SystemStatusManager.addStatusMessage(
                SystemStatusManager.StatusType.NETWORK,
                "网络不佳，请检查网络连接",
                priority = 1
            )
        }
    }

    fun register(context: Context) {
        kotlin.runCatching {
            context.registerReceiver(this, IntentFilter(action))
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun unregister(context: Context) {
        kotlin.runCatching {
            context.unregisterReceiver(this)
        }.onFailure {
            it.printStackTrace()
        }
    }
}