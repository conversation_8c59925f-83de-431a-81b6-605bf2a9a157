package com.yupao.wxcollect.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import com.yupao.keepalive.KeepAliveFloatWindow
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.db.KVHelper.isClashTipsDb
import com.yupao.wxcollect.util.NetworkUtil
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/24/024</p>
 *
 * <AUTHOR>
 */
class NetworkConnectionIntentReceiver(
    private val onNetworkConnected: () -> Unit = {},
) : BroadcastReceiver() {

    companion object {
        private const val action = ConnectivityManager.CONNECTIVITY_ACTION
    }

    override fun onReceive(p0: Context?, p1: Intent?) {
        if (p1?.action != action) {
            return
        }
        val connected = NetworkUtil.isNetworkConnected(App.getContext())
        YLog.i("NetworkConnection", "onChanged: $connected")
        if (connected) {
            this.onNetworkConnected()
        }else{
          
        }
    }

    fun register(context: Context) {
        runCatching {
            context.registerReceiver(this, IntentFilter(action))
        }.onFailure {
            it.printStackTrace()
        }
    }

    fun unregister(context: Context) {
        runCatching {
            context.unregisterReceiver(this)
        }.onFailure {
            it.printStackTrace()
        }
    }
}