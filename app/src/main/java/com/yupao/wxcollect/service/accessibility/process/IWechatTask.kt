package com.yupao.wxcollect.service.accessibility.process

import android.view.accessibility.AccessibilityEvent
import com.yupao.wxcollect.service.accessibility.filter.IPageHandler

/**
 * <AUTHOR>
 * @Date 2023/4/28/028 10:40
 * @Description
 */
interface IWechatTask {

    companion object {
        const val ResolverActivity = "com.android.internal.app.ResolverActivity"

        const val wechatPackageName = "com.tencent.mm"

        const val wechatSplashActivityCls = "com.tencent.mm.app.WeChatSplashActivity"

        const val wechatSwitchAccountCls = "com.tencent.mm.plugin.setting.ui.setting.SettingsSwitchAccountUI"

        const val wechatLauncherUI = "com.tencent.mm.ui.LauncherUI"

        const val wechatSettingUI = "com.tencent.mm.plugin.setting.ui.setting.SettingsUI"

        const val loginUI = "com.tencent.mm.plugin.account.ui.LoginPasswordUI"

        const val mobileInputUI = "com.tencent.mm.plugin.account.ui.MobileInputUI"

        const val fakeSwitchAccountUI = "com.tencent.mm.plugin.setting.ui.setting.FakeSwitchAccountUI"
        // 登录弹窗
        const val loginDialog = "wr3.l0"
        // 如何切换帐号弹窗
        const val howSwitchDialog = "wr3.g"
        // 载入数据库
        const val loadingDataDialog = "wr3.i0"

        const val wechatDialogClass = "wechat_dialog"

        /**
         * 是否微信弹窗
         */
        fun isWechatDialogClass(event: AccessibilityEvent?): Boolean {
            event ?: return false
            val packageName = event.packageName
            if (packageName != wechatPackageName) {
                return false
            }
            val className = event.className
            if (className.isNullOrEmpty()) {
                return false
            }
            return true
        }
    }

    fun getPageHandler(): MutableList<IPageHandler>

}