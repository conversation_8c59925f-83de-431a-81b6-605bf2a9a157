package com.yupao.wxcollect.hook

import android.database.Cursor
import androidx.core.database.getStringOrNull
import com.rwz.hook.core.hook.BaseHookManager
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.ylog.YLog
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedHelpers
import okhttp3.internal.closeQuietly
import java.lang.reflect.Modifier

/**
 * hook ISQLiteDatabase，方便执行sql语句
 *
 * <p>创建时间：2023/12/23/023</p>
 *
 * <AUTHOR>
 */
object WechatDatabaseHook {

    private const val TAG = "WechatDatabaseHook"

    // com.tencent.mm.sdk.storage.ISQLiteDatabase, 实现类：b04.f@85dd619
    private var iSQLiteDatabase: Any? = null

    /**
     * 是否支持sql查询
     */
    fun isSupportQuery() = iSQLiteDatabase != null

    fun hook(hookManager: BaseHookManager) {
        val versionName = AndroidSystemUtil.getVersionName()
        val cls: Class<*>? = kotlin.runCatching {
            if (versionName >= "8.0.44") {
                hookManager.loadClass("com.tencent.mm.storage.m4", true)
            } else {
                hookManager.loadClass("com.tencent.mm.storage.g2", true)
            }
        }.getOrNull()
        // v7.0.20
        val hookCls = cls ?: hookManager.loadClass("com.tencent.mm.storage.ax")
        if (hookCls == null) {
            YLog.i(TAG, "hook: databaseCls hook failed, $versionName")
            return
        }
        // public g2(kt3.f fVar)
//        YLog.i(TAG, "hook: databaseCls = $databaseCls")
        // databaseCls method = public com.tencent.mm.storage.g2(boolean,int,kotlin.jvm.internal.f0,v04.th,java.util.List)
        hookCls.declaredConstructors.filter {
            Modifier.isPublic(it.modifiers)
        }.forEach {
            val parameterTypes = it.parameterTypes
            YLog.i(TAG, "hook: databaseCls method = $it")
            kotlin.runCatching {
                hookConstructor(hookCls, hookManager, parameterTypes)
            }.onFailure {
                YLog.printException(TAG, it)
            }
        }
        YLog.i(TAG, "hook: databaseCls hook succeed")
    }

    private fun hookConstructor(hookCls: Class<*>, hookManager: BaseHookManager, parameterTypes: Array<Class<*>>) {
        //java.lang.Exception: Stack trace
        //	at java.lang.Thread.dumpStack(Thread.java:1529)
        //	at com.yupao.wxmsg.monitor.message.WechatDatabaseHook$hook$1.afterHookedMethod(WechatDatabaseHook.kt:44)
        //	at de.robv.android.xposed.MethodHooker.handleHookedMethod(MethodHooker.java:94)
        //	at EdHooker_fb5c20ce32a62c8c37ed387a4005d0c4932b69e0.hook(Unknown Source:48)
        //	at com.tencent.mm.plugin.messenger.foundation.t.BO0(SourceFile:26)
        //	at com.tencent.mm.plugin.messenger.foundation.t.mw(Unknown Source:0)
        //	at of0.c.v(Unknown Source:8)
        //	at com.tencent.mm.ui.conversation.MainUI.onCreate(SourceFile:64)
        //	at androidx.fragment.app.Fragment.performCreate(SourceFile:27)
        //	at androidx.fragment.app.z.e(SourceFile:31)
        //	at androidx.fragment.app.z.k(SourceFile:139)
        //	at androidx.fragment.app.FragmentManager.executeOpsTogether(SourceFile:548)
        //	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(SourceFile:85)
        //	at androidx.fragment.app.FragmentManager.execSingleAction(SourceFile:32)
        //	at androidx.fragment.app.a.h(SourceFile:11)
        //	at androidx.fragment.app.a0.finishUpdate(SourceFile:13)
        //	at com.tencent.mm.ui.mogic.WxViewPager.populate(SourceFile:50)
        //	at com.tencent.mm.ui.mogic.WxViewPager.populate(SourceFile:1)
        //	at com.tencent.mm.ui.mogic.WxViewPager.onMeasure(SourceFile:190)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
        //	at androidx.appcompat.widget.ContentFrameLayout.onMeasure(SourceFile:155)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at androidx.appcompat.widget.ActionBarOverlayLayout.onMeasure(SourceFile:204)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1552)
        //	at android.widget.LinearLayout.measureVertical(LinearLayout.java:842)
        //	at android.widget.LinearLayout.onMeasure(LinearLayout.java:721)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7049)
        //	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
        //	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:767)
        //	at android.view.View.measure(View.java:26077)
        //	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:3578)
        //	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:2380)
        //	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:2657)
        //	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:2078)
        //	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:8558)
        //	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1077)
        //	at android.view.Choreographer.doCallbacks(Choreographer.java:897)
        //	at android.view.Choreographer.doFrame(Choreographer.java:826)
        //	at android.view.Choreographer$FrameHandler.handleMessage(Choreographer.java:1004)
        //	at android.os.Handler.dispatchMessage(Handler.java:106)
        //	at android.os.Looper.loop(Looper.java:237)
        //	at android.app.ActivityThread.main(ActivityThread.java:8126)
        //	at java.lang.reflect.Method.invoke(Native Method)
        //	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
        //	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)

        XposedHelpers.findAndHookConstructor(
            hookCls,
            *parameterTypes,
            object: XC_MethodHook() {
                @Throws(Throwable::class)
                override fun afterHookedMethod(param: MethodHookParam) {
                    super.afterHookedMethod(param)
                    val args = param.args
                    val database = args.firstOrNull()
                    YLog.i(TAG, "${parameterTypes.contentToString()} ISQLiteDatabase prepared: $database")
                    // b04.f@85dd619
                    if (database != null) {
                        iSQLiteDatabase = database
                    }
                }
            })
    }

    private fun isSupperClass(obj: Class<*>?, className: String, maxCount: Int): Boolean {
        if (obj == null) {
            return false
        }
        if (obj.name == className) {
            return true
        }
        if (maxCount < 0) {
            return false
        }
        return isSupperClass(obj.superclass, className, maxCount - 1)
    }

    /**
     * 执行SQL
     *     public static final int CURSOR_ASYNC = 3;
     *     public static final int CURSOR_DEFAULT = 0;
     *     public static final int CURSOR_HEAP = 4;
     *     public static final int CURSOR_IMMEDIATE = 2;
     *     public static final int CURSOR_WINDOWED = 1;
     *     long beginTransaction(long j15);
     *     void close();
     *     int delete(String str, String str2, String[] strArr);
     *     int endTransaction(long j15);
     *     boolean execSQL(String str, String str2);
     *     long insert(String str, String str2, ContentValues contentValues);
     *     long insertOrThrow(String str, String str2, ContentValues contentValues);
     *     boolean isClose();
     *     Cursor query(String str, String[] strArr, String str2, String[] strArr2, String str3, String str4, String str5);
     *     Cursor query(String str, String[] strArr, String str2, String[] strArr2, String str3, String str4, String str5, int i15);
     *     Cursor query(String str, String[] strArr, String str2, String[] strArr2, String str3, String str4, String str5, int i15, String str6);
     *     Cursor rawQuery(String str, String[] strArr);
     *     Cursor rawQuery(String str, String[] strArr, int i15);
     *     Cursor rawQueryWithCancel(String str, String[] strArr);
     *     long replace(String str, String str2, ContentValues contentValues);
     *     int update(String str, ContentValues contentValues, String str2, String[] strArr);
     *
     * @param sql
     */
    fun executeSql(sql: String?, strArr: List<String>? = null): Cursor? {
//        val isClose = isClose()
//        if (isClose) {
//            YLog.i(TAG, "executeSql: database closed, sql: $sql")
//            return null
//        }
        val database = iSQLiteDatabase ?: kotlin.run {
            YLog.i(TAG, "executeSql: iSQLiteDatabase is null")
            return null
        }
        return kotlin.runCatching {
            val methods = database.javaClass.declaredMethods
            val method = methods.find {
                val parameterTypes = it.parameterTypes
                val args = arrayOf(String::class.java, Array<String>::class.java, Int::class.javaPrimitiveType)
                // 判断参数一致 与 返回参数是否一致，该方式不会太精准
                parameterTypes.contentEquals(args) && it.returnType == Cursor::class.java
            }?.apply {
                this.isAccessible = true
            }
            if (method == null) {
                val methods = methods.joinToString("\n"){
                    it.toString()
                }
                YLog.i(TAG, "executeSql: not found rawQuery method. $methods")
            }
            method?.invoke(database, sql, strArr?.toTypedArray(), 0) as? Cursor
        }.onFailure {
            it.printStackTrace()
        }.getOrNull()
    }

    fun executeSqlWithResult(sql: String?, strArr: List<String>? = null): List<Map<String, String?>>? {
        val cursor = executeSql(sql, strArr) ?: return null
        val result = mutableListOf<Map<String, String?>>()
        try {
            val columnCount2 = cursor.columnCount
            val count = cursor.count
            YLog.i(TAG, "executeSqlWithResult columnCount: $columnCount2, count = $count, sql: $sql")
            while (cursor.moveToNext()) {
                val columnCount = cursor.columnCount
                val map = mutableMapOf<String, String?>()
                for (i in 0 until columnCount) {
                    kotlin.runCatching {
                        val key = cursor.getColumnName(i)
                        val value = cursor.getStringOrNull(i)
                        map[key] = value
                    }.onFailure {
                        it.printStackTrace()
                    }
                }
                if (map.isNotEmpty()) {
                    result.add(map)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor.closeQuietly()
        }
        return result
    }

    private fun getFiledValue(cls: Class<*>, obj: Any?, fieldName: String): Any? {
        return kotlin.runCatching {
            cls.getDeclaredField(fieldName).apply {
                isAccessible = true
            }.get(obj)
        }.getOrNull()
    }

    private fun invokeMethod(cls: Class<*>, obj: Any?, method: String): Any? {
        return kotlin.runCatching {
            cls.getDeclaredMethod(method).apply {
                isAccessible = true
            }.invoke(obj)
        }.getOrNull()
    }

    fun isClose(): Boolean {
        val database = iSQLiteDatabase ?: return true
        return (invokeMethod(database.javaClass, database, "isClose") as? Boolean) ?: true
    }
}