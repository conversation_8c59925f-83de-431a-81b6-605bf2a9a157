package com.yupao.wxcollect.hook.task.friend

import com.yupao.wxcollect.hook.ISqlHookTask
import com.yupao.wxcollect.hook.utils.SqlConstant
import com.yupao.wxcollect.hook.utils.SqlUtil
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/12/22/022</p>
 *
 * <AUTHOR>
 */
class MsgInfoHookTask (
    override val sqlType: String? = SqlConstant.INSERT,
    override val tableList: List<String>? = listOf("fmessage_msginfo"),
    private val onReceivedData: (String, Map<String, String?>) -> Unit,
) : ISqlHookTask {

    override fun onHook(
        table: String,
        sqlType: String,
        sql: String,
        argsValue: Array<*>?
    ): Boolean {
        // D  hookSqlExecute executeForLastInsertedRowId sql: INSERT INTO fmessage_msginfo(isSend,talker,encryptTalker,type,msgContent,svrId,chatroomName,createTime) VALUES (?,?,?,?,?,?,?,?), argsValue: [0, wxid_ttkg1apkiy2o21, v3_020b3826fd03010000000000d9dd2c1194d9a6000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d20cef7e76e48719dae72dd7225da91fc70c0136bec0a0dc81c66c66fd90cdf42b4d287c3d9534354e06f69d0d625e14e@stranger, 1, <msg fromusername="wxid_ttkg1apkiy2o21" encryptusername="v3_020b3826fd03010000000000d9dd2c1194d9a6000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d20cef7e76e48719dae72dd7225da91fc70c0136bec0a0dc81c66c66fd90cdf42b4d287c3d9534354e06f69d0d625e14e@stranger" fromnickname="逝风" content="我是逝风" fullpy="shifeng" shortpy="SF" imagestatus="3" scene="6" country="CN" province="Sichuan" city="Chengdu" sign="醉后不知天在水，满船清梦压星河" percard="1" sex="1" alias="rwz657026189" weibo="" albumflag="0" albumstyle="0" albumbgimgid="" snsflag="273" snsbgimgid="http://mmsns.qpic.cn/mmsns/HhRfwkaJkMsAKicpO73hoAwf6WDS5qN61ibAGKO6CwGb3EPSu139M1wQYUuTI3lJZSPMhjZe6TbMA/0" snsbgobjectid="12209609962638225569" mhash="b647d138933a57d1c10522fc0e89492d" mfullhash="b647d138933a57d1c10522fc0e89492d" bigheadimgurl="http://wx.qlogo.cn/mmhead/ver_1/tNSoHKzo7bFHbTc7K4zBSbib6otoJIIDsgDmhzXVF6ic6jKHzxraMOcBhwNOS0gG5wAQRb8aECGsvwyhrz1C7NSQtK5cTVV1QtBdysoBLIywY/0" smallheadimgurl="http://wx.qlogo.cn/mmhead/ver_1/tNSoHKzo7bFHbTc7K4zBSbib6otoJIIDsgDmhzXVF6ic6jKHzxraMOcBhwNOS0gG5wAQRb8aECGsvwyhrz1C7NSQtK5cTVV1QtBdysoBLIywY/96" ticket="v4_000b708f0b040000010000000000218f537776c8d30999f18d0985651000000050ded0b020927e3c97896a09d47e6e9e356b56a1fa7cebb9910b4cfe0d33f0eb85df3b3c9ff8ded4b22c4dc12ee240e9df6df39a1822ddd044ae566df0f832765da1b9866f776ec940c63c5722da86511e1c30bc4891e15b004063623a7fdf23cb23cf6161ff5861521ff8a20c487eb7618e7a025ca762117f@stranger" opcode="2" googlecontact="" qrticket="" chatroomusername="" sourceusername="" sourcenickname="" sharecardusername="" sharecardnickname="" cardversion="" extflag="0"><brandlist count="0" ver="797009097"></brandlist></msg>, 1662682589595661296, , 1703217550000]
        // D  hookSqlExecute executeForLastInsertedRowId sql: INSERT INTO fmessage_conversation(contentNickname,contentFromUsername,lastModifiedTime,recvFmsgType,fmsgType,addScene,talker,contentPhoneNumMD5,encryptTalker,fmsgIsSend,fmsgIsHasShowSelfQRCode,isNew,state,contentVerifyContent,fmsgSysRowId,fmsgContent,displayName,contentFullPhoneNumMD5) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), argsValue: [逝风, wxid_ttkg1apkiy2o21, 1703217550311, 1, 1, 6, wxid_ttkg1apkiy2o21, , v3_020b3826fd03010000000000d9dd2c1194d9a6000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d20cef7e76e48719dae72dd7225da91fc70c0136bec0a0dc81c66c66fd90cdf42b4d287c3d9534354e06f69d0d625e14e@stranger, 0, 0, 1, 0, 我是逝风, 7, <msg fromusername="wxid_ttkg1apkiy2o21" encryptusername="v3_020b3826fd03010000000000d9dd2c1194d9a6000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d20cef7e76e48719dae72dd7225da91fc70c0136bec0a0dc81c66c66fd90cdf42b4d287c3d9534354e06f69d0d625e14e@stranger" fromnickname="逝风" content="我是逝风" fullpy="shifeng" shortpy="SF" imagestatus="3" scene="6" country="CN" province="Sichuan" city="Chengdu" sign="醉后不知天在水，满船清梦压星河" percard="1" sex="1" alias="rwz657026189" weibo="" albumflag="0" albumstyle="0" albumbgimgid="" snsflag="273" snsbgimgid="http://mmsns.qpic.cn/mmsns/HhRfwkaJkMsAKicpO73hoAwf6WDS5qN61ibAGKO6CwGb3EPSu139M1wQYUuTI3lJZSPMhjZe6TbMA/0" snsbgobjectid="12209609962638225569" mhash="b647d138933a57d1c10522fc0e89492d" mfullhash="b647d138933a57d1c10522fc0e89492d" bigheadimgurl="http://wx.qlogo.cn/mmhead/ver_1/tNSoHKzo7bFHbTc7K4zBSbib6otoJIIDsgDmhzXVF6ic6jKHzxraMOcBhwNOS0gG5wAQRb8aECGsvwyhrz1C7NSQtK5cTVV1QtBdysoBLIywY/0" smallheadimgurl="http://wx.qlogo.cn/mmhead/ver_1/tNSoHKzo7bFHbTc7K4zBSbib6otoJIIDsgDmhzXVF6ic6jKHzxraMOcBhwNOS0gG5wAQRb8aECGsvwyhrz1C7NSQtK5cTVV1QtBdysoBLIywY/96" ticket="v4_000b708f0b040000010000000000218f537776c8d30999f18d0985651000000050ded0b020927e3c97896a09d47e6e9e356b56a1fa7cebb9910b4cfe0d33f0eb85df3b3c9ff8ded4b22c4dc12ee240e9df6df39a1822ddd044ae566df0f832765da1b9866f776ec940c63c5722da86511e1c30bc4891e15b004063623a7fdf23cb23cf6161ff5861521ff8a20c487eb7618e7a025ca762117f@stranger" opcode="2" googlecontact="" qrticket="" chatroomusername="" sourceusername="" sourcenickname="" sharecardusername="" sharecardnickname="" cardversion="" extflag="0"><brandlist count="0" ver="797009097"></brandlist></msg>, 逝风, ]
        val keyList = SqlUtil.getInsertSqlKeyList(sql)
        val type = SqlUtil.getArgValue("type", keyList, argsValue)
        if (type == "1") {
            val talker = SqlUtil.getArgValue("talker", keyList, argsValue)
            if (!talker.isNullOrEmpty()) {
                val isSend = SqlUtil.getArgValue("isSend", keyList, argsValue)
                val createTime = SqlUtil.getArgValue("createTime", keyList, argsValue)
                val map = mapOf(
                    "talker" to talker,
                    "isSend" to isSend,
                    "createTime" to createTime,
                )
                onReceivedData.invoke(talker, map)
            } else {
                YLog.i("MsgInfoHookTask", "onHook sql: $sql, argsValue: ${argsValue.contentToString()}")
            }
        } else {
            YLog.i("MsgInfoHookTask", "onHook2 sql: $sql, argsValue: ${argsValue.contentToString()}")
        }
        return true
    }
}

fun main() {
    val sql = "INSERT INTO fmessage_msginfo(isSend,talker,encryptTalker,type,msgContent,svrId,chatroomName,createTime) VALUES (?,?,?,?,?,?,?,?)"
    val args = "0, wxid_7pvgwvchx3m222, v3_020b3826fd03010000000000a5c001b6434bd5000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d1be398099d2f2ed07edaabde40aa7b98f6b1644d31cb3dc8ed016d9b9febf1511a6ac746763015902f106ab32127c9df@stranger, 1, 1, 7402279579882571473, , 1703754943000".split(", ").toTypedArray()
    val task = MsgInfoHookTask() { key, map ->
        println("$key, $map")
    }

    task.onHook(
        "fmessage_msginfo", SqlConstant.INSERT, sql, args
    )
}