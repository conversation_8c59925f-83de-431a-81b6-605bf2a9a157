package com.yupao.wxcollect.hook

import android.util.Log
import com.rwz.hook.core.hook.BaseHookManager
import com.yupao.wxcollect.hook.task.friend.ApplyToAddFriendsHookTask
import com.yupao.wxcollect.hook.utils.SqlUtil
import com.yupao.ylog.YLog
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedHelpers

/**
 * hook sql语句执行（不包含所有，待穷举）
 *
 * <p>创建时间：2023/12/22/022</p>
 *
 * <AUTHOR>
 */
class WechatSqlHook(
    private val hookManager: BaseHookManager?
) {
    companion object {
        private const val TAG = "WechatSqlHook"

    }

    private val sqlTaskList = mutableListOf<ISqlHookTask>()

    private var hookTask: IHookTask? = null

    fun setHookTask(hookTask: IHookTask?) {
        this.hookTask = hookTask
    }

    fun register(task: ISqlHookTask) {
        sqlTaskList.add(task)
    }

    fun register(task: List<ISqlHookTask>) {
        sqlTaskList.addAll(task)
    }

    fun unregister(task: ISqlHookTask) {
        sqlTaskList.remove(task)
    }

    fun hook() {
        // SQLiteDatabase …… SQLiteConnection执行的语句
        hookSQLiteConnection()
    }

    private fun hookSQLiteConnection() {
        // public long executeForLong(String str, Object[] objArr, CancellationSignal cancellationSignal) {
        // public String executeForString(String str, Object[] objArr, CancellationSignal cancellationSignal) {
        // public int executeForChangedRowCount(String str, Object[] objArr, CancellationSignal cancellationSignal) {
        // public long executeForLastInsertedRowId(String str, Object[] objArr, CancellationSignal cancellationSignal)
        // public void execute(String str, Object[] objArr, CancellationSignal cancellationSignal) {
        hookSqlExecute("executeForLong")
        hookSqlExecute("executeForString")
        hookSqlExecute("executeForChangedRowCount")
        hookSqlExecute("executeForLastInsertedRowId")
        hookSqlExecute("execute")
        YLog.i(TAG, "hookSQLiteConnection: succeed")
    }

    private fun hookSqlExecute(methodName: String) {
        hookManager ?: return
        val hookCls = hookManager.loadClass("com.tencent.wcdb.database.SQLiteConnection")
        val signalCls: Class<*> = hookManager.loadClass("com.tencent.wcdb.support.CancellationSignal")
        XposedHelpers.findAndHookMethod(
            hookCls,
            methodName,
            String::class.java,
            Array<Any>::class.java,
            signalCls,
            object: XC_MethodHook() {
                @Throws(Throwable::class)
                override fun afterHookedMethod(param: MethodHookParam) {
                    super.afterHookedMethod(param)
                    val args = param.args
                    val sql = args.getOrNull(0)?.toString()
                    val argsValue = args.getOrNull(1) as? Array<*>
                    hookTask?.onHook(sql, argsValue, param.thisObject)
                    onHooked(sql, argsValue)
                }
            })
    }

    fun onHooked(sql: String?, argsValue: Array<*>?) {
        // SELECT version FROM PriorityConfig WHERE type=?;, argsValue: [16777219]
        // INSERT INTO FTS5IndexWeApp (content) VALUES (?);, argsValue: [鱼泡网招聘求职找工作]
        // INSERT OR REPLACE INTO userinfo VALUES (?,?,?);, argsValue: [8196, 2, 0]
        // UPDATE FTS5MetaContact SET timestamp=? WHERE aux_index=?, argsValue: [1698380355461, wxid_l8yw077lc2yk22]
        // DELETE FROM FinderConsumeNewXmlData WHERE type = 1 AND createTime < 1702965678006, argsValue: null
        sql ?: return
        val splitList = sql.split(" ")
        // 类型
        val type = splitList.firstOrNull()?.lowercase() ?: return
        val taskList = sqlTaskList.filter {
            it.sqlType == null || it.sqlType == type
        }
        if (taskList.isEmpty()) {
            return
        }
        val table = SqlUtil.getSqlTable(type, splitList) ?: return
        taskList.forEach {
            if (it.tableList?.contains(table) != false) {
                val result = kotlin.runCatching {
                    it.onHook(table, type, sql, argsValue)
                }.getOrDefault(false)
                if (result) {
                    return
                }
            }
        }
    }
}

interface ISqlHookTask {
    val sqlType: String?

    val tableList: List<String>?

    /**
     * 当hook sql调用时
     *
     * @param table 表名
     * @param sqlType sql类型
     * @param sql sql语句
     * @param argsValue sql参数
     * @return true: 阻断后续ISqlHookTask执行
     */
    fun onHook(table: String, sqlType: String, sql: String, argsValue: Array<*>?): Boolean
}

interface IHookTask {
    fun onHook(sql: String?, argsValue: Array<*>?, thisObject: Any?)
}

fun main() {
    val hook = WechatSqlHook(null)
    val sql = "INSERT INTO fmessage_msginfo(isSend,talker,encryptTalker,type,msgContent,svrId,chatroomName,createTime) VALUES(?,?,?,?,?,?,?,?)"
    val values = "0, wxid_ttkg1apkiy2o21, v3_020b3826fd03010000000000d9dd2c1194d9a6000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d20cef7e76e48719dae72dd7225da91fc70c0136bec0a0dc81c66c66fd90cdf42b4d287c3d9534354e06f69d0d625e14e@stranger, 1, 1, 1844114404623087441, , 1703231887000".split(", ").toTypedArray()
    hook.register(ApplyToAddFriendsHookTask(null).getSqlHookTaskList())
    hook.onHooked(sql, values)

    val sql2 = "UPDATE fmessage_conversation SET contentNickname=?,contentFromUsername=?,lastModifiedTime=?,recvFmsgType=?,fmsgType=?,addScene=?,talker=?,contentPhoneNumMD5=?,encryptTalker=?,fmsgIsSend=?,fmsgIsHasShowSelfQRCode=?,isNew=?,rowid=?,state=?,contentVerifyContent=?,fmsgSysRowId=?,fmsgContent=?,displayName=?,contentFullPhoneNumMD5=? WHERE talker= ?"
    val values2 = "逝风, wxid_ttkg1apkiy2o21, 1703231887914, 1, 1, 6, wxid_ttkg1apkiy2o21, , v3_020b3826fd03010000000000d9dd2c1194d9a6000000501ea9a3dba12f95f6b60a0536a1adb6a1e8246f2b4932e0dfb1aa9b1d20cef7e76e48719dae72dd7225da91fc70c0136bec0a0dc81c66c66fd90cdf42b4d287c3d9534354e06f69d0d625e14e@stranger, 0, 0, 1, 7, 0, 我是逝风, 8, 1, 逝风, , wxid_ttkg1apkiy2o21".split(", ").toTypedArray()
    hook.onHooked(sql2, values2)
}