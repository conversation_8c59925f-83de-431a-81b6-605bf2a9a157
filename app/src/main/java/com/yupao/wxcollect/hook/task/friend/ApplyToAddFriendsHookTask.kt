package com.yupao.wxcollect.hook.task.friend

import androidx.core.os.bundleOf
import com.rwz.hook.core.hook.ITransmit
import com.rwz.hook.utils.ToastUtil
import com.yupao.wxcollect.service.WechatUserManager
import com.yupao.wxcollect.service.procedure.entity.request.ApplyToAddFriendsParamsModel
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.util.SpUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Hashtable

/**
 * 申请添加好友任务, 包括主动和被动添加好友
 *
 * <p>创建时间：2023/12/22/022</p>
 *
 * <AUTHOR>
 */
class ApplyToAddFriendsHookTask(private val transmit: ITransmit?) {

    companion object {
        private const val TAG = "ApplyToAddFriendsHook"
    }

    private val msgInfoHookTask = MsgInfoHookTask(onReceivedData = this::onReceivedData)

    private val conversationHookTask = ConversationHookTask(onReceivedData = this::onReceivedData)

    private val cacheData = Hashtable<String, MutableMap<String, String?>>()

    fun getSqlHookTaskList() = listOf(msgInfoHookTask, conversationHookTask)

    private val resp by lazy { ReportRepository(null) }

    private fun onReceivedData(talker: String, map: Map<String, String?>) {
        val values = cacheData.getOrPut(talker) {
            mutableMapOf()
        }
        values.putAll(map)
        val createTime = values["createTime"]
        val lastModifiedTime = values["lastModifiedTime"]
        if (!createTime.isNullOrEmpty() && !lastModifiedTime.isNullOrEmpty()) {
            values.remove(talker)
            YLog.i(TAG, "upload: $values")
            val bundle = bundleOf()
            values.forEach {
                bundle.putString(it.key, it.value)
            }
            val userEntity = WechatUserManager.getUserEntityByOtherProcess(SpUtil.isMainType())
            val wechatId = userEntity?.username
            val wechatName = userEntity?.nickname
            val alias = userEntity?.alias
//            val wechatId = WechatFetchWechatIdHook.fetchWechatId()
//            val wechatName = WechatFetchWechatIdHook.fetchWechatName()
//            val alias = WechatFetchWechatIdHook.fetchAlias()
            report(wechatId, wechatName, alias, values)
            cacheData.remove(talker)
//            bundle.putString(ITransmitRequest.KEY_CURR_WECHAT_ID, wechatId)
//            transmit?.sendMessage(ITransmitRequest.HOOK_APPLY_TO_ADD_FRIENDS, bundle)
        } else {
            YLog.i(TAG, "upload createTime: $createTime, lastModifiedTime: $lastModifiedTime")
        }
    }

    private fun report(
        wechatId: String?,
        wechatName: String?,
        alias: String?,
        data: Map<String, String?>
    ) {
        YLog.i(TAG,
            "report: wechatId = $wechatId, wechatName = $wechatName, alias = $alias, data = $data"
        )
        if (wechatId.isNullOrEmpty()) {
            return
        }
//        val entity = ApplyToAddFriendsQueryEntity(
//            talker = data.get("talker"),
//            displayName = data.get("displayName"),
//            lastModifiedTime = data.get("lastModifiedTime"),
//            isSend = data.get("isSend"),
//            state = data.get("state"),
//            addScene = data.get("addScene"),
//            createTime = data.get("createTime"),
//        )
        GlobalScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val model = ApplyToAddFriendsParamsModel(
                    wxId = wechatId,
                    targetWxId = data["talker"],
                    nickname = data["displayName"],
                    addScene = data["addScene"],
                    state = data["state"],
                    applyTime = data["createTime"],
                    lastModifiedTime = data["lastModifiedTime"],
                    isSend = data["isSend"],
                    phoneNumber = SpUtil.getString(SpUtil.KEY_PHONE_NUM),
                    systemType = if (SpUtil.isSubType()) "4" else "3"
                )
                YLog.i(TAG, "start report: $wechatName, model: $model")
                val resp = resp.applyToAddFriends(model)
                if (resp.isOK()) {
                    ToastUtil.getInstance().showShortSingle("好友申请上报成功")
                    SpUtil.putString(SpUtil.KEY_APPLY_FRIEND_LATELY, System.currentTimeMillis().toString())
                    YLog.i(TAG, "report: succeed")
                } else {
                    YLog.i(TAG, "report: failed code: ${resp.getCode()}, msg: ${resp.getMsg()}")
                    ToastUtil.getInstance().showShortSingle(resp.getMsg())
                }
            }.onFailure {
                YLog.printException(TAG, it)
                it.printStackTrace()
            }
        }
    }
}