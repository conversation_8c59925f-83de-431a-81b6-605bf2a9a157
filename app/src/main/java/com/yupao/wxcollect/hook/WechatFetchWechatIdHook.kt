package com.yupao.wxcollect.hook

import com.rwz.hook.core.hook.BaseHookManager
import com.yupao.wxcollect.hook.entity.WechatIdEntity
import com.yupao.ylog.YLog
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedHelpers
import java.util.Arrays

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/12/18/018</p>
 *
 * <AUTHOR>
 */
object WechatFetchWechatIdHook {

    private const val TAG = "WechatFetchWechatIdHook"

    private val wechatIdEntity by lazy { WechatIdEntity() }

    @Volatile
    // com.tencent.mm.storage.t1
    private var wechatStorage: Any? = null

    fun hook(hookManager: BaseHookManager) {
//        hookQuery()
        hookTencentStorage(hookManager)
        YLog.i(TAG, "hookTencentStorage: succeed")
    }

    private fun hookTencentStorage(hookManager: BaseHookManager) {
        val t1Cls = hookManager.loadClass("com.tencent.mm.storage.t1")
        XposedHelpers.findAndHookMethod(
            t1Cls,
            "z",
            Int::class.javaPrimitiveType,
            Any::class.java,
            object: XC_MethodHook() {
                @Throws(Throwable::class)
                override fun afterHookedMethod(param: MethodHookParam) {
                    super.afterHookedMethod(param)
                    val args = param.args
                    args.forEachIndexed { index, any ->
                        if (any is Array<*>) {
                            YLog.i(TAG, "hookTencentStorage arg$index : ${getArgs(any)}")
                        } else {
                            YLog.i(TAG, "hookTencentStorage arg$index : ${toString(any)}")
                        }
                    }
                    wechatStorage = param.thisObject
                }
            })
    }



    fun fetchWechatId(): String? {
        val wechatId = fetchUserInfo(2)
        if (!wechatId.isNullOrEmpty()) {
            return wechatId
        }
        val wechatName = fetchWechatName()
        if (wechatName.isNullOrEmpty()) {
            return null
        }
        val sql = "select username from rcontact where nickname='$wechatName' and verifyFlag = 0 and type != 4"
        return WechatDatabaseHook.executeSqlWithResult(sql)
            ?.firstOrNull()
            ?.get("username")
    }

    fun fetchWechatName(): String? {
        return fetchUserInfo(4)
    }

    fun fetchAlias(): String? {
        val alias = fetchUserInfo(42)
        if (!alias.isNullOrEmpty()) {
            return alias
        }
        val wechatName = fetchWechatName()
        if (wechatName.isNullOrEmpty()) {
            return null
        }
        val sql = "select alias from rcontact where nickname='$wechatName' and verifyFlag = 0 and type != 4"
        return WechatDatabaseHook.executeSqlWithResult(sql)
            ?.firstOrNull()
            ?.get("alias")
    }

    private fun queryUserInfo(key: Int): String? {
        return WechatDatabaseHook.executeSqlWithResult("select value from userinfo where id=$key")
            ?.firstOrNull()
            ?.get("value")
    }

    private fun fetchUserInfo(key: Int): String? {
        val storage = wechatStorage ?: kotlin.run {
            YLog.i(TAG, "fetchWechatId: not found wechatStorage")
            return queryUserInfo(key)
        }
        var value: String? = null
        try {
            value = storage.javaClass.getDeclaredMethod(
                "z",
                Int::class.javaPrimitiveType,
                Any::class.java
            ).apply {
                isAccessible = true
            }.invoke(storage, key, null)?.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (value.isNullOrEmpty()) {
            return queryUserInfo(key)
        }
        return value
    }

    fun fetchCacheWechatId(): String? {
        val currentTimeMillis = System.currentTimeMillis()
        val dt = currentTimeMillis - (wechatIdEntity.updateTime ?: 0)
        val cacheWechatId = wechatIdEntity.wechatId
        // 操过10s才会去更新
        if (dt > 10_000 || cacheWechatId.isNullOrEmpty()) {
            val wechatId = fetchWechatId()
            YLog.i(TAG, "fetchCacheWechatId: fetchWechatId: $wechatId")
            if (!wechatId.isNullOrEmpty()) {
                wechatIdEntity.wechatId = wechatId
                wechatIdEntity.updateTime = currentTimeMillis
                return wechatId
            }
        }
        return cacheWechatId
    }

    private fun getArgs(args: Array<*>?): String {
        if (args == null) {
            return ""
        }
        val sb = StringBuilder()
        for (arg in args) {
            if (arg is Array<*> && arg.isArrayOf<Any>()) {
                sb.append(",").append(getArgs(arg))
            } else {
                sb.append(",").append(toString(arg))
            }
        }
        return if (sb.isNotEmpty()) {
            "[" + sb.substring(1) + "]"
        } else {
            sb.toString()
        }
    }

    private fun toString(obj: Any?): String? {
        if (obj == null) {
            return "null"
        }
        if (obj is String || obj is Number || obj is Boolean) {
            return obj.toString()
        }
        if (obj is Set<*> || obj is List<*> || obj is Map<*, *>) {
            return obj.toString()
        }
        if (obj is Array<*> && obj.isArrayOf<String>()) {
            return Arrays.toString(obj as Array<String?>?)
        }
        if (obj is IntArray) {
            return Arrays.toString(obj as IntArray?)
        }
        if (obj is Array<*> && obj.isArrayOf<Long>()) {
            return Arrays.toString(obj as Array<Long?>?)
        }
        if (obj is BooleanArray) {
            return Arrays.toString(obj as BooleanArray?)
        }
        if (obj is FloatArray) {
            return Arrays.toString(obj as FloatArray?)
        }
        if (obj is DoubleArray) {
            return Arrays.toString(obj as DoubleArray?)
        }
        return if (obj is ByteArray) {
            Arrays.toString(obj as ByteArray?)
        } else obj.toString()
    }
}