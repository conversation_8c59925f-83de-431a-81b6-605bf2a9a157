package com.yupao.wxcollect.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.service.db.CollectTaskDao
import com.yupao.wxcollect.service.procedure.entity.db.CollectTaskLocalModel

/**
 * 采集任务相关数据库，跟[CommDatabase]隔离开，是因为改动较大，避免影响配置文件
 *
 * <p>创建时间：2024/8/7/007</p>
 *
 * <AUTHOR>
 */
@Database(
    entities = [CollectTaskLocalModel::class],
    version = 1,
    exportSchema = false
)
abstract class CollectDatabase: RoomDatabase() {

    abstract fun getCollectTaskDao(): CollectTaskDao

}

object CollectDatabaseModule {
    private var database: CollectDatabase? = null

    @Synchronized
    fun getDataBase(): CollectDatabase {
        return database ?: kotlin.run {
            Room.databaseBuilder(
                AndroidSystemUtil.getContext(),
                CollectDatabase::class.java, "collect_data"
            )
                .allowMainThreadQueries()
                .build()
                .apply {
                    database = this
                }
        }
    }
}