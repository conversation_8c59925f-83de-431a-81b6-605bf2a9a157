package com.yupao.wxcollect.database

import android.content.Context
import androidx.room.Room
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class CommonDatabaseModule {
    companion object {
        private var database: CommDatabase? = null

        @Synchronized
        fun getDataBase(context: Context): CommDatabase {
            if (database == null) {
                database = Room.databaseBuilder(
                    context,
                    CommDatabase::class.java, "common_data"
                )
                    .allowMainThreadQueries()
                    .build()
            }
            return database!!
        }
    }

    @Singleton
    @Provides
    fun provideCommonDatabase(@ApplicationContext applicationContext: Context): CommDatabase {
        return getDataBase(applicationContext)
    }
}