package com.yupao.wxcollect.database.upgrade

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/6/26/026 13:37
 * @Description 数据库版本2 -> 3
 * 新增公众号文章表
 */
object Migration2to3: Migration(2, 3) {

    override fun migrate(database: SupportSQLiteDatabase) {
        kotlin.runCatching {
            YLog.i("Migration2to3", "version: ${database.version}")
            database.execSQL("CREATE TABLE IF NOT EXISTS `official_article_report` (`id` INTEGER, `appType` TEXT, `wechatId` TEXT, `telNum` TEXT, `createTime` TEXT, `officialName` TEXT, `url` TEXT, `articleUnique` TEXT, `talker` TEXT, `reportCount` INTEGER, `reportTime` INTEGER, `reportFailMsg` TEXT, `remark` TEXT, PRIMARY KEY(`id`))")
            YLog.i("Migration2to3", "update succeed")
        }.exceptionOrNull()?.let {
            it.printStackTrace()
            YLog.printException("Migration2to3", it)
        }
    }
}