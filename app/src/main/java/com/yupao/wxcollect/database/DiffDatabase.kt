package com.yupao.wxcollect.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.contact.ContactDiffDao
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.contact.ContactDiffLocalModel
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.group.GroupDiffDao
import com.yupao.wxcollect.service.procedure.task.customize.diff.comm.provider.group.GroupDiffLocalModel

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 14:37
 * @Description
 */
@Database(
    entities = [
        GroupDiffLocalModel::class,
        ContactDiffLocalModel::class,
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(value = [Converters::class])
abstract class MainDiffDatabase: RoomDatabase() {

    abstract fun getGroupDiffDao(): GroupDiffDao

    abstract fun getContactDiffDao(): ContactDiffDao

}

@Database(
    entities = [
        GroupDiffLocalModel::class,
        ContactDiffLocalModel::class,
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(value = [Converters::class])
abstract class SubDiffDatabase: RoomDatabase() {

    abstract fun getGroupDiffDao(): GroupDiffDao

    abstract fun getContactDiffDao(): ContactDiffDao
}

@Database(
    entities = [
        GroupDiffLocalModel::class,
        ContactDiffLocalModel::class,
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(value = [Converters::class])
abstract class MainBackupDiffDatabase: RoomDatabase() {

    abstract fun getGroupDiffDao(): GroupDiffDao

    abstract fun getContactDiffDao(): ContactDiffDao
}

@Database(
    entities = [
        GroupDiffLocalModel::class,
        ContactDiffLocalModel::class,
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(value = [Converters::class])
abstract class SubBackupDiffDatabase: RoomDatabase() {

    abstract fun getGroupDiffDao(): GroupDiffDao

    abstract fun getContactDiffDao(): ContactDiffDao
}

object DiffDatabaseModule {
    val mainDiffDatabase by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        Room.databaseBuilder(
            AndroidSystemUtil.getContext(),
            MainDiffDatabase::class.java, "main_diff"
        )
            .allowMainThreadQueries()
            .addMigrations()
            .build()
    }

    val subDiffDatabase by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        Room.databaseBuilder(
            AndroidSystemUtil.getContext(),
            SubDiffDatabase::class.java, "sub_diff"
        )
            .allowMainThreadQueries()
            .addMigrations()
            .build()
    }

    val mainDiffBackupDatabase by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        Room.databaseBuilder(
            AndroidSystemUtil.getContext(),
            MainBackupDiffDatabase::class.java, "main_diff_backup"
        )
            .allowMainThreadQueries()
            .addMigrations()
            .build()
    }

    val subDiffBackupDatabase by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        Room.databaseBuilder(
            AndroidSystemUtil.getContext(),
            SubBackupDiffDatabase::class.java, "sub_diff_backup"
        )
            .allowMainThreadQueries()
            .addMigrations()
            .build()
    }
}