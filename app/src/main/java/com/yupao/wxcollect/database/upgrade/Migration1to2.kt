package com.yupao.wxcollect.database.upgrade

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/6/26/026 13:37
 * @Description 数据库版本1 -> 2
 */
object Migration1to2: Migration(1, 2) {

    private const val config_tab = "report_config"

    override fun migrate(database: SupportSQLiteDatabase) {
        kotlin.runCatching {
            YLog.i("Migration1to2", "version: ${database.version}")
            database.execSQL("ALTER TABLE `$config_tab` ADD COLUMN `circleNumber` INTEGER DEFAULT 30 ")
            database.execSQL("ALTER TABLE `$config_tab` ADD COLUMN `completeTime` INTEGER")
            database.execSQL("ALTER TABLE `$config_tab` ADD COLUMN `remark` TEXT")
            YLog.i("Migration1to2", "update succeed")
        }.exceptionOrNull()?.let {
            it.printStackTrace()
            YLog.printException("Migration1to2", it)
        }
    }
}