package com.yupao.wxcollect.database

import androidx.room.TypeConverter

/**
 * 自定义room 转换器
 * <AUTHOR>
 * @date 2022/6/24/024
 */
class Converters {

    /**
     * 字符集合转换为字符
     */
    @TypeConverter
    fun fromListString(value: List<String>?): String? {
        if (value.isNullOrEmpty()) return null
        return value.joinToString(",")
    }

    /**
     * 字符转换为字符集合
     */
    @TypeConverter
    fun string2List(value: String?): List<String>? {
        if (value.isNullOrEmpty()) return null
        return value.split(",").toList()
    }
}