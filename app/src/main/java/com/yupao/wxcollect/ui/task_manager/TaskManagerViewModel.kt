package com.yupao.wxcollect.ui.task_manager

import android.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yupao.scafold.ktx.nullStateIn
import com.yupao.scafold.ktx.signalFlow
import com.yupao.transmit.MessageManager
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.director.CollectEntity
import com.yupao.wxcollect.service.procedure.entity.director.IFinishedStatus
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatus
import com.yupao.wxcollect.service.procedure.entity.director.TaskStatusTypeAdapter
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

/**
 * 任务管理
 *
 * <p>创建时间：2024/8/7/007</p>
 *
 * <AUTHOR>
 */
@HiltViewModel
class TaskManagerViewModel @Inject constructor(
): ViewModel() {

    private val fetchDataSignal = signalFlow<String?>()

    private val dataRequest = QueryTaskRequest()

    val allData = fetchDataSignal.onEach {
        dataRequest.submit()
    }.flatMapLatest { appType ->
        dataRequest.dataFlow.map { taskList ->
            taskList
                .filter {
                    it.condition.appType == appType || appType == null
                }
                .sortedBy { it.condition.sort ?: Int.MAX_VALUE }
                .mapNotNull (this::toUIState)
        }
    }
        .flowOn(Dispatchers.IO)
        .nullStateIn(viewModelScope)

    private fun toUIState(model: CollectEntity): TaskManagerUIState? {
        val isCycleTask = model.cycleCount > 1
        val status = TaskStatusTypeAdapter.deserialize(model.extInfo)
        // 已完成任务或者非循环任务都不展示
        if (status is IFinishedStatus || !isCycleTask) {
            return null
        }
        val statusText: String
        val statusColor: Int
        if (status is TaskStatus.Failed) {
            statusText = "· 异常"
            statusColor = Color.parseColor("#FFFE524A")
        } else {
            statusText = "· 执行中"
            statusColor = Color.parseColor("#FFFF8904")
        }
        return TaskManagerUIState(
            taskCode = model.taskCode,
            name = model.taskName,
            intervalText = model.taskInterval.takeIf { it > 0 }?.let { "上传间隔：${it / 60}分钟" } ?: "",
            durationText = model.query.queryTime?.takeIf { it > 0 }?.let { "采集范围：${it / 60}分钟" } ?: "",
            recyclerText = "任务时效：${if(isCycleTask) "长期" else "单次"}",
            taskStatus = statusText,
            statusColor = statusColor,
            appType = model.condition.appType,
            appTypeText = "绑定标识：${if (model.condition.appType == ReportConfig.APP_SUB) "副系统" else "主系统"}",
            isShowPerformBtn = isCycleTask && status !is IFinishedStatus
        )
    }

    fun fetchData(taskCode: String?) {
        fetchDataSignal.tryEmit(taskCode)
    }

    override fun onCleared() {
        this.dataRequest.destroy()
        super.onCleared()
    }
}