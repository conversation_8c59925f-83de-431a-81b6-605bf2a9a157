package com.yupao.wxcollect.ui.upload_result

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/4/26/026 9:00
 * @Description
 */
@Keep
data class ReportInfoRequestEntity(
    //采集的微信号昵称
    val uploader_name: String?,
    //采集的微信号ID（原始）
    val uploader_id: String?,
    //采集的微信号ID（别名）
    val uploader_id_alias: String?,
    //手机编号
    val mobile_number: String?,
    //主副号：1=主号；2=副号；
    val wechat_type: Int?,
    //分页参数
    val page: Int?,
    //分页参数
    val page_size: Int?,
    //列表类型：0=全部；1=主号；2=副号；
    val list_type: Int?,
)
