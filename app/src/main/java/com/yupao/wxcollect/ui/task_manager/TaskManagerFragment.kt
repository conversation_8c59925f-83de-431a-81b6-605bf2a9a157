package com.yupao.wxcollect.ui.task_manager

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.rwz.hook.utils.ToastUtil
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.FragmentTaskManagerBinding
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.MessageEntity
import com.yupao.utils.system.toast.ToastUtils
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.ui.entity.TaskExecuteEntity
import com.yupao.wxcollect.ui.upload_node.UploadNodeActivity
import kotlinx.coroutines.launch

/**
 * 任务管理
 *
 * <p>创建时间：2024/8/7/007</p>
 *
 * <AUTHOR>
 */
class TaskManagerFragment: Fragment() {

    private lateinit var binding: FragmentTaskManagerBinding

    private val viewModel by viewModels<TaskManagerViewModel>()

    private val appType by lazy {
        arguments?.getString(TabActivity.KEY_APP_TYPE)
    }

    private val adapter by lazy {
        TaskManagerAdapter(appType, this.requireActivity())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_task_manager, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.list.apply {
            adapter = <EMAIL>
            layoutManager = LinearLayoutManager(this.context)
        }
        binding.executeAllTask.setOnClickListener {
            val list = viewModel.allData.value?.filter {
                it.isShowPerformBtn
            }?.map {
                TaskExecuteEntity(it.taskCode, it.appType)
            }
            if (list.isNullOrEmpty()) {
                ToastUtil.showShort("没有可以执行的任务")
                return@setOnClickListener
            }
            MessageManager.executor.submit(
                MessageEntity(
                    code = MessageCode.EXECUTE_TASK,
                    toHost = CoreClient.serviceHost,
                )
                    .putProxyHost(listOf(AppConstant.Host.wxCollectReportHost))
                    .put(MessageKey.TaskExecuteList, Gson().toJson(list))
            )
            UploadNodeActivity.start(requireContext())
        }
        lifecycleScope.launch {
            viewModel.allData.collect {
                val list = it ?: emptyList()
                binding.emptyView.root.visibility = if (list.isEmpty()) View.VISIBLE else View.GONE
                binding.executeAllTask.visibility = if (list.isEmpty()) View.GONE else View.VISIBLE
                <EMAIL>(list)
            }
        }
        viewModel.fetchData(appType)
    }
}