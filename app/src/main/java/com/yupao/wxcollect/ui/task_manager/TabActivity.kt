package com.yupao.wxcollect.ui.task_manager

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.yupao.execute.wxcollect.BR
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.ActivityTabBinding
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.ui.BaseActivity
import com.yupao.wxcollect.ui.viewpager.IFragmentFactory
import com.yupao.wxcollect.ui.viewpager.ViewPagerAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:22
 * @Description TabActivity
 */
@AndroidEntryPoint
class TabActivity: BaseActivity() {
    companion object {
        private const val TAG = "TabActivity"

        private const val KEY_FRAGMENT = "fragment"

        const val KEY_APP_TYPE = "appType"

        @JvmStatic
        fun start(context: Context, fragmentCls: Class<out Fragment>) {
            val starter = Intent(context, TabActivity::class.java)
                .putExtra(KEY_FRAGMENT, fragmentCls.name)
            context.startActivity(starter)
        }
    }

    private val viewModel by viewModels<TabViewModel>()

    private lateinit var binding: ActivityTabBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = BindViewMangerV2.bindActivity<ActivityTabBinding>(
            this,
            DataBindingConfigV2(
                R.layout.activity_tab,
                BR.vm,
                viewModel
            ).addBindingParam(BR.click, ClickProxy())
                .addBindingParam(BR.companion, ReportConfig.Companion)
        )
        binding.executePendingBindings()
        initView()
        initObserve()
        // 默认选择全部
        viewModel.setupAppType(null)
    }

    private fun initView() {
        findViewById<TextView>(R.id.title).text = "任务管理"
        findViewById<View>(R.id.back).setOnClickListener {
            onBackPressed()
        }
        with(binding.viewPager) {
            adapter = ViewPagerAdapter(this@TabActivity, object : IFragmentFactory {
                override fun create(index: Int?): Fragment {
                    val fragmentName = intent.getStringExtra(KEY_FRAGMENT)
                    if (fragmentName.isNullOrEmpty()) {
                       return Fragment()
                    }
                    val fragment = Class.forName(fragmentName).newInstance() as Fragment
                    fragment.arguments = Bundle().apply {
                        val appType = when(index) {
                            1 -> ReportConfig.APP_MAIN
                            2 -> ReportConfig.APP_SUB
                            else -> null
                        }
                        putString(KEY_APP_TYPE, appType)
                    }
                    return fragment
                }

                override fun size(): Int {
                    return 3
                }
            })
            isUserInputEnabled = false
        }
    }

    private fun initObserve() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.appType.collectLatest {
                    when (it) {
                        ReportConfig.APP_MAIN -> binding.viewPager.setCurrentItem(1, false)
                        ReportConfig.APP_SUB -> binding.viewPager.setCurrentItem(2, false)
                        else -> binding.viewPager.setCurrentItem(0, false)
                    }
                }
            }
        }
    }

    inner class ClickProxy {

        fun onTabClick(appType: String?) {
            viewModel.setupAppType(appType)
        }
    }
}