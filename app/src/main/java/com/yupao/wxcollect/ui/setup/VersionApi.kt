package com.yupao.wxcollect.ui.setup

import com.yupao.data.net.yupao.NetRequestInfo
import com.yupao.wxcollect.ui.entity.VersionRequestEntity
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * <AUTHOR>
 * @Date 2023/4/13/013 16:59
 * @Description https://w3nu1yaadv.feishu.cn/docx/OOMZd4DtaoqKFhxFM8Oc40cQnWb
 */
interface VersionApi {

    /**
     * 上传普通聊天记录
     */
    @POST("/backend/collect/wechat/appVersion")
    suspend fun appVersion(@Body entity: VersionRequestEntity): NetRequestInfo<VersionEntity>
}