package com.yupao.wxcollect.ui.setup

import androidx.lifecycle.ViewModel
import com.yupao.ylog.YLog
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 16:13
 * @Description 上传节点
 */
@HiltViewModel
class SetupViewModel @Inject constructor(
    val resp: SetupRepository
): ViewModel() {

    private val clearConfigSignal = MutableSharedFlow<List<String?>>(
        replay = 1,
        extraBufferCapacity = 1,
    )

    val clearConfigFlow = clearConfigSignal.flatMapConcat {
        resp.clear(it)
    }.flowOn(Dispatchers.IO)

    fun clearConfig(list: List<String?>?) {
        YLog.i("SetupViewModel", "clearConfig: list = $list")
        clearConfigSignal.tryEmit(list ?: return)
    }

    private val checkVersionSignal = MutableSharedFlow<Unit>(
        replay = 1,
        extraBufferCapacity = 1,
    )

    val checkVersionFlow = checkVersionSignal.flatMapConcat {
        resp.checkVersion()
    }.flowOn(Dispatchers.IO)

    fun checkVersion() {
        checkVersionSignal.tryEmit(Unit)
    }
}