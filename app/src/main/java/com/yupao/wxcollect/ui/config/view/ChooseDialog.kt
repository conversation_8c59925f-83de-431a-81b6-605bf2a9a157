package com.yupao.wxcollect.ui.config.view

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.*
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import com.yupao.scafold.basebinding.AnimConfig
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.DialogChooseSystemBinding
import com.yupao.execute.wxcollect.databinding.ItemChooseDialogBinding
import com.yupao.wxcollect.ui.config.entity.BottomSheetEntity
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023年3月27日 09:46:57
 * @Description 底部弹窗菜单
 */
class ChooseDialog : DialogFragment() {

    /**
     * 点击处理
     */
    private val clickProxy = ClickProxy()

    /**
     * databinding
     */
    private var _binding: DialogChooseSystemBinding? = null

    private var callback: ((List<BottomSheetEntity>) -> Unit)? = null

    private var data: List<BottomSheetEntity> ?= null

    private var checkedData = mutableListOf<BottomSheetEntity>()

    private var title: String? = null

    private var isSingleCheck = false

    private var currCheckIndex: Int = -1

    companion object {
        fun newInstance(title: String?, list: List<BottomSheetEntity>, isSingleCheck: Boolean = false,callback: ((List<BottomSheetEntity>) -> Unit)?): ChooseDialog {
            val fragment = ChooseDialog()
            fragment.data = list
            fragment.title = title
            fragment.callback = callback
            fragment.isSingleCheck = isSingleCheck
            return fragment
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.apply {
            AnimConfig.initShowAnim(AnimConfig.ANIM_TYPE_CENTER, this)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BindViewMangerV2.getViewDataBinding(
            viewLifecycleOwner,
            inflater,
            container,
            DataBindingConfigV2(R.layout.dialog_choose_system, 0, null)
        )
        _binding?.clickProxy = clickProxy
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        _binding?.title?.text = title
        val inflater = LayoutInflater.from(context)
        val bindingList = mutableListOf<ItemChooseDialogBinding>()
        data?.forEachIndexed { index, entity ->
            val binding = DataBindingUtil.inflate<ItemChooseDialogBinding>(
                inflater,
                R.layout.item_choose_dialog,
                null,
                false
            )
            bindingList.add(binding)
            binding.title.text = entity.title
            binding.item.setOnClickListener {
                val isChecked = binding.checkbox.isChecked
                YLog.i("ChooseDialog", "onViewCreated: currCheckIndex = $currCheckIndex, ${checkedData.isNotEmpty()}")
                if (isSingleCheck && currCheckIndex != -1 && checkedData.isNotEmpty()) {
                    if (currCheckIndex == index) {
                        return@setOnClickListener
                    } else {
                        checkedData.clear()
                        checkedData.add(entity)
                        binding.checkbox.toggle()
                        bindingList.getOrNull(currCheckIndex)?.checkbox?.toggle()
                        currCheckIndex = index
                        return@setOnClickListener
                    }
                }
                if (!isChecked) {
                    checkedData.add(entity)
                } else {
                    checkedData.remove(entity)
                }
                binding.checkbox.toggle()
                currCheckIndex = index
            }
            _binding?.container?.addView(binding.root)
        }
    }

    override fun onStart() {
        super.onStart()
        clearPadding()
    }

    /**
     * 去掉padding，修复无法全屏
     */
    private fun clearPadding() {
        dialog?.window?.let { window ->
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//            window.setWindowAnimations(R.style.AnimDownToTop)
            requireActivity().windowManager.defaultDisplay.getMetrics(DisplayMetrics())
            window.decorView.setPadding(0, 0, 0, 0)
            val params =
                if (window.attributes != null) window.attributes else WindowManager.LayoutParams()
            params.dimAmount = 0f
            params.gravity = Gravity.BOTTOM
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = params
        }
    }

    private fun performInit(list: List<String>) {
        ToastUtilsAssist.showCustomToast(requireContext(), "成功初始化")
    }

    inner class ClickProxy {
        fun clickClose() {
            dismissAllowingStateLoss()
        }

        fun clickSure() {
            if (checkedData.isEmpty()) {
                ToastUtilsAssist.showCustomToast(requireContext(), "至少选择一个")
                return
            }
            callback?.invoke(checkedData)
        }
    }
}
