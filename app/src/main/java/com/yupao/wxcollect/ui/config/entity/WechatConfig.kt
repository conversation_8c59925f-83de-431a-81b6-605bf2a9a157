package com.yupao.wxcollect.ui.config.entity

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 10:02
 * @Description 微信配置
 */
@Keep
data class WechatConfig(
    // 数据库路径
    val databasePath: String?,
    // wechat auth_info_key_prefs目录
    val authInfoKeyPrefsPath: String?,
    // 目标auth_info_key_prefs存储目录
    val destAuthInfoKeyPrefsDirPath: String?,
    // 微信版本是否高于7.0.20
    val isWxVersionHi: Boolean?,
)