package com.yupao.wxcollect.ui

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.yupao.wxcollect.transmit.ActivityLifecycleRequest
import com.yupao.wxcollect.transmit.entity.LifecycleEntity
import com.yupao.ylog.YLog

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/5/21/021</p>
 *
 * <AUTHOR>
 */
object ActivityLifecycleManager {

    fun initApp(app: Application) {
        app.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(p0: Activity, p1: Bundle?) {
            }

            override fun onActivityStarted(p0: Activity) {
            }

            override fun onActivityResumed(p0: Activity) {
                YLog.i("onActivityResumed: ${p0.javaClass.simpleName}")
                IReportClientManager.instance.addTransmitRequest(
                    ActivityLifecycleRequest(LifecycleEntity.resumeEvent())
                )
            }

            override fun onActivityPaused(p0: Activity) {
            }

            override fun onActivityStopped(p0: Activity) {
            }

            override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {
            }

            override fun onActivityDestroyed(p0: Activity) {
            }
        })
    }

}