package com.yupao.wxcollect.ui.wechat

import android.content.Intent
import com.yupao.utils.system.asm.AndroidSystemUtil

/**
 * 回调接口
 */
interface OnWechatSelectListener {
    /**
     * 账号被选择
     */
    fun onAccountSelected(account: WechatAccount)

    /**
     * 账号确认
     */
    fun onAccountConfirmed(account: WechatAccount)

    /**
     * 点击修改按钮
     */
    fun onAccountModify()

    /**
     * 手动输入微信号
     */
    fun onAccountInput(wechatId: String)

    /**
     * 取消操作
     */
    fun onCancel()

}

/**
 * 微信账号选择辅助类
 * 用于简化 WechatSelectActivity 的调用和回调处理
 */
object WechatSelectHelper : OnWechatSelectListener {
    // 回调接口
    private var listener: OnWechatSelectListener? = null

    /**
     * 初始化
     */
    fun init(listener: OnWechatSelectListener) {
        this.listener = listener
    }


    override fun onAccountSelected(account: WechatAccount) {
        this.listener?.onAccountSelected(account)
    }

    override fun onAccountConfirmed(account: WechatAccount) {
        this.listener?.onAccountConfirmed(account)
    }

    override fun onAccountModify() {
        this.listener?.onAccountModify()
    }

    override fun onAccountInput(wechatId: String) {
        this.listener?.onAccountInput(wechatId)
    }

    override fun onCancel() {
        this.listener?.onCancel()
    }

    /**
     * 启动账号选择
     */
    fun startForSelect(accounts: ArrayList<WechatAccount>) {
        val intent =
            Intent(AndroidSystemUtil.getContext(), WechatSelectActivity::class.java).apply {
                putParcelableArrayListExtra("accounts", accounts)
                putExtra("mode", WechatSelectActivity.MODE_SELECT)
            }
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        AndroidSystemUtil.getContext().startActivity(intent)
    }

    /**
     * 启动账号确认
     */
    fun startForConfirm(accounts: ArrayList<WechatAccount>) {
        val intent =
            Intent(AndroidSystemUtil.getContext(), WechatSelectActivity::class.java).apply {
                putParcelableArrayListExtra("accounts", accounts)
                putExtra("mode", WechatSelectActivity.MODE_CONFIRM)
            }
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        AndroidSystemUtil.getContext().startActivity(intent)
    }

    /**
     * 启动账号输入
     */
    fun startForInput() {
        val intent =
            Intent(AndroidSystemUtil.getContext(), WechatSelectActivity::class.java).apply {
                putExtra("mode", WechatSelectActivity.MODE_INPUT)
            }
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        AndroidSystemUtil.getContext().startActivity(intent)
    }

    /**
     * 释放资源
     */
    fun release() {
        listener = null
    }
}
