package com.yupao.wxcollect.ui.main

import android.Manifest
import android.app.NotificationManager
import android.content.ActivityNotFoundException
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.fragment.app.FragmentActivity
import com.permissionx.guolindev.PermissionX
import com.yupao.utils.log.LogUtil
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.utils.system.toast.ToastUtils
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.net_monitor.NetMonitorService
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.DataOutputStream
import java.util.concurrent.CountDownLatch


/**
 * <AUTHOR>
 * @Date 2023/4/26/026 17:47
 * @Description 权限请求帮助类
 */
object PermissionHelp {

    fun request(
        activity: FragmentActivity,
        lifecycleScope: CoroutineScope,
        onPermissionAllowed: suspend (Boolean) -> Unit
    ) {
        requestVpn(activity)
        var permissionResult = false
        val countDownLatch = CountDownLatch(2)
        lifecycleScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.IO) {
                val result = requestRoot(activity)
                YLog.i("PermissionHelp", "initPermission: root = $result")
                withContext(Dispatchers.Main) {
                    if (!result) {
                        ToastUtilsAssist.showCustomToast(activity, "未获取root权限")
                    }
                    permissionResult = permissionResult or result
                    countDownLatch.countDown()
                }
            }
            withContext(Dispatchers.Main) {
                val allPermission = mutableListOf(
                    Manifest.permission.READ_PHONE_STATE,
                    Manifest.permission.ACCESS_NETWORK_STATE,
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.READ_CALENDAR,
                    Manifest.permission.WRITE_CALENDAR,
                )
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // 锁屏通知
                    allPermission.add(Manifest.permission.USE_FULL_SCREEN_INTENT)
                }
                PermissionX.init(activity)
                    .permissions(allPermission)
                    .request{ allGranted, grantedList, deniedList ->
                        YLog.i(
                            "PermissionHelp",
                            "initPermission: allGranted = $allGranted, grantedList = $grantedList, deniedList = $deniedList"
                        )
                        permissionResult = permissionResult or allGranted
                        if (!allGranted) {
                            ToastUtilsAssist.showCustomToast(activity, "未获取相关权限")
                        }
                        countDownLatch.countDown()
                    }
            }
            countDownLatch.await()
            withContext(Dispatchers.Main) {
                requestIgnoreBatteryOptimizations()
                onPermissionAllowed.invoke(permissionResult)
            }
        }
    }

    private fun requestVpn(context: FragmentActivity) {
        val hasPermission = NetMonitorService.requestPermission(context)
        YLog.i("PermissionHelp", "requestVpn: hasPermission = $hasPermission")
    }

    private fun requestRoot(context: Context): Boolean {
        var os: DataOutputStream? = null
        var process: Process? = null
        try {
            val sourceDir = context.packageManager.getApplicationInfo(context.packageName, 0).sourceDir
            val cmd = "chmod 777 $sourceDir"
            process = Runtime.getRuntime().exec("su")
            os = DataOutputStream(process.outputStream)
            os.writeBytes(cmd + "\n")
            os.writeBytes("exit\n")
            os.flush()
            val result = process.waitFor()
            YLog.i("PermissionHelp", "requestRoot: $result")
            return result == 0
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            os?.close()
            process?.destroy()
        }
        return false
    }

//    public void requestIgnoreBatteryOptimizations() {
//        try {
//            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
//            intent.setData(Uri.parse("package:" + getPackageName()));
//            startActivity(intent);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    private fun requestIgnoreBatteryOptimizations() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val ignoringBatteryOptimizations = isIgnoringBatteryOptimizations()
            YLog.i("PermissionHelp", "requestIgnoreBatteryOptimizations: $ignoringBatteryOptimizations")
            if (!ignoringBatteryOptimizations) {
                val context = App.getContext() ?: return
                ToastUtils(context).showShort("请在省电保护打开【无限制】")
                kotlin.runCatching {
                    val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                    intent.data = Uri.parse("package:" + context.packageName)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                }.exceptionOrNull()?.printStackTrace()
            }
        }
    }

    private fun isIgnoringBatteryOptimizations(): Boolean {
        val context = App.getContext() ?: return false
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as? PowerManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager?.isIgnoringBatteryOptimizations(context.packageName) ?: false
        } else {
            true
        }
    }

    private fun isIgnoringBatteryOptimizations(context: Context): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val pm = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            return pm.isIgnoringBatteryOptimizations(context.getPackageName())
        }
        return false
    }

    fun turnIgnoringBatteryOptimizations(context: Context) {
        val ignoringBatteryOptimizations = isIgnoringBatteryOptimizations(context)
        LogUtil.d("PermissionHelp", "turn: ignoringBatteryOptimizations = $ignoringBatteryOptimizations")
        kotlin.runCatching {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.data = Uri.parse("package:" + context.packageName)
            context.startActivity(intent)
        }.exceptionOrNull()?.printStackTrace()
    }

    fun turnOnNotificationListener(context: Context) {
        kotlin.runCatching {
            if (!notificationListenerEnable(context)) {
                gotoNotificationAccessSetting(context)
            }
        }.exceptionOrNull()?.printStackTrace()
    }

    private fun notificationListenerEnable(context: Context): Boolean {
        var enable = false
        val packageName: String = context.packageName
        val flat = Settings.Secure.getString(context.contentResolver, "enabled_notification_listeners")
        if (flat != null) {
            enable = flat.contains(packageName)
        }
        return enable
    }

    private fun gotoNotificationAccessSetting(context: Context ): Boolean {
        try {
            val intent = Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            return true
        } catch(e: ActivityNotFoundException) {
            try {
                val intent = Intent()
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                val cn = ComponentName("com.android.settings","com.android.settings.Settings\$NotificationAccessSettingsActivity")
                intent.component = cn
                intent.putExtra(":settings:show_fragment", "NotificationAccessSettings")
                context.startActivity(intent)
                return true
            } catch(ex: Exception) {
                ex.printStackTrace()
            }
            return false
        }
    }


    private fun isNotificationEnable(): Boolean {
        return kotlin.runCatching {
            val manager = AndroidSystemUtil.getContext().getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.areNotificationsEnabled()
        }.getOrDefault(false)
    }

    fun checkNotification() {
        val isNotificationEnable = isNotificationEnable()
        YLog.i("checkNotification: $isNotificationEnable")
        if (!isNotificationEnable) {
            val context = AndroidSystemUtil.getContext()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                kotlin.runCatching {
                    val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                    intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    context.startActivity(intent)
                }.onFailure {
                    YLog.printException("checkNotification", it)
                }
            }
        }
    }
}