package com.yupao.wxcollect.ui.main

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.yupao.data.protocol.data
import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.execute.wxcollect.R
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.execute.MessageEntity
import com.yupao.transmit.handle.IMessageHandler
import com.yupao.utils.system.toast.ToastUtils
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.constant.AppPathCache
import com.yupao.wxcollect.constant.SEPARATOR
import com.yupao.wxcollect.constant.WECHAT_DATABASE_NAME
import com.yupao.wxcollect.net.AppHttpClient
import com.yupao.wxcollect.service.IReportServiceManager
import com.yupao.wxcollect.service.db.DatabaseHelperImpl
import com.yupao.wxcollect.service.db.DatabaseOpenResultEntity
import com.yupao.wxcollect.service.net_monitor.NetMonitorService
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.service.procedure.AutoInstallManager
import com.yupao.wxcollect.service.procedure.api.ReportApi
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.entity.query.MessageQueryEntity
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.transmit.PrintLogTransmitRequest
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.ui.BaseActivity
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.setup.WechatAccountService
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.wxcollect.util.LogUtil
import com.yupao.wxcollect.util.SpUtil
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.File
import java.nio.charset.Charset
import javax.inject.Inject
import kotlin.math.absoluteValue

/**
 * <AUTHOR>
 * @Date 2023/4/20/020 15:37
 * @Description
 */
@AndroidEntryPoint
class DebugActivity: BaseActivity() {

    @Inject
    lateinit var reportRepository: ReportRepository

    @Volatile
    private var clickEnable = false

    companion object {
        private const val TAG = "DebugActivity"

        private const val KEY_SAVE_LOG = "save_log"

        @JvmStatic
        fun start(context: Context?, saveLog: Boolean) {
            val starter = Intent(context, DebugActivity::class.java)
                .putExtra(KEY_SAVE_LOG, saveLog)
            starter.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context?.startActivity(starter) ?: return
        }
    }

    private val updateBaseInfo = object : IMessageHandler {
        override fun handle(fromHost: String?, args: ArgsEntity?) {
            val currUrl = args?.optString(MessageKey.CurrUrl)
            YLog.i(tag = TAG, "currUrl: $currUrl")
            if (!currUrl.isNullOrEmpty()) {
                SpUtil.putString(SpUtil.KEY_CURR_URL, currUrl)
                setupEnvInfo()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_debug)
        MessageManager.handler.registerHandler(MessageCode.UPDATE_BASE_INFO, updateBaseInfo)
        MessageManager.executor.submit(MessageEntity(MessageCode.FETCH_BASE_INFO, AppConstant.Host.wxCollectReportHost))
        findViewById<View>(R.id.copy).setOnClickListener {
//            WechatFileUtil.copyFromOtherProcess(
//                "/data/user/0/com.tencent.mm/shared_prefs/auth_info_key_prefs.xml",
//                "/data/user/0/com.yupao.wxcollect"
//            )
            val destDir = "/data/user/0/com.yupao.execute.wxcollect/files/"
            File(destDir, WECHAT_DATABASE_NAME).takeIf { it.exists() }?.delete()
            WechatFileUtil.copyFromOtherProcess(
                "/data/user/0/com.tencent.mm/shared_prefs/auth_info_key_prefs.xml",
//                "/data/user/0/com.tencent.mm/MicroMsg/e0c69019eaa3c23f8d23c851f0634d43/EnMicroMsg.db",
                destDir
            ) {
                YLog.i("DebugActivity", "copyFromOtherProcess: result = $it")
            }
        }
        findViewById<View>(R.id.startService).setOnClickListener {
            IReportClientManager.instance.startService(
                this,
                from = IReportServiceManager.KEY_FROM_APP_APPLICATION,
                config = NotificationConfig(
                    pendingActivityPath = MainActivity::class.java.name
                )
            )
        }
        findViewById<View>(R.id.switchAccount).setOnClickListener {
            val intent = Intent(this, WechatAccountService::class.java)
            intent.putExtra(WechatAccountService.TURN_ON, true)
            intent.putExtra(WechatAccountService.APP_TYPE_LIST, arrayListOf(ReportConfig.APP_MAIN))
            startService(intent)
        }
        findViewById<View>(R.id.openVpn).setOnClickListener {
            NetMonitorService.start(this, 123)
        }
        findViewById<View>(R.id.closeVpn).setOnClickListener {
            NetMonitorService.stop(this)
        }
        findViewById<View>(R.id.print).setOnClickListener {
            // 保存日志
            LogUtil.saveToFile()
            IReportClientManager.instance.addTransmitRequest(PrintLogTransmitRequest())
        }
        setupEnvInfo()
        addBtn("禁止充电") {
//            CmdUtil.execCmd("dumpsys battery set usb 0")
            CmdUtil.executeAsync("echo 1 > /sys/class/power_supply/battery/input_suspend")
        }
        addBtn("允许充电") {
//            CmdUtil.execCmd("dumpsys battery set usb 1")
            CmdUtil.executeAsync("echo 0 > /sys/class/power_supply/battery/input_suspend")
        }
        addBtn("静默升级") {
            lifecycleScope.launch {
                delay(10000)
                // /data/user/0/com.yupao.wxcollect/files/wxcollect_v11.1.0_release.apk
                // /data/user/0/com.yupao.wxcollect/files/app.apk
//                val appFilePath = "${<EMAIL>}/app.apk"
                val appFilePath = "${<EMAIL>}/weixin8050android2701_0x2800323e_arm64.apk"
                val exists = File(appFilePath).exists()
                YLog.d("DebugActivity", "onCreate: exists = $exists")
                CmdUtil.executeAsync("pm install -r $appFilePath")
            }
        }
        addBtn("电池温度") {
            CmdUtil.executeAsync("dumpsys battery | grep temperature") { result ->
                YLog.d("DebugActivity", "onCreate: result = $result")
                ToastUtils(this@DebugActivity).showShort(result.result)
            }
        }
        addBtn("SQL查询") {
            performQuery("select * from message where type = 285212721")
        }

        addBtn("启动VPN") {
            val requestPermission = NetMonitorService.requestPermission(this)
            if (requestPermission) {
                NetMonitorService.start(this)
            }
        }
        val isSaveLog = intent.getBooleanExtra(KEY_SAVE_LOG, false)
        if (isSaveLog) {
            // 保存日志
            LogUtil.saveToFile()
            IReportClientManager.instance.addTransmitRequest(PrintLogTransmitRequest())
        }
        if (BuildConfig.DEBUG) {
            addBtn("app本地更新") {
                kotlin.runCatching {
                    val ip = "*************"
                    val url = "http://${ip}:8080/execute_wxcollect_v1.0.0_test_pre.apk"
                    AutoInstallManager.performUpgrade(url)
                }
            }
        }
    }

    private fun setupEnvInfo() {
        val view = findViewById<TextView>(R.id.env)
        view?.post {
            view.text = "url: ${AppConstant.URL.baseUrl()} " +
                    "\nVersionName: ${BuildConfig.VERSION_NAME}" +
                    "\nVersionCode: ${BuildConfig.VERSION_CODE}" +
                    "\nlastCommit: ${BuildConfig.lastCommit}" +
                    "\nbuildTime: ${BuildConfig.buildTime}" +
                    ""
        }
    }

    private fun addBtn(text: String, block: () -> Unit) {
        findViewById<ViewGroup>(R.id.container).apply {
            val btn = Button(this@DebugActivity)
            btn.text = text
            addView(btn, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            btn.setOnClickListener {
                block.invoke()
            }
        }
    }

    private fun performQuery(script: String) {
        ProcessLifecycleOwner.get().lifecycleScope.launch(Dispatchers.IO) {
            reportRepository.queryAllConfig()?.collectLatest {
                Log.d("TAG", "fetchConfig: $it")
                it.data?.firstOrNull()?.let { config ->
                    val help = DatabaseHelperImpl()
                    val databasePath = config.databasePath
                    val databaseBaseName = File(databasePath).parentFile?.name  ?: (databasePath.hashCode().absoluteValue)
                    val destDir = AppPathCache.appWechatDir() + SEPARATOR + databaseBaseName + SEPARATOR
                    val targetFilePath = destDir + WECHAT_DATABASE_NAME
                    val entity = help.open(targetFilePath, config.password)
                    if (entity.code == DatabaseOpenResultEntity.CODE_OK) {
                        kotlin.runCatching {
//                            help.rawQuery(script, "Test")
                            val list = help.query(script, MessageQueryEntity::class, "Test")
//                            val content = list?.firstOrNull()?.content
                            list?.forEach {
                                loadUrlContent(it?.content ?: return@collectLatest)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun parseUrl(content: String): String {
        val startFlag = "http://mp.weixin.qq.com/s"
        val start = content.indexOf(startFlag)
        val endFlag = "#rd"
        val end = content.indexOf(endFlag)
        return content.substring(start, end + endFlag.length)
    }

    private fun loadUrlContent(orgUrl: String) {
        val url = kotlin.runCatching {
            parseUrl(orgUrl)
        }.getOrNull() ?: return
//        Log.d("DebugActivity", "loadUrlContent: orgUrl = $orgUrl")
        Log.d("DebugActivity", "loadUrlContent: url = $url")
        ProcessLifecycleOwner.get().lifecycleScope.launch(Dispatchers.IO) {
            val call = AppHttpClient.instanceForC.createApiService(ReportApi::class.java).urlContent(url)
            val callback = object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    val string = response.body()?.string() ?: orgUrl
//                    Log.d("TAG", "onResponse: $string")
                    File(getExternalFilesDir(null), "gzh.txt").writeText(string)
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                }
            }
            call.enqueue(callback)
        }
    }

    override fun onResume() {
        super.onResume()
        if (!clickEnable) {
            lifecycleScope.launch {
                delay(1000)
                clickEnable = true
            }
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (!clickEnable) {
            return true
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onDestroy() {
        super.onDestroy()
        MessageManager.handler.unregisterHandler(MessageCode.UPDATE_BASE_INFO)
    }
}

fun main() {
    val result = mutableListOf<String>()
    findFilePathByKeywords(
        "E:\\自动导\\log_2024-06-03_11-11-34\\wxcollect\\log\\log\\report\\main\\customize\\source",
        "22266476141@chatroom",
        result
    )
    println("result: ${result.joinToString("\n")}")
}

fun findFilePathByKeywords(dirPath: String, keywordRegex: String, list: MutableList<String>) {
    val dirFile = File(dirPath)
    if (dirFile.isDirectory) {
        val listFiles = dirFile.listFiles()
        listFiles?.forEach {
            findFilePathByKeywords(it.absolutePath, keywordRegex, list)
        }
        return
    }
    val text = dirFile.readText(Charset.forName("UTF-8"))
//    println("path: $dirPath")
    if (text.contains(Regex(keywordRegex))) {
        list.add(dirPath)
    }
}