package com.yupao.wxcollect.ui.setup

import android.accessibilityservice.AccessibilityService
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils.SimpleStringSplitter
import android.view.View
import android.widget.TextView
import androidx.activity.viewModels
import androidx.annotation.IdRes
import androidx.appcompat.widget.SwitchCompat
import androidx.lifecycle.lifecycleScope
import com.liulishuo.filedownloader.BaseDownloadTask
import com.liulishuo.filedownloader.FileDownloadListener
import com.liulishuo.filedownloader.FileDownloader
import com.yupao.data.protocol.doError
import com.yupao.data.protocol.doLoading
import com.yupao.data.protocol.doSuccess
import com.yupao.execute.wxcollect.R
import com.yupao.scafold.baseui.LoadingView
import com.yupao.utils.system.asm.AndroidSystemUtil
import com.yupao.utils.system.asm.PhoneUtils
import com.yupao.utils.system.toast.ToastUtils
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.BatterySwitchManager
import com.yupao.wxcollect.service.CoroutinePool
import com.yupao.wxcollect.service.IReportServiceManager
import com.yupao.wxcollect.service.db.KVHelper
import com.yupao.wxcollect.service.net_monitor.NetMonitorService
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.transmit.CheckSupportClearENRequest
import com.yupao.wxcollect.ui.BaseActivity
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.config.entity.BottomSheetEntity
import com.yupao.wxcollect.ui.config.view.ChooseDialog
import com.yupao.wxcollect.ui.dialog.WarnDialog
import com.yupao.wxcollect.ui.dialog.WarnEntity
import com.yupao.wxcollect.ui.main.MainActivity
import com.yupao.wxcollect.util.KVUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File


/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:22
 * @Description 设置页面
 */
@AndroidEntryPoint
class SetupActivity: BaseActivity() {

    private val viewModel by viewModels<SetupViewModel>()

    private val loadingView: LoadingView by lazy { findViewById(R.id.loading) }

    private val netSwitch by lazy {
        findViewById<SureSwitchCompat>(R.id.newSwitch)
    }

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_setup)
        findViewById<TextView>(R.id.title).text = "设置"
        findViewById<View>(R.id.back).setOnClickListener {
            onBackPressed()
        }
        findViewById<View>(R.id.init_config).setOnClickListener {
            initConfig()
        }
        findViewById<View>(R.id.clear_en).setOnClickListener {
            clearEn()
        }
        findViewById<View>(R.id.upgrade).setOnClickListener {
            if (checkInstallPermission()) {
                viewModel.checkVersion()
            } else {
                ToastUtilsAssist.showCustomToast(this, "请先授予app安装权限")
            }
        }
        findViewById<TextView>(R.id.version).text = "V${AndroidSystemUtil.getVersionName()}"
        val batterySwitch = findViewById<SwitchCompat>(R.id.batterySwitch)
        batterySwitch.isChecked = BatterySwitchManager.isCtrl()
        batterySwitch.setOnCheckedChangeListener { _, checked ->
            BatterySwitchManager.save(checked)
            ToastUtils(this).showShort(
                if(checked) "已开启控制充电开关" else "已关闭控制充电开关"
            )
        }
        // 拷贝最新数据库开关
        initSwitchButton(R.id.isCopyLatestDb, KVHelper::isCopyLatestDb, KVHelper::saveCopyLatestDb)
        // clash提示开关
        initSwitchButton(R.id.isClashTipsDb, KVHelper::isClashTipsDb, KVHelper::saveClashTipsDb)

        CoroutinePool.other.launch {
            val isChecked = KVHelper.netSwitchEnable()
            withContext(Dispatchers.Main) {
                netSwitch.isChecked = isChecked
            }
        }
        netSwitch.setOnCheckedChanged { newValue ->
            if (newValue) {
                WarnDialog.newInstance(WarnEntity(
                    "确定执行时不关闭微信网络吗？",
                    "不关闭微信网络可能会导致上传失败，请谨慎操作。"
                ) {
                    netSwitch.isChecked = true
                    CoroutinePool.other.launch {
                        KVHelper.saveNetSwitchEnable(true)
                    }
                    NetMonitorService.stop(this)
                    addCloseTask()
                }).show(supportFragmentManager, "WarnDialog")
            } else {
                CoroutinePool.other.launch {
                    KVHelper.saveNetSwitchEnable(false)
                }
                netSwitch.isChecked = false
            }
        }
        val openKeepAlive = findViewById<SwitchCompat>(R.id.openKeepAlive)
        lifecycleScope.launch(Dispatchers.IO) {
            val keepAlive = KVUtil.isOpenKeepAlive()
            withContext(Dispatchers.Main) {
                openKeepAlive.isChecked = keepAlive
            }
        }
        openKeepAlive.setOnCheckedChangeListener { _, isChecked ->
            lifecycleScope.launch(Dispatchers.IO) {
                KVUtil.setKeepAlive(isChecked)
            }
        }
        initObserve()
        hindLoading()
    }

    private fun initSwitchButton(
        @IdRes id: Int,
        initValue: suspend () -> Boolean,
        setValue: suspend (Boolean) -> Unit
    ) {
        val button = findViewById<SwitchCompat>(id)
        lifecycleScope.launch(Dispatchers.IO) {
            button.isChecked = initValue.invoke()
        }
        button.setOnCheckedChangeListener { _, checked ->
            lifecycleScope.launch(Dispatchers.IO) {
                setValue(checked)
            }
        }
    }

    private var closeTaskJob: Job? = null

    private fun addCloseTask() {
        this.closeTaskJob?.cancel()
        this.closeTaskJob = lifecycleScope.launch(Dispatchers.Main) {
            delay(KVHelper.NetSwitchInvalidTime)
            withContext(Dispatchers.IO) {
                KVHelper.saveNetSwitchEnable(false)
            }
            netSwitch.isChecked = false
        }
    }

    private fun checkUpgrade(entity: VersionEntity?): Boolean {
        val version = entity?.version ?: return false
        val currVersion = AndroidSystemUtil.getVersionCode()
        val newVersion = version.toIntOrNull() ?: return false
        YLog.i("SetupActivity", "checkUpgrade: currVersion = $currVersion, newVersion = $newVersion")
        if (newVersion > currVersion && !entity.download_link.isNullOrEmpty()) {
            performUpgrade(entity.download_link)
            return true
        }
        return false
    }

    private fun performUpgrade(url: String) {
        val dialog = ProcessDialog()
        val appFile = File(this.filesDir, "app.apk")
        if (appFile.exists()) {
            appFile.delete()
        }
        appFile.parentFile?.takeUnless { it.exists() }?.mkdirs()
        val appFilePath = appFile.absolutePath
        YLog.i("SetupActivity", "start download")
        FileDownloader.setup(App.getContext())
        FileDownloader.getImpl().create(url)
            .setPath(appFilePath)
            .apply {
                listener = object : FileDownloadListener() {
                    override fun pending(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {

                    }

                    override fun connected(
                        task: BaseDownloadTask?,
                        etag: String?,
                        isContinue: Boolean,
                        soFarBytes: Int,
                        totalBytes: Int
                    ) {
                        YLog.i(
                            "SetupActivity",
                            "connected: etag = $etag, isContinue = $isContinue, soFarBytes = $soFarBytes, totalBytes = $totalBytes"
                        )
                        dialog.show(supportFragmentManager, "app-update")
                    }

                    override fun progress(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {
                        dialog.setProcess((soFarBytes * 100L / totalBytes).toInt())
                    }

                    override fun blockComplete(task: BaseDownloadTask?) {
                    }

                    override fun retry(task: BaseDownloadTask?, ex: Throwable?, retryingTimes: Int, soFarBytes: Int) {
                    }

                    override fun completed(task: BaseDownloadTask) {
                        YLog.i("SetupActivity", "download completed, start install")
                        dialog.dismiss()
                        PhoneUtils.openAPKFile(this@SetupActivity, appFilePath)
//                        installApk(appFilePath)
                    }

                    override fun paused(task: BaseDownloadTask, soFarBytes: Int, totalBytes: Int) {

                    }

                    override fun error(task: BaseDownloadTask, e: Throwable) {
                        e.printStackTrace()
                        dialog.dismiss()
                        YLog.printException("app-upgrade", e)
                    }

                    override fun warn(task: BaseDownloadTask) {

                    }
                }
            }
            .start()
    }

    private fun checkInstallPermission(): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val hasInstallPermission: Boolean = packageManager.canRequestPackageInstalls()
            if (!hasInstallPermission) {
                //注意这个是8.0新API
                val packageURI = Uri.parse("package:$packageName")
                val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES, packageURI)
                if (intent.resolveActivity(packageManager) == null) {
                    return false
                }
                startActivityForResult(intent, PhoneUtils.INSTALL_PERMISSION_CODE)
                return  false
            }
        }
        return true
    }

    private var clearEnDialog: ChooseDialog? = null

    private fun clearEn() {
        val list = mutableListOf(
            BottomSheetEntity(unique = ReportConfig.APP_MAIN, "清除主系统EN"),
            BottomSheetEntity(unique = ReportConfig.APP_SUB, "清除副系统EN")
        )
        clearEnDialog?.dismissAllowingStateLoss()
        clearEnDialog = ChooseDialog.newInstance("清除EN", list, true) {
            clearEnDialog?.dismissAllowingStateLoss()
            if (isAccessibilitySettingsOn(this@SetupActivity, WechatAccountService::class.java)) {
                checkService(it)
            } else {
                turnAccessibilitySettings()
            }
        }.apply {
            show(supportFragmentManager, "ChooseDialog")
        }
    }

    private fun turnAccessibilitySettings() {
        kotlin.runCatching {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }
    }

    private fun checkService(list: List<BottomSheetEntity>) {
        IReportClientManager.instance.addTransmitRequest(CheckSupportClearENRequest(){
            YLog.i("SetupActivity", "checkService: it = $it")
            if (it) {
                performTurn(list)
            } else {
                ToastUtilsAssist.showCustomToast(App.getContext(), "请先开启无障碍服务")
                turnAccessibilitySettings()
            }
        })
    }

    private fun performTurn(it: List<BottomSheetEntity>) {
        val appTypeList = arrayListOf<String>()
        it.forEach {
            it.unique?.let { unique ->
                appTypeList.add(unique)
            }
        }
        val intent = Intent(this, WechatAccountService::class.java)
        intent.putExtra(WechatAccountService.TURN_ON, true)
        intent.putExtra(
            WechatAccountService.KEY_NOTICE_CONFIG, NotificationConfig(
                pendingActivityPath = MainActivity::class.java.name,
                content = "正在监听微信页面",
                title = "微信清除EN服务"
            ))
        intent.putExtra(WechatAccountService.APP_TYPE_LIST, appTypeList)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }

    private fun isAccessibilitySettingsOn(
        context: Context,
        clazz: Class<out AccessibilityService?>
    ): Boolean {
        var accessibilityEnabled = 0
        val service: String = context.getPackageName().toString() + "/" + clazz.canonicalName
        try {
            accessibilityEnabled = Settings.Secure.getInt(
                context.getApplicationContext().getContentResolver(),
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            e.printStackTrace()
        }
        val mStringColonSplitter = SimpleStringSplitter(':')
        if (accessibilityEnabled == 1) {
            val settingValue: String = Settings.Secure.getString(
                context.applicationContext.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            if (settingValue != null) {
                mStringColonSplitter.setString(settingValue)
                while (mStringColonSplitter.hasNext()) {
                    val accessibilityService = mStringColonSplitter.next()
                    if (accessibilityService.equals(service, ignoreCase = true)) {
                        return true
                    }
                }
            }
        }
        return false
    }

    private var initConfigDialog: ChooseDialog? = null

    private fun initConfig() {
        val list = mutableListOf(
            BottomSheetEntity(unique = ReportConfig.APP_MAIN, "初始化主系统"),
            BottomSheetEntity(unique = ReportConfig.APP_SUB, "初始化副系统")
        )
        initConfigDialog?.dismissAllowingStateLoss()
        initConfigDialog = ChooseDialog.newInstance("初始化配置", list, false) { result ->
            viewModel.clearConfig(result.map { it.unique })
        }.apply {
            show(supportFragmentManager, "ChooseDialog")
        }
    }

    private fun showLoading() {
        loadingView.startLoading()
        loadingView.visibility = View.VISIBLE
    }

    private fun hindLoading() {
        loadingView.stopLoading()
        loadingView.visibility = View.GONE
    }

    private fun initObserve() {
        lifecycleScope.launchWhenResumed {
            viewModel.clearConfigFlow.collectLatest {
                it.doLoading {
                    showLoading()
                }
                it.doSuccess {
                    initConfigDialog?.dismissAllowingStateLoss()
                    hindLoading()
                    ToastUtilsAssist.showCustomToast(this@SetupActivity, "初始化成功")
                    IReportClientManager.instance.startService(
                        this@SetupActivity,
                        from = IReportServiceManager.KEY_FROM_SETUP,
                        config = NotificationConfig(
                            pendingActivityPath = MainActivity::class.java.name
                        )
                    )
                }
                it.doError {
                    YLog.printException("clearConfig", it.exception)
                    hindLoading()
                    ToastUtilsAssist.showCustomToast(this@SetupActivity, "初始化失败")
                }
            }
        }
        lifecycleScope.launchWhenResumed {
            viewModel.checkVersionFlow.collectLatest {
                it.doLoading {
                    showLoading()
                }
                it.doSuccess {
                    hindLoading()
                    if (it.data?.isOK() == true) {
//                        val result = checkUpgrade(
//                            VersionEntity(
//                                date = null,
//                                version = "999",
//                                download_link = "http://yupaoapp.vrtbbs.com/package/android/1686146393_zidongdao_v1.0.3_release.apk"
//                            )
//                        )
                        val result = checkUpgrade(it.data?.getData())
                        if (!result) {
                            ToastUtilsAssist.showCustomToast(this@SetupActivity, "您已经是最新版本")
                        }
                    } else {
                        ToastUtilsAssist.showCustomToast(this@SetupActivity, "检测更新失败")
                    }
                }
                it.doError {
                    YLog.printException("checkVersion", it.exception)
                    hindLoading()
                    ToastUtilsAssist.showCustomToast(this@SetupActivity, "检测更新失败")
                }
            }
        }
    }
}