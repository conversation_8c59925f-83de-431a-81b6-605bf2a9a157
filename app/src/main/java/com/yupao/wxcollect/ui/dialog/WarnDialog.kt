package com.yupao.wxcollect.ui.dialog


import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Parcelable
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.DialogWarnBinding
import com.yupao.scafold.basebinding.AnimConfig
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * @Date 2023年3月27日 09:46:57
 * @Description 提示弹窗
 */
class WarnDialog : DialogFragment() {

    private var binding: DialogWarnBinding? = null

    companion object {
        private const val KEY_CONFIG = "config"

        fun newInstance(entity: WarnEntity): WarnDialog {
            val fragment = WarnDialog()
            fragment.arguments = bundleOf(KEY_CONFIG to entity)
            return fragment
        }
    }

    private val entity: WarnEntity? by lazy {
        return@lazy arguments?.getParcelable<WarnEntity>(KEY_CONFIG)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.apply {
            AnimConfig.initShowAnim(AnimConfig.ANIM_TYPE_CENTER, this)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = BindViewMangerV2.getViewDataBinding(
            viewLifecycleOwner,
            inflater,
            container,
            DataBindingConfigV2(R.layout.dialog_warn, 0, null)
        )
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding?.content?.text = entity?.content
        if (entity?.warn.isNullOrEmpty()) {
            binding?.warn?.visibility = View.INVISIBLE
        } else {
            binding?.warn?.visibility = View.VISIBLE
            binding?.warn?.text = entity?.warn
        }
        binding?.sure?.setOnClickListener {
            entity?.sureBlock?.invoke()
            dismissAllowingStateLoss()
        }
        binding?.cancel?.setOnClickListener {
            entity?.cancelBlock?.invoke() ?: dismissAllowingStateLoss()
        }
        dialog?.setCanceledOnTouchOutside(false)
        dialog?.setCancelable(false)
    }

    override fun onStart() {
        super.onStart()
        clearPadding()
    }

    /**
     * 去掉padding，修复无法全屏
     */
    private fun clearPadding() {
        dialog?.window?.let { window ->
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//            window.setWindowAnimations(R.style.AnimDownToTop)
            requireActivity().windowManager.defaultDisplay.getMetrics(DisplayMetrics())
            window.decorView.setPadding(0, 0, 0, 0)
            val params =
                if (window.attributes != null) window.attributes else WindowManager.LayoutParams()
            params.dimAmount = 0f
            params.gravity = Gravity.CENTER
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = params
        }
    }
}

@Parcelize
data class WarnEntity(
    val content: String,
    val warn: String? = null,
    val cancelBlock: (() -> Unit)? = null,
    val sureBlock: () -> Unit,
): Parcelable