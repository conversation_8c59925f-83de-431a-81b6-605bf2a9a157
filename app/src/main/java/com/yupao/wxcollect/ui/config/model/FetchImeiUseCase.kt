package com.yupao.wxcollect.ui.config.model

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.telephony.TelephonyManager
import com.yupao.data.protocol.Resource
import com.yupao.wxcollect.App
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 10:13
 * @Description 获取Imei
 */
class FetchImeiUseCase @Inject constructor(){
    companion object {
        const val hiVersionIMEI = "1234567890ABCDEF"
    }

    operator fun invoke(
        isWxVersionHi: Boolean?,
        dispatcher: CoroutineDispatcher?
    ): Flow<Resource<List<String?>>> {
        return flow {
            emit(Resource.Loading)
            val result = fetchCurrWxUin(isWxVersionHi)
            emit(result)
        }.catch { e ->
            emit(Resource.Error(exception = e))
        }
            .flowOn(dispatcher ?: Dispatchers.IO)
    }

    @SuppressLint("MissingPermission")
    private fun fetchCurrWxUin(isWxVersionHi: Boolean?): Resource<List<String?>> {
        return try {
            val tm =
                App.getContext()?.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
            val list = mutableListOf<String>()
            if (Build.BRAND == "xiaomi" && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                kotlin.runCatching {
                    tm?.getImei(0)
                }.getOrNull()?.let {
                    list.add(it)
                }
                kotlin.runCatching {
                    tm?.getImei(1)
                }.getOrNull()?.let {
                    if (!list.contains(it)) {
                        list.add(it)
                    }
                }
                kotlin.runCatching {
                    tm?.meid
                }.getOrNull()?.let {
                    if (!list.contains(it)) {
                        list.add(it)
                    }
                }
            }
            kotlin.runCatching {
                tm?.deviceId
            }.getOrNull()?.let {
                if (!list.contains(it)) {
                    list.add(it)
                }
            }
            list.add(hiVersionIMEI)
            YLog.i("FetchImeiUseCase", "fetchCurrWxUin: list = $list")
            Resource.Success(list)
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Success(listOf(hiVersionIMEI))
        }
    }
}