package com.yupao.wxcollect.ui.config.model

import android.util.Xml
import com.yupao.data.protocol.Resource
import com.yupao.wxcollect.util.CmdUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import org.xmlpull.v1.XmlPullParser
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.io.StringBufferInputStream
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 10:13
 * @Description 获取WxUni
 */
class FetchWxUinUseCase @Inject constructor(){

    operator fun invoke(fileDir: String?, dispatcher: CoroutineDispatcher?): Flow<Resource<String?>> {
        return flow {
            emit(Resource.Loading)
            if (fileDir.isNullOrEmpty()) {
                return@flow emit(Resource.Error(errorMsg = "filePath为空"))
            }
            val result = fetchCurrWxUin(File(fileDir).listFiles()?.lastOrNull()?.absolutePath)
            emit(result)
        }.catch { e ->
            emit(Resource.Error(exception = e))
        }
            .flowOn(dispatcher ?: Dispatchers.IO)
    }

    private suspend fun fetchCurrWxUin(filePath: String?): Resource<String> {
        filePath ?: return Resource.Error(errorMsg = "filePath为空")
        val file = File(filePath)
        try {
            val inputStream: InputStream = try {
                file.readBytes()
                FileInputStream(file)
            } catch (e: Exception) {
                e.printStackTrace()
                YLog.printException("FetchWxUinUseCase", e)
                // 尝试通过命令获取文件内容
                val content = withContext(Dispatchers.IO) {
                    CmdUtil.execCmdWithBack("cat $filePath")
                }
                if (content.isEmpty()) {
                    throw e
                } else {
                    StringBufferInputStream(content.joinToString("\n"))
                }
            }
            val parser = Xml.newPullParser()
            //2.指定解析器要解析的xml文件在哪里,这里以InputStream 的形式给予
            parser.setInput(inputStream, "utf-8")
            //3.得到事件类型
            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                //XmlPullParser.START_TAG=2:遇到开始标签时执行
                if (eventType == XmlPullParser.START_TAG) {//得到当前解析到的标签的名字
                    val starTag = parser.name
                    if ("int" == starTag) {
                        if ("_auth_uin" == parser.getAttributeValue(0)) {
                            return Resource.Success(parser.getAttributeValue(1))
                        }
                    }
                }
                //将事件流继续向后推进
                eventType = parser.next()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return Resource.Error(
                errorMsg = "获取微信uid失败，请检查auth_info_key_prefs文件权限，文件地址：$filePath",
                exception = e,
            )
        }
        return Resource.Error(
            errorMsg = "获取微信uid失败，文件地址：$filePath",
        )
    }
}