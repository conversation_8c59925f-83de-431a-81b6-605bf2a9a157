package com.yupao.wxcollect.ui.config.model

import com.yupao.data.protocol.Resource
import com.yupao.wxcollect.constant.WECHAT_AUTH_INFO_NAME
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.ProducerScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import java.io.File
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 10:13
 * @Description 复制UserAuthFile，不会重复复制
 */
class CopyUserAuthFileUseCase @Inject constructor(){

    operator fun invoke(
        filePath: String?,
        destDir: String?,
        dispatcher: CoroutineDispatcher?,
        maxTryCount: Int = 0
    ): Flow<Resource<Boolean>> {
        return callbackFlow<Resource<Boolean>> {
            performCopyFile(
                filePath = filePath,
                destDir = destDir,
                scope = this,
                tryCount = 0, maxTryCount = maxTryCount)
            awaitClose {
                YLog.i("CopyUserAuthFileUseCase", "invoke: close")
            }
        }.catch { e ->
            emit(Resource.Error(exception = e))
        }.flowOn(dispatcher ?: Dispatchers.IO)
    }

    private fun performCopyFile(
        filePath: String?,
        destDir: String?,
        scope: ProducerScope<Resource<Boolean>>,
        tryCount: Int = 0,
        maxTryCount: Int = 0,
    ) {
        val callback: ((Boolean) -> Unit) = { result ->
            if (result) {
                scope.trySend(Resource.Success(true))
            } else {
                if (tryCount >= maxTryCount) {
                    scope.trySend(Resource.Error(errorMsg = "文件拷贝失败"))
                } else {
                    YLog.i("CopyUserAuthFileUseCase", "performCopyFile: tryCount = $tryCount")
                    performCopyFile(filePath, destDir, scope, tryCount + 1, maxTryCount)
                }
            }
        }
        copyUserAuthFile(filePath, destDir, callback)
    }

    private fun copyUserAuthFile(
        filePath: String?,
        destDir: String?,
        callback: ((Boolean) -> Unit)
    ) {
        filePath ?: return callback.invoke(false)
        destDir ?: return callback.invoke(false)
        File(destDir, WECHAT_AUTH_INFO_NAME).delete()
        WechatFileUtil.copyFromOtherProcess(filePath, destDir, callback)
    }

    private fun checkFileExist(destDir: String?): File? {
        return File(destDir, WECHAT_AUTH_INFO_NAME).takeIf { it.exists() }
    }
}