package com.yupao.wxcollect.ui.databinder

import android.widget.TextView
import androidx.databinding.BindingAdapter

/**
 * <AUTHOR>
 * @Date 2023/4/11/011 14:40
 * @Description
 */
class TextBindingAdapter {

    @BindingAdapter("android:drawableLeft")
    fun setDrawableLeft(view: TextView?, resId: Int?) {
        view?.setCompoundDrawablesWithIntrinsicBounds(resId ?: 0, 0, 0, 0)
    }

    @BindingAdapter("android:drawableRight")
    fun setDrawableRight(view: TextView?, resId: Int?) {
        view?.setCompoundDrawablesWithIntrinsicBounds(0, 0, resId ?: 0, 0)
    }

    @BindingAdapter("android:drawableTop")
    fun setDrawableTop(view: TextView?, resId: Int?) {
        view?.setCompoundDrawablesWithIntrinsicBounds(0, resId ?: 0, 0, 0)
    }

    @BindingAdapter("android:drawableBottom")
    fun setDrawableBottom(view: TextView?, resId: Int?) {
        view?.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, resId ?: 0)
    }
}