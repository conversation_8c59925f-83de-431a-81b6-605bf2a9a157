package com.yupao.wxcollect.ui.upload_node

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.ItemUploadNodeBinding
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.ui.entity.TaskUIState
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:49
 * @Description 上传节点
 */
class UploadNodeAdapter(
    private val appType: String?,
    private val context: Context,
    private val callback: DiffItemCallback = DiffItemCallback()
) : ListAdapter<TaskUIState, NodeViewHolder>(callback)  {

    private fun setupNodeText(textView: TextView, text: String?, isSucceed: Boolean) {
        textView.text = text
        if (isSucceed) {
            textView.setTextColor(textView.context.resources.getColor(R.color.core))
        } else {
            textView.setTextColor(Color.RED)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NodeViewHolder {
        val binding = DataBindingUtil.inflate<ItemUploadNodeBinding>(
            LayoutInflater.from(parent.context),
            R.layout.item_upload_node,
            parent,
            false
        )
        return NodeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: NodeViewHolder, position: Int) {
        val item = getItem(position) ?: return
        holder.bind(item)
        when (appType) {
            ReportConfig.APP_MAIN -> {
                holder.dataBinding.apply {
                    msg.setBackgroundColor(context.resources.getColor(R.color.app_main_background))
                    setupNodeText(msg, item.msg, item.isSucceed)
                    executePendingBindings()
                }
            }
            ReportConfig.APP_SUB -> {
                holder.dataBinding.apply {
                    setupNodeText(msg, item.msg, item.isSucceed)
                    executePendingBindings()
                }
            }
            else -> {
                holder.dataBinding.apply {
                    if (item.appType == ReportConfig.APP_MAIN) {
                        msg.setBackgroundColor(context.resources.getColor(R.color.app_main_background))
                    } else {
                        msg.setBackgroundColor(Color.WHITE)
                    }
                    setupNodeText(msg, item.msg, item.isSucceed)
                    executePendingBindings()
                }
            }
        }
    }
}

class NodeViewHolder(val dataBinding: ItemUploadNodeBinding): RecyclerView.ViewHolder(dataBinding.root) {

    private var item: TaskUIState? = null
    fun bind(item: TaskUIState) {
        this.item = item
    }

    init {
        dataBinding.root.setOnClickListener {
            YLog.i("UploadNodeAdapter", "${item?.msg} unique: ${item?.unique}")
        }
    }
}

class DiffItemCallback : DiffUtil.ItemCallback<TaskUIState>() {
    override fun areItemsTheSame(oldItem: TaskUIState, newItem: TaskUIState): Boolean {
        return oldItem.unique == newItem.unique
    }

    override fun areContentsTheSame(oldItem: TaskUIState, newItem: TaskUIState): Boolean {
        return oldItem == newItem
    }

}