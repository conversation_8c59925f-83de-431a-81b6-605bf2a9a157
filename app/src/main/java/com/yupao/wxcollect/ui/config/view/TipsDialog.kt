package com.yupao.wxcollect.ui.config.view

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.DialogTipsBinding
import com.yupao.scafold.basebinding.AnimConfig
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2

/**
 * <AUTHOR>
 * @Date 2023年3月27日 09:46:57
 * @Description 提示弹窗
 */
class TipsDialog : DialogFragment() {

    /**
     * databinding
     */
    private var _binding: DialogTipsBinding? = null

    private var content: String? = null

    companion object {
        fun newInstance(content: String): TipsDialog {
            val fragment = TipsDialog()
            fragment.content = content
            return fragment
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.apply {
            AnimConfig.initShowAnim(AnimConfig.ANIM_TYPE_CENTER, this)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BindViewMangerV2.getViewDataBinding(
            viewLifecycleOwner,
            inflater,
            container,
            DataBindingConfigV2(R.layout.dialog_tips, 0, null)
        )
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        _binding?.content?.text = content
        _binding?.sure?.setOnClickListener {
            dismiss()
        }
        dialog?.setCanceledOnTouchOutside(false)
        dialog?.setCancelable(false)
    }

    override fun onStart() {
        super.onStart()
        clearPadding()
    }

    /**
     * 去掉padding，修复无法全屏
     */
    private fun clearPadding() {
        dialog?.window?.let { window ->
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//            window.setWindowAnimations(R.style.AnimDownToTop)
            requireActivity().windowManager.defaultDisplay.getMetrics(DisplayMetrics())
            window.decorView.setPadding(0, 0, 0, 0)
            val params =
                if (window.attributes != null) window.attributes else WindowManager.LayoutParams()
            params.dimAmount = 0f
            params.gravity = Gravity.CENTER
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = params
        }
    }
}

