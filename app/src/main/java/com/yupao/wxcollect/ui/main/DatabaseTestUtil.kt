package com.yupao.wxcollect.ui.main

import com.yupao.utils.log.LogUtil
import com.yupao.wxcollect.App
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteDatabaseHook
import java.io.File
import java.security.MessageDigest

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2023/9/21/021</p>
 *
 * <AUTHOR>
 *
 * 1035CCD 4D2E7B5F1328C9E97F6B856CF
 */
object DatabaseTestUtil {
    // /sdcard/MicroMsg/90ecc83077682dc1fd3eaa2e8b134243/FTS5IndexMicroMsg_encrypt.db
    const val databasePath = "/sdcard/MicroMsg/90ecc83077682dc1fd3eaa2e8b134243/FTS5IndexMicroMsg_encrypt.db"

    fun perform() {
        val file = File(databasePath)
        if (!file.exists()) {
            LogUtil.d("DatabaseTestUtil", "perform: not exist = $databasePath")
        }
        val uid = "-1572854175"
//        val uid = (-1572854175L and 4294967295L).toString()
        // MEID: 99001190942670
        //IMEI1: 864560048226706
        //IMEI2:864560049286709
        val imeiList = listOf(
            "1234567890ABCDEF",
            "99001190942670",
            "864560048226706",
            "864560049286709",
        )
        val wxid = "wxid_gf08j0dzplz312"
        imeiList.forEach {
            openDb(uid, it, wxid)
        }
    }

    fun openDb(uid: String?, imei: String, wxid: String) {
        LogUtil.d("DatabaseTestUtil", "openDb: uid = $uid, imei = $imei, wxid = $wxid")
        val appendText = uid + imei + wxid
        val md5 = md5(appendText)
        val password: String
        if (md5 != null && md5.length >= 7) {
            password = md5.substring(0, 7).toLowerCase()
            LogUtil.d("DatabaseTestUtil", "openDb password $appendText => $password")
        } else {
            LogUtil.d("DatabaseTestUtil", "openDb invalid password")
            return
        }
        SQLiteDatabase.loadLibs(App.getContext())
        val hook = object : SQLiteDatabaseHook {
            override fun preKey(database: SQLiteDatabase) {}

            override fun postKey(database: SQLiteDatabase) {
//                database.rawExecSQL("PRAGMA cipher_migrate;")  //最关键的一句！！！
                database.rawExecSQL("PRAGMA cipher = 'aes-256-cbc';")
                database.rawExecSQL("PRAGMA cipher_use_hmac = ON;")
                database.rawExecSQL("PRAGMA cipher_page_size = 4096;")
                database.rawExecSQL("PRAGMA kdf_iter = 64000;")
                // PRAGMA cipher = 'aes-256-cbc';
                //PRAGMA cipher_use_hmac = ON;
                //PRAGMA cipher_page_size = 4096;
                //PRAGMA kdf_iter = 64000;
//                database.execSQL("ATTACH DATABASE 'EnMicroMsg-decrypted.db' AS wechatdecrypted KEY '';")
//                database.execSQL("SELECT sqlcipher_export( 'wechatdecrypted' );")
//                database.execSQL("DETACH DATABASE wechatdecrypted;")
            }
        }
        try {
            val database = SQLiteDatabase.openDatabase(
                databasePath,
                password,
                null,
                SQLiteDatabase.NO_LOCALIZED_COLLATORS,
                hook
            )
            val open = database.isOpen
            LogUtil.d("DatabaseTestUtil", "openDb-result: $open")
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtil.d("DatabaseTestUtil", "openDb: $e")
        }
    }

    private fun md5(s: String): String? {
        var md5: MessageDigest? = null
        try {
            md5 = MessageDigest.getInstance("MD5")
            md5!!.update(s.toByteArray(charset("UTF-8")))
            val encryption = md5.digest()//加密
            val sb = StringBuffer()
            for (i in encryption.indices) {
                if (Integer.toHexString(0xff and encryption[i].toInt()).length == 1) {
                    sb.append("0").append(Integer.toHexString(0xff and encryption[i].toInt()))
                } else {
                    sb.append(Integer.toHexString(0xff and encryption[i].toInt()))
                }
            }
            return sb.toString()
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

}