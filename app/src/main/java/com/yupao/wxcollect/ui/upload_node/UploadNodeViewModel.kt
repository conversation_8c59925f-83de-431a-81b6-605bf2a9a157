package com.yupao.wxcollect.ui.upload_node

import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.yupao.scafold.ktx.mapFilterSuccess
import com.yupao.scafold.ktx.signalFlow
import com.yupao.wxcollect.service.procedure.task.TaskCode
import com.yupao.wxcollect.service.record.TaskRecordHandler
import com.yupao.wxcollect.ui.entity.TaskRecordEntity
import com.yupao.wxcollect.ui.entity.TaskRecordParamsModel
import com.yupao.wxcollect.ui.entity.TaskUIState
import com.yupao.ylog.YLog
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.runBlocking
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 16:13
 * @Description 上传节点
 */
@HiltViewModel
class UploadNodeViewModel @Inject constructor(
    private val resp: TaskRecordRep,
): ViewModel() {
    companion object {
        private const val TAG = "UploadNodeViewModel"

        const val AllText = "全部"
    }

    private val appTypeSignal = signalFlow<String?>()

    // 默认筛选类型
    private val defaultTypeList = listOf(TaskCode.WxGroupChat, TaskCode.WxGroup)

    // 错误容器需要监听的任务类型
    private val errorTaskCode = listOf(TaskCode.WxGroupChat, TaskCode.WxGroup, TaskCode.WxArticle)

    val appType = appTypeSignal.asLiveData(viewModelScope.coroutineContext, Long.MAX_VALUE)

    private val _filterType = MutableStateFlow<List<MenuUIState>>(emptyList())
    val filterType = _filterType.asStateFlow()

    fun updateFilterType(uiState: List<MenuUIState>) {
        _filterType.update { uiState }
    }

    private val _filterStatus = MutableStateFlow<MenuUIState?>(null)
    val filterStatus = _filterStatus.asStateFlow()

    fun updateFilterStatus(uiState: List<MenuUIState>) {
        _filterStatus.update { uiState.firstOrNull() }
    }

    private val _filterStatusUIState = MutableStateFlow(listOf(
        MenuUIState(AllText,true, AllText, 0),
        MenuUIState("成功",false, TaskRecordHandler.TypeSucceed, 1),
        MenuUIState("失败",false, TaskRecordHandler.TypeFailed, 2),
    ))

    val filterStatusUIState = _filterStatusUIState.asStateFlow()

    private val refreshDataFlow = appTypeSignal.flatMapLatest { appType ->
        resp.queryPage(TaskRecordParamsModel(
            appType = appType,
        ))
    }.mapFilterSuccess().onEach { list ->
        YLog.i(TAG, "refresh data: ${list?.size}")
        allDataFlow.update { list }
    }.flowOn(Dispatchers.IO)

    // 丢弃最新的数据
    private val updateSignal = MutableSharedFlow<Unit>(0,1, BufferOverflow.DROP_LATEST)

    private val updateDataFlow = updateSignal.flatMapLatest {
        resp.queryPage(TaskRecordParamsModel(
            appType = appType.value,
            lastId = allDataFlow.value?.firstOrNull()?.id
        ))
    }.mapFilterSuccess().onEach {
        YLog.i(TAG, "update data: ${it?.size}")
        val list = it ?: return@onEach
        val currList = allDataFlow.value?.toMutableList() ?: mutableListOf()
        list.reversed().forEach { entity ->
            val index = currList.indexOfFirst {
                it.unique == entity.unique
            }
            val findEntity = if (index >= 0) currList[index] else null
            if (findEntity != null) {
                val currId = findEntity.id
                if (currId == entity.id) {
                    YLog.d(TAG, "updateDataFlow: update repeat ${entity.name}: ${entity.msg}")
                    return@forEach
                }
                val newEntity = findEntity.copy(
                    id = entity.id,
                    msg = findEntity.msg + "\n${entity.msg}",
                    result = entity.result
                )
                currList[index] = newEntity
                YLog.d(TAG, "updateDataFlow: exist: $currId -> ${findEntity.id}")
            } else {
                currList.add(0, entity)
            }
        }
        allDataFlow.update { currList }
    }.flowOn(Dispatchers.IO)

    private val allDataFlow = MutableStateFlow<List<TaskRecordEntity>?>(null)

    private val filterDataFlow = combine(allDataFlow, filterStatus, filterType) { list, state, typeList ->
        YLog.i(TAG, "filter data: ${list?.size}, state: $state, typeList: $typeList")
        list?.filter {
            if (state?.unique != null && state.unique != AllText) {
                if (state.unique == it.result || (state.unique == TaskRecordHandler.TypeSucceed && it.result == TaskRecordHandler.TypeUnknown)) {
                } else {
                    return@filter false
                }
            }
            // 是否包含全部的
            val isContainAll = typeList.find { uiState ->
                uiState.unique == AllText
            } != null
            if (isContainAll) {
                return@filter true
            }
            typeList.forEach {  uiState ->
                if (uiState.text == it.name) {
                    return@filter true
                }
            }
            false
        } ?: emptyList()
    }

    // 筛选后的数据
    val dataFlow = filterDataFlow.map {
        it.map { model ->
            TaskUIState(
                unique = model.unique,
                isSucceed = model.result != TaskRecordHandler.TypeFailed,
                msg = model.msg.plus(if(model.isEnd()) "" else "\n ..."),
                appType = model.appType
            )
        }
    }.flowOn(Dispatchers.IO)

    // 根据任务列表取最新一个任务失败的错误原因
    val failedTaskFlow = allDataFlow.map {
        (it ?: emptyList()).groupBy { entity ->
            entity.taskCode ?: ""
        }.filter { entry ->
            errorTaskCode.contains(entry.key)
        }.mapNotNull { entry ->
            entry.value.firstOrNull { entity ->
                entity.result == TaskRecordHandler.TypeFailed || entity.result == TaskRecordHandler.TypeSucceed
            }?.let { entity ->
                // 如果最近一次成功则不再展示
                if (entity.result == TaskRecordHandler.TypeSucceed) {
                    return@let null
                }
                val msg = entity.msg
                val errorMsg = msg.substringAfter("推广失败: ").trim()
                "失败任务: ${entity.name} | 失败原因: $errorMsg"
            }
        }
    }.flowOn(Dispatchers.IO)

    // 任务类型列表
    val filterTypeUIState: StateFlow<List<MenuUIState>> = allDataFlow.mapNotNull {
        val list = it?.groupBy { entity ->
            entity.name
        }?.map { entry ->
            val entity = entry.value.firstOrNull()
            val unique = entity?.unique
            MenuUIState(
                text = entry.key,
                isChecked = false,
                unique = unique ?: entry.key,
                sort = entity?.taskSort ?: Int.MAX_VALUE,
                taskCode = entity?.taskCode,
            )
        } ?: emptyList()
        val sortList = list.sortedBy { uiState ->
            uiState.sort
        }
        YLog.i(TAG, "filterTypeUIState sortList: $sortList")
        listOf(MenuUIState(AllText, false, AllText, 0)) + sortList
    }
        .flowOn(Dispatchers.IO)
        .onEach {
            if (this.filterType.value.isEmpty() && it.isNotEmpty()) {
                val list = it.filter { uiState ->
                    val result = this.defaultTypeList.contains(uiState.taskCode)
                    if (result) {
                        uiState.isChecked = true
                    }
                    return@filter result
                }
                updateFilterType(list)
            }
        }
        .stateIn(viewModelScope, SharingStarted.Eagerly, emptyList())

    fun fetchAllData() {
        YLog.i("UploadNodeViewModel", "fetchAllData:")
        appTypeSignal.tryEmit(appType.value)
    }

    fun setupAppType(appType: String?) {
        appTypeSignal.tryEmit(appType)
        YLog.i(TAG, "setupAppType")
    }

    fun updateRecord() {
        updateSignal.tryEmit(Unit)
        YLog.i(TAG, "updateRecord")
    }

    init {
        refreshDataFlow.launchIn(viewModelScope)
        updateDataFlow.launchIn(viewModelScope)
    }
}


val list = MutableStateFlow<List<String>?>(null)
fun main() {
    runBlocking {
        async(Dispatchers.IO) {
            list.collectLatest {
                println("collectLatest: $it")
            }
        }

        delay(100)
        list.update { listOf("1", "2", "3") }

        delay(100)
        list.value?.toMutableList()?.apply {
            this[1] = "6"
            list.update { this }
        }
        println("emit")
    }
    println("over")
}