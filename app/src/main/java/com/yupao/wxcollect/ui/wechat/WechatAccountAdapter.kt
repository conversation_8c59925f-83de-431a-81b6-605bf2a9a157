package com.yupao.wxcollect.ui.wechat

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.yupao.execute.wxcollect.R

class WechatAccountAdapter(
    private val onItemClick: (WechatAccount) -> Unit
) : ListAdapter<WechatAccount, WechatAccountAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_wechat_account, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvNickname: TextView = itemView.findViewById(R.id.tvNickname)
        private val tvWechatId: TextView = itemView.findViewById(R.id.tvWechatId)

        fun bind(account: WechatAccount) {
            tvNickname.text = "微信昵称：${account.nickname}"
            tvWechatId.text = "微信号：${account.alias}"

            itemView.setOnClickListener {
                onItemClick(account)
            }
        }
    }

    private class DiffCallback : DiffUtil.ItemCallback<WechatAccount>() {
        override fun areItemsTheSame(oldItem: WechatAccount, newItem: WechatAccount): Boolean {
            return oldItem.alias == newItem.alias
        }

        override fun areContentsTheSame(oldItem: WechatAccount, newItem: WechatAccount): Boolean {
            return oldItem == newItem
        }
    }
}