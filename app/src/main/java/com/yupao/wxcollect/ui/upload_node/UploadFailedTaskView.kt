package com.yupao.wxcollect.ui.upload_node

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.fragment.app.FragmentManager
import com.rwz.hook.utils.ToastUtil
import com.yupao.execute.wxcollect.R
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.ui.dialog.WarnDialog
import com.yupao.wxcollect.ui.dialog.WarnEntity
import com.yupao.wxcollect.util.CmdUtil

/**
 * 执行失败的任务View
 *
 * <p>创建时间：2024/10/29/029</p>
 *
 * <AUTHOR>
 */
class UploadFailedTaskView @JvmOverloads constructor(
    private val context: Context,
    attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private val contentView by lazy {
        LayoutInflater.from(context).inflate(R.layout.layout_node_failed_task, null, false)
    }

    private val itemContainer by lazy {
        contentView.findViewById<ViewGroup>(R.id.itemContainer)
    }

    init {
        addView(contentView)
    }

    fun initClick(fm: FragmentManager) {
        contentView.findViewById<View>(R.id.reboot).setOnClickListener {
            WarnDialog.newInstance(WarnEntity("确定重启导演App吗？") {
                val targetApp = CoreClient.servicePackageName
                val coreService = CoreClient.serviceName
                CmdUtil.executeAsync("am force-stop $targetApp ; am startservice -n ${targetApp}/$coreService") { result ->
                    if (result.isOK()) {
                        ToastUtil.getInstance().showShortSingle("已重启")
                    } else {
                        ToastUtil.getInstance().showShortSingle("重启失败")
                    }
                }
            }).show(
                fm,
                "WarnDialog"
            )
        }
    }

    fun updateFailedTask(list: List<String>) {
        if (list.isEmpty()) {
            visibility = View.GONE
            return
        }
        visibility = View.VISIBLE
        val count = itemContainer.childCount
        for (i in count - 1 downTo list.size) {
            getChildAt(i)?.visibility = View.GONE
        }
        list.forEachIndexed { index, s ->
            if (index < count) {
                val itemView = (itemContainer.getChildAt(index) as? TextView)
                itemView?.visibility = View.VISIBLE
                itemView?.text = s
            } else {
                val itemView = LayoutInflater.from(context).inflate(R.layout.item_node_failed_task, null, false) as TextView
                itemView.text = s
                itemContainer.addView(itemView)
            }
        }
        requestLayout()
        invalidate()
    }
}