package com.yupao.wxcollect.ui.task_manager

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yupao.scafold.ktx.nullStateIn
import com.yupao.scafold.ktx.signalFlow
import com.yupao.wxcollect.ui.upload_node.UploadNodeViewModel
import com.yupao.ylog.YLog
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/8/7/007</p>
 *
 * <AUTHOR>
 */
@HiltViewModel
class TabViewModel @Inject constructor(
): ViewModel() {

    private val appTypeSignal = signalFlow<String?>()

    val appType = appTypeSignal.nullStateIn(viewModelScope)

    fun setupAppType(appType: String?) {
        appTypeSignal.tryEmit(appType)
    }
}