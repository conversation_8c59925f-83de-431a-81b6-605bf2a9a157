package com.yupao.wxcollect.ui.upload_result

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.yupao.data.protocol.doError
import com.yupao.data.protocol.doLoading
import com.yupao.data.protocol.doSuccess
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.FragmentResultListBinding
import com.yupao.scafold.baseui.LoadingView
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.widget.recyclerview.xrecyclerview.OnLoadMoreListener
import com.yupao.widget.recyclerview.xrecyclerview.OnRefreshListener
import com.yupao.widget.recyclerview.xrecyclerview.XRecyclerView
import com.yupao.wxcollect.service.procedure.task.ReportType
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import me.zhanghai.android.files.util.viewModels

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:22
 * @Description 上传监听
 */
@AndroidEntryPoint
class UploadResultFragment: Fragment() {

    companion object {
        private const val KEY_APP_TYPE = "appType"

        private const val KEY_REPORT_TYPE = "reportType"

        fun newInstance(appType: String?, @ReportType reportType: Int?): Fragment {
            val fragment = UploadResultFragment()
            fragment.arguments = bundleOf(
                KEY_APP_TYPE to appType,
                KEY_REPORT_TYPE to reportType,
            )
            return fragment
        }
    }

    private val appType by lazy {
        arguments?.getString(KEY_APP_TYPE)
    }

    @ReportType
    private val reportType by lazy {
        arguments?.getInt(KEY_REPORT_TYPE)
    }

    private val adapter by lazy { UploadResultAdapter() }

    private val viewModel by viewModels<UploadResultViewModel>()

    var binding: FragmentResultListBinding? = null

    private val loadingView: LoadingView? by lazy { binding?.loading }

    private val recyclerView by lazy {
        binding?.list
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_result_list, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView?.apply {
            this.anchorAdapter(<EMAIL>)
            setOnRefreshListener(onRefreshListener)
            setOnLoadMoreListener(onLoadMoreListener)
        }
        initObserve()
        showLoading()
        adapter.setEmptyView(R.layout.layout_empty_view)
        viewModel.initParams(appType, reportType)
        viewModel.fetchData()
    }

    private val onRefreshListener = object : OnRefreshListener{
        override fun onRefresh(xRecyclerView: XRecyclerView) {
            viewModel.refresh()
        }
    }

    private val onLoadMoreListener = object : OnLoadMoreListener{
        override fun onLoadMore(xRecyclerView: XRecyclerView) {
            viewModel.loadMore()
        }
    }

    private fun showLoading() {
        loadingView?.startLoading()
        loadingView?.visibility = View.VISIBLE
    }

    private fun hindLoading() {
        loadingView?.stopLoading()
        loadingView?.visibility = View.GONE
    }

    private fun initObserve() {
        lifecycleScope.launch {
            viewModel.dataFlow.collectLatest {
                YLog.i("UploadResultActivity", "dataFlow:  = $it")
                it.doLoading {
                    showLoading()
                }
                it.doSuccess { res ->
                    hindLoading()
                    recyclerView?.finishRefreshAndLoadMore()
                    val data = res.data ?: return@doSuccess
                    if (viewModel.isRefresh()) {
                        adapter.setList(data)
                    } else {
                        adapter.addData(data)
                    }
                }
                it.doError {
                    hindLoading()
                    recyclerView?.finishRefreshAndLoadMore()
                    ToastUtilsAssist.showCustomToast(requireContext(), "数据获取失败")
                }
            }
        }
    }

}