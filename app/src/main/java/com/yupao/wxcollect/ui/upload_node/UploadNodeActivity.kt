package com.yupao.wxcollect.ui.upload_node

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import com.yupao.execute.wxcollect.BR
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.ActivityUploadNodeBinding
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.ArgsEntity
import com.yupao.transmit.handle.IMessageHandler
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.ui.BaseActivity
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.viewpager.IFragmentFactory
import com.yupao.wxcollect.ui.viewpager.ViewPagerAdapter
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:22
 * @Description 上传节点
 */
@AndroidEntryPoint
class UploadNodeActivity: BaseActivity() {
    companion object {
        private const val TAG = "UploadNodeActivity"

        @JvmStatic
        fun start(context: Context) {
            if (IReportClientManager.instance.checkClientRegister()) {
                val starter = Intent(context, UploadNodeActivity::class.java)
                context.startActivity(starter)
            } else {
                Toast.makeText(context, "服务正在启动，请重试", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private val viewModel by viewModels<UploadNodeViewModel>()

    private lateinit var binding: ActivityUploadNodeBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = BindViewMangerV2.bindActivity<ActivityUploadNodeBinding>(
            this,
            DataBindingConfigV2(
                R.layout.activity_upload_node,
                BR.vm,
                viewModel
            ).addBindingParam(BR.click, ClickProxy())
                .addBindingParam(BR.companion, ReportConfig.Companion)
        )
        binding.executePendingBindings()
        initView()
        initObserve()
        // 默认选择全部
        viewModel.setupAppType(null)
    }

    private fun initView() {
        findViewById<TextView>(R.id.title).text = "上传执行"
        findViewById<View>(R.id.back).setOnClickListener {
            onBackPressed()
        }
        with(binding.viewPager) {
            adapter = ViewPagerAdapter(this@UploadNodeActivity, object : IFragmentFactory {
                override fun create(index: Int?): Fragment {
                    return UploadNodeFragment.newInstance(
                        when(index) {
                            1 -> ReportConfig.APP_MAIN
                            2 -> ReportConfig.APP_SUB
                            else -> null
                        }
                    )
                }

                override fun size(): Int {
                    return 3
                }
            })
            isUserInputEnabled = false
        }
    }

    private var updateTime = 0L

    private val updateRecordHandler = object : IMessageHandler {
        override fun handle(fromHost: String?, args: ArgsEntity?) {
            viewModel.updateRecord()
            updateTime = TimeUtil.getCurrTime()
        }
    }

    private fun initObserve() {
        viewModel.appType.observe(this) {
            when (it) {
                ReportConfig.APP_MAIN -> binding.viewPager.setCurrentItem(1, false)
                ReportConfig.APP_SUB -> binding.viewPager.setCurrentItem(2, false)
                else -> binding.viewPager.setCurrentItem(0, false)
            }
        }
        val period = 30_000L
        lifecycle.coroutineScope.launch(Dispatchers.IO) {
            while (isActive) {
                delay(period)
                if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                    YLog.i(TAG, "updateTask: $updateTime")
                    viewModel.takeIf { TimeUtil.getCurrTime() - updateTime > period }?.fetchAllData()
                    IReportClientManager.instance.checkClientRegister()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        YLog.i(TAG, "onResume")
        MessageManager.handler.registerHandler(MessageCode.UpdateRecord, updateRecordHandler)
    }

    override fun onPause() {
        super.onPause()
        YLog.i(TAG, "onPause")
        MessageManager.handler.unregisterHandler(MessageCode.UpdateRecord)
    }

    inner class ClickProxy {

        fun onTabClick(appType: String?) {
            viewModel.setupAppType(appType)
        }
    }
}