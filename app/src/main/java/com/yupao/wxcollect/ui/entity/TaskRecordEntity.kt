package com.yupao.wxcollect.ui.entity

import com.yupao.wxcollect.service.record.TaskRecordHandler

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/6/6/006</p>
 *
 * <AUTHOR>
 */
data class TaskRecordEntity(
    var id: Int?,
    val unique: String?,
    var result: String,
    var msg: String,
    var errorMsg: String?,
    val appType: String?,
    val name: String,
    val taskSort: Int?,
    val taskCode: String?,
) {
    fun isEnd() = result != TaskRecordHandler.TypeUnknown
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TaskRecordEntity

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id ?: 0
    }


}