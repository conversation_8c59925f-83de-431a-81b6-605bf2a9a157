package com.yupao.wxcollect.ui.config.model

import com.yupao.data.protocol.Resource
import com.yupao.wxcollect.constant.WECHAT_DATABASE_BASE_PATH
import com.yupao.wxcollect.constant.WECHAT_DATABASE_DIR
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.util.WechatFileUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 9:27
 * @Description 获取数据库路径
 */
class FetchDatabasePathUseCase @Inject constructor() {

    operator fun invoke(appType: String?, dispatcher: CoroutineDispatcher?): Flow<Resource<List<String>>> {
        return flow<Resource<List<String>>> {
            emit(Resource.Loading)
            val baseDir = String.format(
                WECHAT_DATABASE_BASE_PATH,
                if (appType == ReportConfig.APP_MAIN) "0" else "999"
            )
            val list = getDatabaseName(baseDir).firstOrNull()
            YLog.i("FetchDatabasePathUseCase", "invoke: list = $list")
            if (list == null || list.isEmpty()) {
                emit(Resource.Error(errorMsg = "未找到数据库目录"))
                return@flow
            }
            val result = getWechatDatabasePath(list, appType ?: ReportConfig.APP_MAIN)
            YLog.i("FetchDatabasePathUseCase", "invoke: result = $result")
            emit(Resource.Success(result))
        }.catch { e ->
            emit(Resource.Error(exception = e))
        }
            .flowOn(dispatcher  ?: Dispatchers.IO)
    }

    private suspend fun getDatabaseName(baseDir: String) = callbackFlow {
        WechatFileUtil.getDatabaseName(baseDir) {
            trySend(it)
        }
        awaitClose {
            YLog.i("FetchDatabasePathUseCase", "getDatabaseName: ")
        }
    }

    private suspend fun getWechatDatabasePath(nameList: List<String>, appType: String): List<String> {
        return nameList.flatMap {
            val dir = String.format(WECHAT_DATABASE_DIR, if (appType == ReportConfig.APP_MAIN) "0" else "999", it)
            WechatFileUtil.fetchAllDatabase(dir)
        }
    }
}