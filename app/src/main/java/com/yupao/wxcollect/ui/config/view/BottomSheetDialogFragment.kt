package com.yupao.wxcollect.ui.config.view

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.*
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import com.yupao.scafold.basebinding.AnimConfig
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.DialogfragmentBottomSheetBinding
import com.yupao.wxcollect.ui.config.entity.BottomSheetEntity

/**
 * <AUTHOR>
 * @Date 2023年3月27日 09:46:57
 * @Description 底部弹窗菜单
 */
class BottomSheetDialogFragment : DialogFragment() {

    /**
     * 点击处理
     */
    private val clickProxy = ClickProxy()

    /**
     * 底部弹窗适配器
     */
    private val bottomSheetAdapter: BottomSheetAdapter by lazy {
        BottomSheetAdapter(data = data).apply {
            setOnItemClickListener{ _, _, pos ->
                onClickItem?.invoke(data.getOrNull(pos))
                dismissAllowingStateLoss()
            }
        }
    }

    /**
     * databinding
     */
    private var _binding: DialogfragmentBottomSheetBinding? = null

    /**
     * 点击菜单回调
     */
    private var onClickItem: ((BottomSheetEntity?) -> Unit)? = null

    /**
     * 菜单数据
     */
    private var data: MutableList<BottomSheetEntity>? = null

    companion object {

        fun showBottomSheet(
            activity: AppCompatActivity?,
            data: MutableList<BottomSheetEntity>? = null,
            onClickItem: ((BottomSheetEntity?) -> Unit)? = null,
        ) {
            activity?.let {
                BottomSheetDialogFragment().apply {
                    this.onClickItem = onClickItem
                    this.data = data
                }.show(activity.supportFragmentManager, "SetTopChangeAreaDialogFragment")
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.apply {
            AnimConfig.initShowAnim(AnimConfig.ANIM_TYPE_CENTER, this)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BindViewMangerV2.getViewDataBinding(
            viewLifecycleOwner,
            inflater,
            container,
            DataBindingConfigV2(R.layout.dialogfragment_bottom_sheet, 0, null)
        )
        _binding?.clickProxy = clickProxy
        _binding?.adapter = bottomSheetAdapter
        return _binding?.root
    }

    override fun onStart() {
        super.onStart()
        clearPadding()
    }

    /**
     * 去掉padding，修复无法全屏
     */
    private fun clearPadding() {
        dialog?.window?.let { window ->
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//            window.setWindowAnimations(R.style.AnimDownToTop)
            requireActivity().windowManager.defaultDisplay.getMetrics(DisplayMetrics())
            window.decorView.setPadding(0, 0, 0, 0)
            val params =
                if (window.attributes != null) window.attributes else WindowManager.LayoutParams()
            params.dimAmount = 0f
            params.gravity = Gravity.BOTTOM
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = params
        }
    }

    inner class ClickProxy {
        /**
         * 点击菜单
         */
        fun clickMenuItem(entity: BottomSheetEntity?) {
            onClickItem?.invoke(entity)
            dismissAllowingStateLoss()
        }

        /**
         * 点击空白区域
         */
        fun clickEmptyArea() {
            dismissAllowingStateLoss()
        }
    }
}
