package com.yupao.wxcollect.ui.config.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.yupao.data.protocol.data
import com.yupao.data.protocol.doError
import com.yupao.data.protocol.doSuccess
import com.yupao.execute.wxcollect.BR
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.FragmentConfigBinding
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.wxcollect.service.IReportServiceManager
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.task.ReportType
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.config.entity.BottomSheetEntity
import com.yupao.wxcollect.ui.config.viewmodel.ConfigViewModel
import com.yupao.wxcollect.ui.main.MainActivity
import com.yupao.wxcollect.ui.main.PermissionHelp
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @Date 2023/4/7/007 14:45
 * @Description
 */
@AndroidEntryPoint
class MainConfigFragment: Fragment() {

    companion object {

        private const val KEY_APP_TYPE = "appType"

        fun newInstance(@ReportType reportType: String): Fragment {
            val fragment = MainConfigFragment()
            fragment.arguments = bundleOf(
                KEY_APP_TYPE to reportType
            )
            return fragment
        }
    }

    private val viewModel by viewModels<ConfigViewModel>()

    private val activityViewModel by lazy {
        ViewModelProvider(requireActivity())[ConfigViewModel::class.java]
    }

    @ReportType
    private val appType by lazy {
        arguments?.getString(KEY_APP_TYPE)
    }

    private lateinit var binding: FragmentConfigBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = BindViewMangerV2.bindFragment(
            lifecycleOwner = this,
            inflater = inflater,
            container = container,
            dataBindingConfig = DataBindingConfigV2(
                R.layout.fragment_config,
                BR.vm,
                viewModel
            ).addBindingParam(BR.click, ClickProxy())
        )
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initObserve()
        viewModel.setupAppType(appType)
        PermissionHelp.request(requireActivity(), lifecycleScope) {
            YLog.i("MainConfigFragment", "onViewCreated: $it")
            onPermissionAllowed()
        }
    }

    override fun onResume() {
        super.onResume()
        activityViewModel.let {
            if (viewModel.telNo.value.isNullOrEmpty() && it.telNo.value != null) {
                viewModel.telNo.value = it.telNo.value
            }
        }
    }

    private fun initObserve() {
        activityViewModel.telNo.observe(viewLifecycleOwner) {
            viewModel.telNo.value = it
        }
        viewModel.isAtMostHi.observe(viewLifecycleOwner) {
            val imeiList = viewModel.imeiFlow.value?.data
            YLog.i("MainConfigFragment", "initObserve: isAtMostHi = $it, imeiList = $imeiList")
            if (imeiList.isNullOrEmpty()) {
                return@observe
            }
            if (it) {
                viewModel.imei.postValue(imeiList.lastOrNull())
            } else {
                viewModel.imei.postValue(imeiList.firstOrNull())
            }
            val passwordList = viewModel.passwordFlow.value?.data
            YLog.i("MainConfigFragment", "initObserve: isAtMostHi = $it, passwordList = $passwordList")
            if (passwordList.isNullOrEmpty()) {
                return@observe
            }
            if (it) {
                viewModel.password.postValue(passwordList.lastOrNull()?.password)
            } else {
                viewModel.password.postValue(passwordList.firstOrNull()?.password)
            }
        }
        YLog.i("MainConfigFragment", "initObserve:")
        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.passwordFlow.collectLatest {
                YLog.i("MainConfigFragment", "passwordFlow:  = $it")
                it?.doError {  error ->
                    ToastUtilsAssist.showCustomToast(requireContext(), error.errorMsg)
                    hindLoading()
                    viewModel.fetchDatabasePath()
                }
                it?.doSuccess { result ->
                    YLog.i("passwordFlow: $result")
                    // 优先通过imei中查找设置的值
                    val entity = result.data?.find { it.imei == viewModel.imei.value }
                    YLog.i("passwordFlow password: ${entity?.password}")
                    if (entity != null) {
                        viewModel.password.postValue(entity.password)
                    } else if (viewModel.password.value.isNullOrEmpty()) {
                        if (viewModel.isAtMostHi.value == true) {
                            viewModel.password.postValue(result.data?.lastOrNull()?.password)
                        } else {
                            viewModel.password.postValue(result.data?.firstOrNull()?.password)
                        }
                    }
                    hindLoading()
                    // 需要在IMEI之后获取数据库，因为数据库如果通过cmd获取不到时，需要执行远程获取，但是没有提交一次FileJob会失败
                    viewModel.fetchDatabasePath()
                }
            }
        }
        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.databaseFlow.collectLatest {
                it?.doError {  error ->
                    ToastUtilsAssist.showCustomToast(requireContext(), error.errorMsg)
                }
                it?.doSuccess { result ->
                    YLog.i("databaseFlow: $result")
                    if (viewModel.databasePath.value.isNullOrEmpty()) {
                        viewModel.databasePath.postValue(it.data?.firstOrNull())
                    }
                }
            }
        }
        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.imeiFlow.collectLatest {
                it?.doError {  error ->
                    ToastUtilsAssist.showCustomToast(requireContext(), error.errorMsg)
                    hindLoading()
                }
                it?.doSuccess { result ->
                    YLog.i("ConfigViewModel", "imeiFlow:  = $result")
                    val imei = if (viewModel.isAtMostHi.value == true) {
                        it.data?.lastOrNull()
                    } else {
                        it.data?.firstOrNull()
                    }
                    if (!imei.isNullOrEmpty()) {
                        if (viewModel.imei.value.isNullOrEmpty()) {
                            viewModel.imei.postValue(imei)
                        }
                        viewModel.createPassword()
                    } else {
                        ToastUtilsAssist.showCustomToast(requireContext(), "未查找到IMEI")
                        hindLoading()
                    }
                }
            }
        }
        lifecycleScope.launch(Dispatchers.Main) {
            viewModel.submitConfigFlow.collectLatest {
                it.doError {  error ->
                    ToastUtilsAssist.showCustomToast(requireContext(), error.errorMsg)
                    hindLoading()
                }
                it.doSuccess { result ->
                    val config: ReportConfig? = result.data
                    val msg = if(config != null) "提交成功" else "提交失败"
                    Toast.makeText(requireContext(), msg, Toast.LENGTH_SHORT).show()
                    hindLoading()
                }
            }
        }
        lifecycleScope.launch(Dispatchers.IO) {
            viewModel.queryConfigFlow.collectLatest {
                YLog.i("MainConfigFragment", "queryConfigFlow:  = $it")
                withContext(Dispatchers.Main) {
                    it?.doError {  error ->
                        ToastUtilsAssist.showCustomToast(requireContext(), error.errorMsg)
                        performFetchData()
                    }
                    it?.doSuccess { result ->
                        viewModel.updateReportConfig(result.data)
                        performFetchData()
                    }
                }
            }
        }
    }

    private fun showLoading() {
        binding.loading.run {
            startLoading()
            visibility = View.VISIBLE
        }
    }

    private fun hindLoading() {
        YLog.i("MainConfigFragment", "hindLoading")
        binding.loading.run {
            stopLoading()
            visibility = View.GONE
        }
    }

    private fun onPermissionAllowed() {
        YLog.i("MainConfigFragment", "onPermissionAllowed")
        viewModel.query()
    }

    private fun performFetchData() {
        viewModel.fetchImei()
    }

    private fun startService(config: ReportConfig) {
        // 启动服务
        IReportClientManager.instance.startService(
            requireContext(),
            from = IReportServiceManager.KEY_FROM_APP_CONFIG,
            config = NotificationConfig(
                pendingActivityPath = MainActivity::class.java.name
            ),
            reportConfig = config,
        )
    }

    inner class ClickProxy {
        fun switchType() {
            lifecycleScope.launch(Dispatchers.Main) {
                val list = viewModel.passwordFlow.value?.data?.map {
                    BottomSheetEntity(it.imei, it.password)
                }
                if (list == null || list.isEmpty()) {
                    return@launch
                }
                BottomSheetDialogFragment.showBottomSheet(
                    requireActivity() as? AppCompatActivity,
                    list.toMutableList()
                ) { entity ->
                    entity?.title?.let {
                        viewModel.password.value = it
                        viewModel.imei.value = entity.unique
                    }
                }
            }
        }

        fun switchDatabase() {
            lifecycleScope.launch(Dispatchers.Main) {
                val list = viewModel.databaseFlow.value?.data?.map {
                    BottomSheetEntity(null, it)
                }
                if (list == null || list.isEmpty()) {
                    return@launch
                }
                BottomSheetDialogFragment.showBottomSheet(
                    requireActivity() as? AppCompatActivity,
                    list.toMutableList()
                ) {
                    it?.title?.let {
                        viewModel.databasePath.value = it
                    }
                }
            }
        }

        fun createPassword() {
            showLoading()
            viewModel.createPassword()
        }

        fun submit() {
            showLoading()
            viewModel.submit {
                if (!it.isNullOrEmpty()) {
                    TipsDialog.newInstance(it).show(childFragmentManager, "TipsDialog")
                }
                hindLoading()
            }
        }
    }
}