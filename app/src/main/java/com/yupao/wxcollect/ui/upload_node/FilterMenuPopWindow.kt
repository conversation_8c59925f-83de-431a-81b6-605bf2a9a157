package com.yupao.wxcollect.ui.upload_node

import android.content.Context
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.view.forEach
import com.yupao.utils.system.window.DensityUtils
import com.yupao.utils.system.window.ScreenTool
import com.yupao.execute.wxcollect.R

/**
 * 聊天菜单按钮
 *
 * <p>创建时间：2023/12/5/005</p>
 *
 * <AUTHOR>
 */
class FilterMenuPopWindow(
    private val context: Context,
    private val width: Int = ViewGroup.LayoutParams.MATCH_PARENT,
    private val height: Int = ViewGroup.LayoutParams.WRAP_CONTENT,
): PopupWindow(width, height) {

    private val rootView by lazy {
        LayoutInflater.from(context)
            .inflate(R.layout.pop_filter_menu, null, false)
    }

    private val itemContainer by lazy {
        rootView.findViewById<ViewGroup>(R.id.itemContainer)
    }

    init {
        contentView = rootView
        isOutsideTouchable = true
        isFocusable = true
    }

    private var currMenuUIState: MenuUIState? = null

    private var isSingleCheck: Boolean = true

    private var clickBlock: ((List<MenuUIState>) -> Unit)? = null

    private val uiStateList = mutableListOf<MenuUIState>()

    internal fun showPopWindow(
        parent: View?,
        uiState: List<MenuUIState>?,
        isSingleCheck: Boolean,
        clickBlock: ((List<MenuUIState>) -> Unit),
    ) {
        parent ?: return
        this.isSingleCheck = isSingleCheck
        this.clickBlock = clickBlock
        initView(uiState)
        val visibleFrame = Rect()
        parent.getGlobalVisibleRect(visibleFrame)
        val height: Int = parent.resources.displayMetrics.heightPixels - visibleFrame.bottom
        val totalHeight = childSize() * DensityUtils.dp2px(context, 50f)
        val y = if (height > totalHeight) 0 else (parent.height * -1 - totalHeight)
        val thisWidth = if (width == ViewGroup.LayoutParams.MATCH_PARENT) ScreenTool.getScreenWidth(context) else width
        val x = (parent.width / 2 - thisWidth / 2).coerceAtLeast(0)
        showAsDropDown(parent, x, y)
        setOnDismissListener {
            val checkList = uiState?.filter { it.isChecked == true } ?: emptyList()
            val firstItem = uiState?.firstOrNull()
            if (checkList.isEmpty() && firstItem != null) {
                this.clickBlock?.invoke(listOf(firstItem))
            } else {
                this.clickBlock?.invoke(checkList)
            }
        }
    }

    private fun childSize(): Int {
        var size = 0
        (rootView as? ViewGroup)?.forEach {
            if (it.visibility == View.VISIBLE) {
                size ++
            }
        }
        return size
    }

    private fun initView(uiStateList: List<MenuUIState>?) {
        if (uiStateList.isNullOrEmpty()) {
            return
        }
        this.uiStateList.clear()
        this.uiStateList.addAll(uiStateList)
        itemContainer.apply {
            removeAllViews()
            uiStateList.forEach { uiState ->
                val layout = if (isSingleCheck) R.layout.item_filter_menu else R.layout.item_filter_menu_mult
                val view = LayoutInflater.from(context).inflate(layout, null, false)
                (view.findViewById<TextView>(R.id.text))?.apply {
                    this.tag = uiState
                    this.text = uiState.text
                    handleCheck(this, uiState.isChecked)
                    if (uiState.isChecked == true) {
                        currMenuUIState = uiState
                    }
                    setOnClickListener {
                        onItemClick(this, uiState)
                    }
                }
                addView(view, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            }
        }
    }

    private fun setAllChecked(isChecked: Boolean) {
        itemContainer.forEach { view ->
            val textView = view.findViewById<TextView>(R.id.text)
            val uiState = textView.tag as? MenuUIState ?: return
            uiState.isChecked = isChecked
            handleCheck(textView, isChecked)
        }
    }

    private fun notifyDataSetChanged() {
        itemContainer.forEach { view ->
            val textView = view.findViewById<TextView>(R.id.text)
            val uiState = textView.tag as? MenuUIState ?: return
            handleCheck(textView, uiState.isChecked)
        }
    }

    private fun onItemClick(textView: TextView, uiState: MenuUIState) {
        if (isSingleCheck) {
            if (uiState.isChecked == true) {
                return
            }
            currMenuUIState?.isChecked = false
            currMenuUIState = null
            uiState.isChecked = true
            dismiss()
        } else {
            uiState.isChecked = !(uiState.isChecked ?: false)
            if (uiState.unique == UploadNodeViewModel.AllText) {
                setAllChecked(uiState.isChecked ?: false)
            } else {
                if (uiState.isChecked == false) {
                    val allItem = this.uiStateList.find { it.unique == UploadNodeViewModel.AllText }
                    if (allItem?.isChecked == true) {
                        allItem.isChecked = false
                        notifyDataSetChanged()
                        return
                    }
                }
                handleCheck(textView, uiState.isChecked)
            }
        }
    }

    private fun handleCheck(textView: TextView, isChecked: Boolean?) {
        textView.setTextColor(textView.resources.getColor(if(isChecked == true) R.color.colorPrimary else R.color.black85))
        if (!isSingleCheck) {
            val icon = if (isChecked == true) R.mipmap.icon_checked else R.mipmap.icon_uncheck
            textView.setCompoundDrawablesWithIntrinsicBounds(icon, 0, 0, 0)
        }
    }
}

data class MenuUIState(
    val text: String?,
    var isChecked: Boolean?,
    val unique: String? = null,
    val sort: Int = 0,
    val taskCode: String? = null
)