package com.yupao.wxcollect.ui.upload_result

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import com.yupao.widget.recyclerview.xrecyclerview.adpter.BaseQuickAdapter
import com.yupao.widget.recyclerview.xrecyclerview.adpter.viewholder.BaseDataBindingHolder
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.ItemUploadResultBinding

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:49
 * @Description 上传监听
 */
class UploadResultAdapter: BaseQuickAdapter<ReportInfoEntity, BaseDataBindingHolder<ItemUploadResultBinding>>(
    R.layout.item_upload_result,
    mutableListOf()
) {

    override fun onCreateDefViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseDataBindingHolder<ItemUploadResultBinding> {
        val rootView = LayoutInflater.from(context).inflate(R.layout.item_upload_result, parent, false)
        return BaseDataBindingHolder(rootView)
    }

    @SuppressLint("SetTextI18n")
    override fun convert(holder: BaseDataBindingHolder<ItemUploadResultBinding>, item: ReportInfoEntity) {
        holder.dataBinding?.let {
            val isMainApp = item.isMainApp()
            it.container.setBackgroundColor(
                context.resources.getColor(if(isMainApp) R.color.app_main_background else R.color.white)
            )
            it.count.text = "总条数：${item.receive_count}"
            it.telNo.text = "手机编号：${item.mobile_number_show_name}"
            it.startTime.text = "接受时间：${item.receive_date_time ?: "-"}"
            it.endTime.text = "完成时间：${item.complete_date_time ?: "-"}"
        }
    }
}