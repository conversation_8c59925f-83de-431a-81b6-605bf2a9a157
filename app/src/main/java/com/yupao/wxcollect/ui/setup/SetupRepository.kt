package com.yupao.wxcollect.ui.setup

import com.yupao.data.protocol.Resource
import com.yupao.wxcollect.database.CommDatabase
import com.yupao.wxcollect.net.AppHttpClient
import com.yupao.wxcollect.service.WechatUserManager
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.ui.entity.VersionRequestEntity
import com.yupao.ylog.YLog
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/19/019 10:50
 * @Description
 */
class SetupRepository @Inject constructor(
    commDatabase: CommDatabase
) {

    private val dao by lazy {
        commDatabase.getReportConfigDao()
    }

    private val api by lazy {
        AppHttpClient.instanceForC.createApiService(VersionApi::class.java)
    }

    /**
     * 清空配置
     */
    suspend fun clear(list: List<String?>) = flow {
        emit(Resource.Loading)
        YLog.i("SetupRepository", "clear: list = $list")
        dao.queryAll().filter {
            list.contains(it.appType)
        }.forEach {
            dao.delete(it)
            // 清除共享的缓存文件
            if (it.appType == ReportConfig.APP_MAIN) {
                WechatUserManager.clearCacheFile(true)
            } else if (it.appType == ReportConfig.APP_SUB) {
                WechatUserManager.clearCacheFile(false)
            }
        }
        emit(Resource.Success(true))
    }.catch { e ->
        emit(Resource.Error(exception = e))
    }

    /**
     * 检测app版本
     */
    suspend fun checkVersion(phoneNumber: String? = null) = flow {
        emit(Resource.Loading)
        val appVersion = api.appVersion(VersionRequestEntity(phoneNumber))
        emit(Resource.Success(appVersion))
    }.catch { e ->
        emit(Resource.Error(exception = e))
    }

}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface SetupRepoEntryPoint {

    fun getSetupRepo(): SetupRepository

}