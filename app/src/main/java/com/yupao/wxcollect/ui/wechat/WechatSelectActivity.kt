package com.yupao.wxcollect.ui.wechat

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.yupao.execute.wxcollect.R
import com.yupao.wxcollect.ui.BaseActivity
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WechatSelectActivity : BaseActivity() {

    private lateinit var adapter: WechatAccountAdapter

    // 广播接收器，用于接收超时关闭指令
    private val closeReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.yupao.wxcollect.CLOSE_SELECT_ACTIVITY") {
                YLog.i("WechatSelectActivity", "收到超时关闭指令")
                finish()
            }
        }
    }

    // View references
    private lateinit var selectLayout: LinearLayout
    private lateinit var confirmLayout: LinearLayout
    private lateinit var inputLayout: LinearLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var tvNickname: TextView
    private lateinit var tvWechatId: TextView
    private lateinit var btnModify: TextView
    private lateinit var btnConfirm: TextView
    private lateinit var etWechatId: EditText
    private lateinit var btnCancel: TextView
    private lateinit var btnInputConfirm: TextView

    companion object {
        private const val KEY_ACCOUNTS = "accounts"
        private const val KEY_MODE = "mode"
        const val MODE_SELECT = 0
        const val MODE_CONFIRM = 1
        const val MODE_INPUT = 2

        // 保存当前实例的弱引用，用于超时关闭
        private var currentInstance: WechatSelectActivity? = null

        /**
         * 关闭当前实例
         */
        fun closeCurrentInstance() {
            currentInstance?.let { activity ->
                if (!activity.isFinishing && !activity.isDestroyed) {
                    YLog.i("WechatSelectActivity", "通过静态方法关闭Activity")
                    activity.finish()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_wechat_select)

        // 保存当前实例
        currentInstance = this

        // 注册广播接收器
        registerCloseReceiver()

        initViews()

        val mode = intent.getIntExtra(KEY_MODE, MODE_SELECT)

        if (mode == MODE_SELECT) {
            setupSelectMode()
        } else if (mode == MODE_CONFIRM) {
            setupConfirmMode()
        } else {
            setupInputMode()
        }

        // 设置根布局点击事件，点击外部区域关闭弹窗
        findViewById<View>(R.id.rootLayout).setOnClickListener {
            onCancel()
        }
    }

    /**
     * 注册广播接收器
     */
    private fun registerCloseReceiver() {
        val filter = IntentFilter("com.yupao.wxcollect.CLOSE_SELECT_ACTIVITY")
        registerReceiver(closeReceiver, filter)
    }

    /**
     * 注销广播接收器
     */
    private fun unregisterCloseReceiver() {
        try {
            unregisterReceiver(closeReceiver)
        } catch (e: Exception) {
            YLog.printException("WechatSelectActivity", e, "注销广播接收器失败")
        }
    }

    private fun initViews() {
        selectLayout = findViewById(R.id.selectLayout)
        confirmLayout = findViewById(R.id.confirmLayout)
        recyclerView = findViewById(R.id.recyclerView)
        tvNickname = findViewById(R.id.tvNickname)
        tvWechatId = findViewById(R.id.tvWechatId)
        btnModify = findViewById(R.id.btnModify)
        btnConfirm = findViewById(R.id.btnConfirm)

        btnCancel = findViewById(R.id.btnCancel)
        inputLayout = findViewById(R.id.inputLayout)
        etWechatId = findViewById(R.id.etWechatId)
        btnInputConfirm = findViewById(R.id.btnInputConfirm)
    }

    private fun setupSelectMode() {
        selectLayout.visibility = View.VISIBLE
        confirmLayout.visibility = View.GONE
        inputLayout.visibility = View.GONE

        // 防止点击内容区域关闭弹窗
        selectLayout.setOnClickListener { /* 拦截点击事件 */ }

        val accounts =
            intent.getParcelableArrayListExtra<WechatAccount>(KEY_ACCOUNTS) ?: arrayListOf()

        adapter = WechatAccountAdapter { account ->
            // 处理选择回调
            onAccountSelected(account)
        }

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
        adapter.submitList(accounts)
    }

    private fun setupConfirmMode() {
        selectLayout.visibility = View.GONE
        confirmLayout.visibility = View.VISIBLE
        inputLayout.visibility = View.GONE

        // 防止点击内容区域关闭弹窗
        confirmLayout.setOnClickListener { /* 拦截点击事件 */ }

        val accounts = intent.getParcelableArrayListExtra<WechatAccount>(KEY_ACCOUNTS)
        val account = accounts?.firstOrNull()
        val nickname = account?.nickname ?: ""
        val wechatId = account?.alias ?: ""
        tvNickname.text = nickname
        tvWechatId.text = wechatId

        btnModify.setOnClickListener {
            // 处理修改
            onModifyClicked()
        }

        btnConfirm.setOnClickListener {
            // 处理确定
            onConfirmClicked()
        }
    }

    private fun setupInputMode() {
        selectLayout.visibility = View.GONE
        confirmLayout.visibility = View.GONE
        inputLayout.visibility = View.VISIBLE

        btnCancel.setOnClickListener {
            onCancel()
        }
        btnInputConfirm.setOnClickListener {
            //关闭软键盘
            etWechatId.clearFocus()
            val wechatId = etWechatId.text.toString().trim()
            if (TextUtils.isEmpty(wechatId)) {
                Toast.makeText(this, "请输入微信号", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            onAccountInput(wechatId)
        }
    }

    /**
     * 处理账号选择回调
     */
    private fun onAccountSelected(account: WechatAccount) {
        WechatSelectHelper.onAccountSelected(account)
        finish()
    }

    /**
     * 处理修改按钮点击回调
     */
    private fun onModifyClicked() {
        WechatSelectHelper.onAccountModify()
        finish()
    }

    /**
     * 处理确定按钮点击回调
     */
    private fun onConfirmClicked() {
        val accounts = intent.getParcelableArrayListExtra<WechatAccount>(KEY_ACCOUNTS)
        val account = accounts?.firstOrNull()
        if (account != null) {
            WechatSelectHelper.onAccountConfirmed(account)
        }
        finish()
    }

    /**
     * 处理手动输入回调
     */
    private fun onAccountInput(wechatId: String?) {
        if (wechatId != null) {
            WechatSelectHelper.onAccountInput(wechatId)
        }
        finish()
    }

    private fun onCancel() {
        WechatSelectHelper.onCancel()
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清除当前实例引用
        if (currentInstance == this) {
            currentInstance = null
        }
        // 注销广播接收器
        unregisterCloseReceiver()
    }
}