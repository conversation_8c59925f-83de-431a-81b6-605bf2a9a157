package com.yupao.wxcollect.ui.viewpager

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.yupao.ylog.YLog

/**
 * <AUTHOR>
 * @Date 2023/4/11/011 10:49
 * @Description
 */
class ViewPagerAdapter (
    private val fragmentActivity: FragmentActivity,
    private val factory: IFragmentFactory,
) : FragmentStateAdapter(fragmentActivity) {
    override fun getItemCount(): Int {
        return factory.size()
    }

    override fun createFragment(position: Int): Fragment {
        //如果是重建流程，需要先移除这个fragment
        val tag = "f${getItemId(position)}"
        val preFragment = fragmentActivity.supportFragmentManager.findFragmentByTag(tag)
        if (preFragment != null) {
            try {
                fragmentActivity.supportFragmentManager.beginTransaction().remove(preFragment)
                    .commitAllowingStateLoss()
            } catch (e: Exception) {
                YLog.printException(e)
            }
        }
        return factory.create(position)
    }

    override fun getItemId(position: Int): Long {
        return super.getItemId(position)
    }
}