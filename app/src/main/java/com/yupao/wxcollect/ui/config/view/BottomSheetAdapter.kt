package com.yupao.wxcollect.ui.config.view

import com.yupao.widget.recyclerview.xrecyclerview.adpter.BaseQuickAdapter
import com.yupao.widget.recyclerview.xrecyclerview.adpter.viewholder.BaseDataBindingHolder
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.ItemBottomSheetBinding
import com.yupao.wxcollect.ui.config.entity.BottomSheetEntity

/**
 * <AUTHOR>
 * @date 2023/3/27
 * @description 底部弹出适配器
 */
class BottomSheetAdapter(
    data: MutableList<BottomSheetEntity>?
) :
    BaseQuickAdapter<BottomSheetEntity, BaseDataBindingHolder<ItemBottomSheetBinding>>(
        R.layout.item_bottom_sheet, data = data
    ) {

    override fun convert(
        holder: BaseDataBindingHolder<ItemBottomSheetBinding>,
        item: BottomSheetEntity,
    ) {
        holder.dataBinding?.data = item
        holder.dataBinding?.executePendingBindings()
    }
}
