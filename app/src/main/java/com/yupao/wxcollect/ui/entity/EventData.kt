package com.yupao.wxcollect.ui.entity

/**
 * 通用的事件data，只适用于单个观测者，如果同时有多个观测者，并且每个观测者需要处理一次则需要使用[MultipleProcessorEventData],
 * 可以使用[com.yupao.model.event.ktx.event]将一个flow转换成事件流，注意通常还需要将其转换成StateFlow或者SharedFlow来使用
 */
@SuppressWarnings("DataKeepRules", "DataClassFieldRule")
data class EventData<T>(
    val data: T,
    private var isConsumed: Boolean = false
) {
    /**
     * 是否需要处理
     */
    fun isNeedHandle(): Boolean {
        return !isConsumed
    }

    fun consumedEvent() {
        isConsumed = true
    }
}