package com.yupao.wxcollect.ui.task_manager

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.ItemTaskManagerBinding
import com.yupao.transmit.MessageManager
import com.yupao.transmit.execute.MessageEntity
import com.yupao.wxcollect.constant.AppConstant
import com.yupao.wxcollect.transmit.director.CoreClient
import com.yupao.wxcollect.transmit.director.MessageCode
import com.yupao.wxcollect.transmit.director.MessageKey
import com.yupao.wxcollect.ui.entity.TaskExecuteEntity
import com.yupao.wxcollect.ui.upload_node.UploadNodeActivity

/**
 * 类的作用的简要阐述
 *
 * <p>创建时间：2024/8/7/007</p>
 *
 * <AUTHOR>
 */
class TaskManagerAdapter (
    private val appType: String?,
    private val context: Context,
    private val callback: DiffItemCallback = DiffItemCallback()
) : ListAdapter<TaskManagerUIState, NodeViewHolder>(callback)  {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NodeViewHolder {
        val binding = DataBindingUtil.inflate<ItemTaskManagerBinding>(
            LayoutInflater.from(parent.context),
            R.layout.item_task_manager,
            parent,
            false
        )
        return NodeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: NodeViewHolder, position: Int) {
        val item = getItem(position) ?: return
        holder.dataBinding.apply {
            this.entity = item
            this.executePendingBindings()
        }
        holder.dataBinding.performTask.setOnClickListener {
            MessageManager.executor.submit(
                MessageEntity(
                    code = MessageCode.EXECUTE_TASK,
                    toHost = CoreClient.serviceHost,
                )
                    .put(MessageKey.TaskExecuteList, Gson().toJson(listOf(TaskExecuteEntity(item.taskCode, item.appType))))
                    .putProxyHost(listOf(AppConstant.Host.wxCollectReportHost))
            )
            UploadNodeActivity.start(context)
        }
    }
}

class NodeViewHolder(val dataBinding: ItemTaskManagerBinding): RecyclerView.ViewHolder(dataBinding.root) {

    private var item: TaskManagerUIState? = null
    fun bind(item: TaskManagerUIState) {
        this.item = item
    }

    init {
        dataBinding.root.setOnClickListener {

        }
    }
}

class DiffItemCallback : DiffUtil.ItemCallback<TaskManagerUIState>() {
    override fun areItemsTheSame(oldItem: TaskManagerUIState, newItem: TaskManagerUIState): Boolean {
        return oldItem.taskCode == newItem.taskCode
    }

    override fun areContentsTheSame(oldItem: TaskManagerUIState, newItem: TaskManagerUIState): Boolean {
        return oldItem == newItem
    }

}