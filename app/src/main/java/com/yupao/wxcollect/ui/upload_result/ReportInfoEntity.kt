package com.yupao.wxcollect.ui.upload_result

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @Date 2023/4/26/026 8:56
 * @Description
 */
@Keep
data class ReportInfoEntity(
    //记录ID
    val id: String?,
    //接收时间-时间戳
    val receive_time: String?,
    //接收时间-日期时间
    val receive_date_time: String?,
    //完成时间-时间戳
    val complete_time: String?,
    //完成时间-日期时间
    val complete_date_time: String?,
    //手机编号
    val mobile_number: String?,
    //主副号：1=主号；2=副号；
    val wechat_type: String?,
    //手机编号显示名称
    val mobile_number_show_name: String?,
    //接收总数
    val receive_count: String?,
) {
    fun isMainApp() = wechat_type == "1"
}
