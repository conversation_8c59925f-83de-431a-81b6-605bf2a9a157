package com.yupao.wxcollect.ui.setup

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.accessibility.AccessibilityEvent
import android.widget.Toast
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.yupao.data.protocol.doError
import com.yupao.data.protocol.doSuccess
import com.yupao.utils.system.toast.ToastUtilsAssist
import com.yupao.wxcollect.App
import com.yupao.wxcollect.service.notice.NotificationConfig
import com.yupao.wxcollect.service.notice.ReportNotificationManager
import com.yupao.wxcollect.service.procedure.model.ReportRepository
import com.yupao.wxcollect.ui.main.MainActivity
import com.yupao.wxcollect.service.accessibility.process.AutoSwitchAccountTask
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject


/**
 * <AUTHOR>
 * @Date 2023/4/21/021 13:34
 * @Description 帮助App切换账号
 */
@AndroidEntryPoint
class WechatAccountService: AccessibilityService() {
    companion object {
        // 是否支持清除EN
        var isSupportClearEN = false

        const val APP_TYPE_LIST = "app_type_list"

        const val KEY_NOTICE_CONFIG = "notice_config"

        const val TURN_ON = "turn_on"

        fun start(context: Context, isTurnOn: Boolean, appTypeList: ArrayList<String>) {
            val intent = Intent(context, WechatAccountService::class.java)
            intent.putExtra(TURN_ON, isTurnOn)
            intent.putExtra(
                KEY_NOTICE_CONFIG, NotificationConfig(
                    pendingActivityPath = MainActivity::class.java.name,
                    content = "正在监听微信页面",
                    title = "微信清除EN服务"
                ))
            intent.putExtra(APP_TYPE_LIST, appTypeList)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            YLog.i(
                "WechatAccountService", "isTurnOn = $isTurnOn, appTypeList = $appTypeList"
            )
        }
    }

    private var autoSwitchAccountTask: AutoSwitchAccountTask? = null

    // 是否开启
    private var isTurnOn: Boolean = true

    private val notificationManager by lazy {
        ReportNotificationManager()
    }

    @Inject
    lateinit var reportRepository: ReportRepository

    /**
     * 当无障碍服务连接之后回调
     */
    override fun onServiceConnected() {
        super.onServiceConnected()
        isSupportClearEN = true
        YLog.i("WechatAccountService", "onServiceConnected")
        Toast.makeText(App.getContext(), "已开启无障碍模式", Toast.LENGTH_SHORT).show()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val list = intent?.getStringArrayListExtra(APP_TYPE_LIST)
        isTurnOn = intent?.getBooleanExtra(TURN_ON, false) ?: false
        YLog.i("WechatAccountService", "onStartCommand: isTurnOn = $isTurnOn")
        if (!isSupportClearEN) {
            ToastUtilsAssist.showCustomToast(this, "未开启无障碍服务，请重试")
        } else if (isTurnOn) {
            val notificationConfig: NotificationConfig? = intent?.getParcelableExtra(
                KEY_NOTICE_CONFIG
            )
            val notification = notificationManager.showNotification(this, notificationConfig)
            val noticeId = notificationConfig?.noticeId ?: (System.currentTimeMillis() % Int.MAX_VALUE).toInt()
            notificationConfig?.noticeId = noticeId
            startForeground(noticeId, notification)
            queryConfig(list)
        }
        return super.onStartCommand(intent, flags, startId)
    }

    private fun queryConfig(appTypeList: List<String>?) {
        ProcessLifecycleOwner.get().lifecycleScope.launch(Dispatchers.IO) {
            reportRepository.queryAllConfig()?.collectLatest {
                it.doSuccess {
                    val map = mutableMapOf<String, MutableSet<String>>()
                    it.data?.forEach {
                        if (it.appType == null || appTypeList?.contains(it.appType) != true) {
                            return@forEach
                        }
                        val list = map[it.appType] ?: mutableSetOf<String>()
                        it.databasePath?.let {
                            list.add(it)
                        }
                        map[it.appType ?: ""] = list
                    }
                    autoSwitchAccountTask = AutoSwitchAccountTask() {
                        YLog.i("WechatAccountService", "queryConfig: task finished")
                        isTurnOn = false
                        ToastUtilsAssist.showCustomToast(App.getContext(), "已成功清除EN")
                        notificationManager.updateNotice(App.getContext(), "已成功清除EN${TimeUtil.formatCurrTime()}")
                    }
                    autoSwitchAccountTask?.perform(map)
                }
                it.doError {
                    YLog.printException("ReportService, fetchConfig", it.exception)
                }
            }
        }
    }

    /**
     * 当触发了需要监听的无障碍事件后回调
     */
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (isTurnOn) {
            autoSwitchAccountTask?.submitEvent(event)
        }
    }

    /**
     * 无障碍服务断开后回调
     */
    override fun onInterrupt() {
        YLog.i("WechatAccountService", "onInterrupt:")
        isSupportClearEN = false
    }
}