package com.yupao.wxcollect.ui.upload_result

import com.yupao.data.net.yupao.BaseListData
import com.yupao.data.net.yupao.NetRequestInfo
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * <AUTHOR>
 * @Date 2023/4/25/025 20:37
 * @Description
 */
interface UploadApi {

    /**
     * 获取上传聊天记录的历史记录
     */
    @POST("/backend/collect/wechat/getUploadHistoryOfChatHistory")
    suspend fun getUploadHistoryOfChatHistory(@Body entity: ReportInfoRequestEntity?): NetRequestInfo<BaseListData<ReportInfoEntity>>
}