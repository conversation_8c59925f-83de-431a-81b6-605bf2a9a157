package com.yupao.wxcollect.ui.config.model

import com.yupao.data.protocol.Resource
import com.yupao.wxcollect.database.CommDatabase
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.ylog.YLog
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/12/012 14:41
 * @Description
 */
class ReportConfigLDS @Inject constructor(
    private val dataBase: CommDatabase
){

    private val dao by lazy { dataBase.getReportConfigDao() }

    fun queryAll() = flow<Resource<List<ReportConfig>>> {
        emit(Resource.Loading)
        emit(Resource.Success(dao.queryAll()))
    }.catch { e ->
        emit(Resource.Error(exception = e))
    }

    fun query(appType: String?) = flow<Resource<ReportConfig?>> {
        emit(Resource.Loading)
        val config = dao.queryByType(appType)
        YLog.i("ReportConfigLDS", "query:  = $appType")
        emit(Resource.Success(config))
    }.catch { e ->
        emit(Resource.Error(exception = e))
    }

    suspend fun insert(config: ReportConfig?) = flow<Resource<ReportConfig?>> {
        emit(Resource.Loading)
        if (config == null) {
            emit(Resource.Error(errorMsg = "config is empty."))
            return@flow
        }
        val queryConfig = dao.queryByType(config.appType)
        if (queryConfig != null) {
            YLog.i("ReportConfigLDS", "update: ${config.appType}")
            config.id = queryConfig.id
            dao.update(config)
        } else {
            YLog.i("ReportConfigLDS", "insert: ${config.appType}")
            dao.insert(config)
        }
        emit(Resource.Success(config))
    }.catch { e ->
        emit(Resource.Error(exception = e))
    }
}