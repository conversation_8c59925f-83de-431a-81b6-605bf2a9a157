package com.yupao.wxcollect.ui.wechat

import com.yupao.ylog.YLog
import com.yupao.utils.system.asm.AndroidSystemUtil
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import android.content.Intent

/**
 * WechatSelectHelper 的扩展功能
 * 提供更简洁的 API 来处理微信账号选择
 */
object WechatSelectHelperExtensions {

    private const val TAG = "WechatSelectHelperExt"

    // 默认超时时间（5分钟）
    private const val DEFAULT_TIMEOUT_MS = 5 * 60 * 1000L

    /**
     * 关闭当前显示的选择页面
     */
    private fun closeCurrentSelectActivity() {
        try {
            // 方法1：通过静态方法直接关闭（最可靠）
            WechatSelectActivity.closeCurrentInstance()
            YLog.i(TAG, "通过静态方法关闭选择页面")

            // 方法2：通过广播关闭（备用方案）
            val context = AndroidSystemUtil.getContext()
            val broadcastIntent = Intent().apply {
                action = "com.yupao.wxcollect.CLOSE_SELECT_ACTIVITY"
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.sendBroadcast(broadcastIntent)
            YLog.i(TAG, "发送关闭选择页面广播")

        } catch (e: Exception) {
            YLog.printException(TAG, e, "关闭选择页面失败")
        }
    }

    /**
     * 选择微信账号（支持多个账号选择）
     * @param accounts 可选择的微信账号列表
     * @param timeoutMs 超时时间（毫秒），默认5分钟
     * @return 选择的微信账号，如果取消或超时则返回 null
     */
    suspend fun selectWechatAccount(
        accounts: List<WechatAccount>,
        timeoutMs: Long = DEFAULT_TIMEOUT_MS
    ): WechatAccount? {
        if (accounts.isEmpty()) {
            YLog.w(TAG, "账号列表为空")
            return null
        }

        if (accounts.size == 1) {
            // 只有一个账号，直接确认
            return accounts.first()
        }

        return try {
            withTimeout(timeoutMs) {
                suspendCancellableCoroutine { continuation ->
                    WechatSelectHelper.release()
                    WechatSelectHelper.init(object : OnWechatSelectListener {
                        override fun onAccountSelected(account: WechatAccount) {
                            if (continuation.isActive) {
                                continuation.resume(account)
                            }
                        }

                        override fun onAccountConfirmed(account: WechatAccount) {
                            if (continuation.isActive) {
                                YLog.w(TAG, "不应该在选择模式下触发确认回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onAccountModify() {
                            if (continuation.isActive) {
                                YLog.w(TAG, "不应该在选择模式下触发修改回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onAccountInput(wechatId: String) {
                            if (continuation.isActive) {
                                YLog.w(TAG, "不应该在选择模式下触发输入回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onCancel() {
                            if (continuation.isActive) {
                                YLog.i(TAG, "选择模式下用户取消")
                                continuation.resume(null)
                            }
                        }
                    })

                    // 设置取消回调，当协程被取消时清理资源
                    continuation.invokeOnCancellation {
                        YLog.w(TAG, "选择协程被取消（可能是超时）")
                        WechatSelectHelper.release()
                        closeCurrentSelectActivity()
                    }

                    WechatSelectHelper.startForSelect(ArrayList(accounts))
                }
            }
        } catch (e: TimeoutCancellationException) {
            YLog.w(TAG, "选择微信账号超时")
            WechatSelectHelper.release()
            // 关闭当前显示的选择页面
            closeCurrentSelectActivity()
            null
        }
    }

    /**
     * 确认微信账号
     * @param account 要确认的微信账号
     * @param timeoutMs 超时时间（毫秒），默认5分钟
     * @return 确认的微信账号，如果取消或超时则返回 null
     */
    suspend fun confirmWechatAccount(
        account: WechatAccount,
        timeoutMs: Long = DEFAULT_TIMEOUT_MS
    ): WechatAccount? {
        return try {
            withTimeout(timeoutMs) {
                suspendCancellableCoroutine { continuation ->
                    WechatSelectHelper.release()
                    WechatSelectHelper.init(object : OnWechatSelectListener {
                        override fun onAccountSelected(account: WechatAccount) {
                            if (continuation.isActive) {
                                YLog.i(TAG, "不应该在确认模式下触发了选择回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onAccountConfirmed(account: WechatAccount) {
                            if (continuation.isActive) {
                                YLog.i(TAG, "用户确认账号: ${account.alias}")
                                continuation.resume(account)
                            }
                        }

                        override fun onAccountModify() {
                            if (continuation.isActive) {
                                YLog.i(TAG, "不应该在确认模式下触发修改回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onAccountInput(wechatId: String) {
                            if (continuation.isActive) {
                                YLog.w(TAG, "不应该在确认模式下触发输入回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onCancel() {
                            if (continuation.isActive) {
                                YLog.i(TAG, "确认模式下用户取消")
                                continuation.resume(null)
                            }
                        }
                    })

                    // 设置取消回调，当协程被取消时清理资源
                    continuation.invokeOnCancellation {
                        YLog.w(TAG, "确认协程被取消（可能是超时）")
                        WechatSelectHelper.release()
                        closeCurrentSelectActivity()
                    }

                    WechatSelectHelper.startForConfirm(arrayListOf(account))
                }
            }
        } catch (e: TimeoutCancellationException) {
            YLog.w(TAG, "确认微信账号超时")
            WechatSelectHelper.release()
            // 关闭当前显示的选择页面
            closeCurrentSelectActivity()
            null
        }
    }

    /**
     * 手动输入微信号
     * @param timeoutMs 超时时间（毫秒），默认5分钟
     * @return 输入的微信号，如果取消或超时则返回 null
     */
    private suspend fun inputWechatId(timeoutMs: Long = DEFAULT_TIMEOUT_MS): String? {
        return try {
            withTimeout(timeoutMs) {
                suspendCancellableCoroutine { continuation ->
                    WechatSelectHelper.release()
                    WechatSelectHelper.init(object : OnWechatSelectListener {
                        override fun onAccountSelected(account: WechatAccount) {
                            // 不应该在输入模式下触发选择
                            if (continuation.isActive) {
                                YLog.i(TAG, "在输入模式下触发了选择回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onAccountConfirmed(account: WechatAccount) {
                            // 不应该在输入模式下触发确认
                            if (continuation.isActive) {
                                YLog.i(TAG, "在输入模式下触发了确认回调")
                                continuation.resume(null)
                            }
                        }

                        override fun onAccountModify() {
                            // 不应该在输入模式下触发修改
                            YLog.w(TAG, "在输入模式下触发了修改回调")
                        }

                        override fun onAccountInput(wechatId: String) {
                            if (continuation.isActive) {
                                YLog.i(TAG, "用户输入微信号: $wechatId")
                                continuation.resume(wechatId)
                            }
                        }

                        override fun onCancel() {
                            if (continuation.isActive) {
                                YLog.i(TAG, "用户取消输入")
                                continuation.resume(null)
                            }
                        }
                    })

                    // 设置取消回调，当协程被取消时清理资源
                    continuation.invokeOnCancellation {
                        YLog.w(TAG, "输入协程被取消（可能是超时）")
                        WechatSelectHelper.release()
                        closeCurrentSelectActivity()
                    }

                    WechatSelectHelper.startForInput()
                }
            }
        } catch (e: TimeoutCancellationException) {
            YLog.w(TAG, "输入微信号超时")
            WechatSelectHelper.release()
            // 关闭当前显示的选择页面
            closeCurrentSelectActivity()
            null
        }
    }
}
