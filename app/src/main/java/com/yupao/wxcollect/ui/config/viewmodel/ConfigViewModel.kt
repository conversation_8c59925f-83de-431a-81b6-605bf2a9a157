package com.yupao.wxcollect.ui.config.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yupao.data.protocol.data
import com.yupao.data.protocol.doSuccess
import com.yupao.wxcollect.constant.AppPathCache
import com.yupao.wxcollect.constant.SEPARATOR
import com.yupao.wxcollect.constant.WECHAT_AUTH_INFO_PATH
import com.yupao.wxcollect.service.db.DatabaseOpenResultEntity
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.transmit.DatabaseCheckTransmitRequest
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.config.entity.WechatConfig
import com.yupao.wxcollect.ui.config.model.FetchDatabasePathUseCase
import com.yupao.wxcollect.ui.config.model.FetchImeiUseCase
import com.yupao.wxcollect.ui.config.model.FetchPasswordUseCase
import com.yupao.wxcollect.ui.config.model.ReportConfigRepository
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import java.io.File
import javax.inject.Inject
import kotlin.math.absoluteValue

/**
 * <AUTHOR>
 * @Date 2023/4/11/011 10:34
 * @Description
 */
@HiltViewModel
class ConfigViewModel @Inject constructor(
    private val passwordUseCase: FetchPasswordUseCase,
    private val databaseUseCase: FetchDatabasePathUseCase,
    private val fetchImeiUseCase: FetchImeiUseCase,
    private val resp: ReportConfigRepository,
): ViewModel() {

    private val _appType = MutableLiveData(ReportConfig.APP_MAIN)
    val appType: LiveData<String>
        get() = _appType

    private val fetchDatabaseSignal = MutableSharedFlow<String>(replay = 1, extraBufferCapacity = 1)

    val databaseFlow = fetchDatabaseSignal.flatMapLatest {
        databaseUseCase.invoke(it, Dispatchers.IO)
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    private val fetchImeiFlowSignal = MutableSharedFlow<Boolean>(replay = 1, extraBufferCapacity = 1)

    val imeiFlow = fetchImeiFlowSignal.flatMapLatest {
        fetchImeiUseCase.invoke(it, Dispatchers.IO)
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    private val fetchPasswordSignal = MutableSharedFlow<WechatConfig>(replay = 1, extraBufferCapacity = 1)

    val passwordFlow = fetchPasswordSignal.flatMapLatest {
        val list = mutableListOf<String>()
        imeiFlow.value?.data?.let {
            list.addAll(it.filterNotNull())
        }
        imei.value?.let {
            if (list.contains(it)) {
                list.remove(it)
            }
            list.add(0, it)
        }
        passwordUseCase.invoke(
            config = it,
            imeiList = list,
            dispatcher = Dispatchers.IO)
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    private val submitConfigSignal = MutableSharedFlow<ReportConfig>(replay = 1, extraBufferCapacity = 1)

    val submitConfigFlow = submitConfigSignal.flatMapLatest {
        resp.insert(it).flowOn(Dispatchers.IO)
    }

    private val queryConfigSignal = MutableSharedFlow<Unit>(replay = 1, extraBufferCapacity = 1)

    val queryConfigFlow = queryConfigSignal.flatMapLatest {
        resp.query(appType.value)
    }.onEach {
        it.doSuccess {
            this.saveReportConfig = it.data
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    val isAtMostHi = MutableLiveData(false)

    val imei = MutableLiveData<String>()

    val telNo = MutableLiveData<String>()

    val password = MutableLiveData<String>()

    val databasePath = MutableLiveData<String>()

    // 存储配置
    private var saveReportConfig: ReportConfig? = null

    fun setupAppType(appType: String?) {
        _appType.value = appType
    }

    fun updateReportConfig(config: ReportConfig?) {
        YLog.i("ConfigViewModel", "updateReportConfig: config = $config")
        config?.let {
            isAtMostHi.value = it.isAtMostHi ?: false
            telNo.value = it.telNo ?: ""
            imei.value = it.imei ?: ""
            databasePath.value = it.databasePath ?: ""
            password.value = it.password ?: ""
        }
    }

    /**
     * 创建密码
     */
    fun createPassword() {
        YLog.i("ConfigViewModel", "createPassword: ${appType.value}")
        val authInfoKeyPrefsPath = String.format(
            WECHAT_AUTH_INFO_PATH,
            if (appType.value == ReportConfig.APP_MAIN) "0" else "999"
        )
        fetchPasswordSignal.tryEmit(
            WechatConfig(
                databasePath = databasePath.value,
                authInfoKeyPrefsPath = authInfoKeyPrefsPath,
                destAuthInfoKeyPrefsDirPath = getDestAuthDir(),
                isWxVersionHi = isAtMostHi.value ?: false
            )
        )
    }

    private fun getDestAuthDir(): String {
        val destAuthPath = "${appType.value}${SEPARATOR}"
        return "${AppPathCache.appWechatDir()}${SEPARATOR}$destAuthPath"
    }

    /**
     * 获取数据库路径
     */
    fun fetchDatabasePath() {
        YLog.i("ConfigViewModel", "fetchDatabasePath")
        fetchDatabaseSignal.tryEmit(appType.value ?: ReportConfig.APP_MAIN)
    }

    /**
     * 获取IMEI
     */
    fun fetchImei() {
        // 微信版本是否高于7.0.20
        fetchImeiFlowSignal.tryEmit(isAtMostHi.value ?: false)
    }

    /**
     * 查询数据
     */
    fun query() {
        queryConfigSignal.tryEmit(Unit)
    }

    /**
     * 提交
     */
    fun submit(callback: (String?) -> Unit) {
        val telNo = telNo.value ?: kotlin.run {
            callback.invoke("请在[导演]App配置手机编号")
            return
        }
        val imei = imei.value ?: kotlin.run {
            callback.invoke("请输入IMEI")
            return
        }
        val databasePath = databasePath.value ?: kotlin.run {
            callback.invoke("请输入数据库路径")
            return
        }
        val password = password.value ?: kotlin.run {
            callback.invoke("请输入密码")
            return
        }
        val config = ReportConfig(
            isAtMostHi = isAtMostHi.value,
            appType = appType.value,
            telNo = telNo,
            imei = imei,
            databasePath = databasePath,
            password = password,
            createTime = TimeUtil.getCurrTime(),
        )
        val currConfig = this.saveReportConfig
        if (isSubmitOnce && currConfig != null) {
            if (
                currConfig.isAtMostHi == config.isAtMostHi
                && currConfig.appType == config.appType
                && currConfig.databasePath == config.databasePath
                && currConfig.imei == config.imei
                && currConfig.password == config.password
                && currConfig.telNo == config.telNo
            ) {
                callback.invoke("配置数据没有修改")
                return
            }
        }
        checkDatabase(config, AppPathCache.isInnerDir() != false, callback)
//        if (AppPath.isInnerDir() == null) {
//            YLog.i("ConfigViewModel", "submit: check database")
//            checkDatabase(config, true, callback)
//        } else {
//            YLog.i("ConfigViewModel", "submit: check file exist")
//            WechatFileUtil.checkFileExistedFromOtherProcess(config.databasePath ?: "") {
//                if (it) {
//                    submitConfigSignal.tryEmit(config)
//                } else {
//                    callback.invoke("数据库不存在")
//                }
//            }
//        }
//        submitConfigSignal.tryEmit(config)
    }

    // 是否密码错误
    private var isPasswordError = false

    // 是否提交过一次
    private var isSubmitOnce = false

    /**
     * 存储路径可能因机型不同而不同，否则可能最后上报失败，第一次需要尝试加载一次
     */
    private fun checkDatabase(config: ReportConfig, isUseInnerDir: Boolean, callback: (String?) -> Unit) {
        val wechatPath = config.databasePath ?: ""
        val origFile = File(wechatPath)
        val databaseBaseName = origFile.parentFile?.name  ?: (databasePath.hashCode().absoluteValue)
        val destDir = AppPathCache.appWechatDir(isUseInnerDir) + SEPARATOR + databaseBaseName + SEPARATOR
        YLog.i("ConfigViewModel", "start database check")
        IReportClientManager.instance.addTransmitRequest(DatabaseCheckTransmitRequest(
            wechatDatabasePath = wechatPath,
            destDatabaseDir = destDir,
            password = config.password ?: "",
            isUseInnerDir = isUseInnerDir,
            appType = config.appType ?: ReportConfig.APP_MAIN,
            isCopyDb = !isPasswordError,
            oldWechatDatabasePath = saveReportConfig?.databasePath,
        ) {
            YLog.i("ConfigViewModel", "checkDatabase: result = $it")
            if (it?.code == DatabaseOpenResultEntity.CODE_OK) {
                if (AppPathCache.isInnerDir() == null) {
                    AppPathCache.setUseInnerDir(isUseInnerDir)
                }
                submitConfigSignal.tryEmit(config)
                isSubmitOnce = true
            } else {
                this.isPasswordError = it?.code == DatabaseOpenResultEntity.CODE_PASSWORD_ERROR || it?.code == DatabaseOpenResultEntity.CODE_FAILED
                // 如果是因为路径存储错误导致不能打开，重置一下路径，部分设备(eg.5448、7348 Redmi6)实在无语
                if (it?.code == DatabaseOpenResultEntity.CODE_PATH_ERROR && isUseInnerDir) {
                    YLog.i("ConfigViewModel", "checkDatabase: retry update dir, isUseInnerDir = $isUseInnerDir")
                    checkDatabase(config, false,callback)
                    return@DatabaseCheckTransmitRequest
                }
                callback.invoke(it?.message)
            }
        })
    }
}

