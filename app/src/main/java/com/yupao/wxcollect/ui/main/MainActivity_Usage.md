# MainActivity 中 WechatSelectActivity 回调实现说明

## 功能概述

在 MainActivity 中实现了完整的微信账号选择和确认流程，包括：
1. **账号选择**：显示账号列表，用户点击选择
2. **账号确认**：显示选择的账号，用户确认或修改
3. **完整回调**：处理所有用户操作的回调

## 实现的回调功能

### 1. 列表点击回调 ✅
```kotlin
override fun onAccountSelected(account: WechatAccount) {
    // 用户点击了列表中的账号
    Toast.makeText(this@MainActivity, 
        "选择了账号：${account.nickname} (${account.wechatId})", 
        Toast.LENGTH_LONG).show()
    
    // 自动进入确认模式
    val accounts = arrayListOf(account)
    wechatSelectHelper.startForConfirm(this@MainActivity, accounts)
}
```

### 2. 确定按钮回调 ✅
```kotlin
override fun onAccountConfirmed(account: WechatAccount) {
    // 用户点击了确定按钮
    Toast.makeText(this@MainActivity, 
        "确认了账号：${account.nickname} (${account.wechatId})", 
        Toast.LENGTH_LONG).show()
    
    // 执行确认后的业务逻辑
    handleAccountConfirmed(account)
}
```

### 3. 修改按钮回调 ✅
```kotlin
override fun onAccountModify(account: WechatAccount?) {
    // 用户点击了修改按钮
    Toast.makeText(this@MainActivity, 
        "点击了修改按钮，重新选择账号", 
        Toast.LENGTH_SHORT).show()
    
    // 重新启动账号选择
    startWechatAccountSelection()
}
```

### 4. 取消操作回调 ✅
```kotlin
override fun onCancelled() {
    // 用户取消了操作（点击外部区域或返回键）
    Toast.makeText(this@MainActivity, 
        "取消了账号选择", 
        Toast.LENGTH_SHORT).show()
}
```

## 使用流程

### 1. 初始化（在 onCreate 中）
```kotlin
// 初始化微信账号选择辅助类
initWechatSelectHelper()
```

### 2. 启动账号选择
```kotlin
addItem(R.drawable.ic_settup, "微信账号选择") {
    startWechatAccountSelection()
}
```

### 3. 启动账号确认
```kotlin
addItem(R.drawable.ic_settup, "微信账号确认") {
    startWechatAccountConfirmation()
}
```

## 完整的用户交互流程

1. **用户点击"微信账号选择"按钮**
   - 显示账号列表弹窗
   - 列表包含：张三、李四、王五、赵六、钱七

2. **用户点击列表中的某个账号**
   - 触发 `onAccountSelected` 回调
   - 显示选择成功的 Toast
   - 自动跳转到确认模式

3. **在确认模式中**
   - 用户可以点击"确定"：触发 `onAccountConfirmed` 回调
   - 用户可以点击"修改"：触发 `onAccountModify` 回调，重新选择
   - 用户可以点击外部区域：触发 `onCancelled` 回调

4. **确认后的处理**
   - 调用 `handleAccountConfirmed(account)` 方法
   - 可以在此方法中添加具体的业务逻辑

## 测试方法

1. 运行应用
2. 在主界面点击"微信账号选择"按钮
3. 在弹出的列表中点击任意账号
4. 观察 Toast 提示和日志输出
5. 在确认界面测试"确定"和"修改"按钮
6. 测试点击外部区域取消功能

## 日志输出

所有操作都会输出详细的日志：
```kotlin
YLog.i("MainActivity", "选择了账号：${account.nickname}, 微信号：${account.wechatId}")
YLog.i("MainActivity", "确认了账号：${account.nickname}, 微信号：${account.wechatId}")
YLog.i("MainActivity", "点击了修改按钮")
YLog.i("MainActivity", "取消了账号选择")
```

## 资源释放

在 `onDestroy()` 中自动释放资源：
```kotlin
override fun onDestroy() {
    super.onDestroy()
    if (::wechatSelectHelper.isInitialized) {
        wechatSelectHelper.release()
    }
}
```
