package com.yupao.wxcollect.ui.upload_result

import com.yupao.data.protocol.Resource
import com.yupao.utils.log.LogUtil
import com.yupao.wxcollect.database.CommDatabase
import com.yupao.wxcollect.net.AppHttpClient
import com.yupao.wxcollect.service.procedure.entity.ReportConfig
import com.yupao.wxcollect.service.procedure.task.ReportType
import com.yupao.wxcollect.util.TimeUtil
import com.yupao.ylog.YLog
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * <AUTHOR>
 * @Date 2023/4/19/019 10:50
 * @Description
 */
class UploadResultRepository @Inject constructor(
    private val commDatabase: CommDatabase,
) {

    private val userDao by lazy { commDatabase.getUserDao() }

    private val configDao by lazy { commDatabase.getReportConfigDao() }

    private val resultDao by lazy { commDatabase.getReportResultDao() }

    private val api by lazy {
        AppHttpClient.instanceForC.createApiService(UploadApi::class.java)
    }

    suspend fun queryRemote(entity: ReportInfoRequestEntity?) = flow {
        emit(Resource.Loading)
        val info = api.getUploadHistoryOfChatHistory(entity)
        emit(Resource.Success(info))
    }.catch { e ->
        emit(Resource.Error(exception = e))
    }

    private suspend fun getType(appType: String?) = when (appType) {
        ReportConfig.APP_MAIN -> 1
        ReportConfig.APP_SUB -> 2
        else -> 0
    }

    suspend fun fetchData(
        appType: String?,
        reportType: Int?,
        page: Int?,
        size: Int?
    ): Flow<Resource<List<ReportInfoEntity>>> {
        return if (reportType == ReportType.REPORT_OFFICIAL_ACCOUNT) {
            queryLocal(appType, reportType, page, size)
        } else {
            queryRemote(appType, page, size)
        }
    }

    private fun queryLocal(
        appType: String?, reportType: Int?, page: Int?, size: Int?
    ): Flow<Resource<List<ReportInfoEntity>>> = flow {
        emit(Resource.Loading)
        if (reportType != null) {
            val taskTypeList = ReportType.getTaskTypeList(reportType)
            val appTypeList = if (appType == null) {
                ReportConfig.getAllAppType()
            } else listOf(appType)
            val offset = ((page ?: 1) - 1) * (size ?: 10)
            val list = resultDao.query(size, appTypeList, taskTypeList, offset)
                ?.map {
                    val appText = if(it.appType == ReportConfig.APP_MAIN) "主系统" else "副系统"
                    ReportInfoEntity(
                        id = null,
                        receive_time = it.startTime?.toString(),
                        receive_date_time = it.startTime?.let {
                            TimeUtil.formatCurrTime(it)
                        },
                        complete_time = it.endTime?.toString(),
                        complete_date_time = it.endTime?.let {
                            TimeUtil.formatCurrTime(it)
                        },
                        mobile_number = it.telNum,
                        wechat_type = if(it.appType == ReportConfig.APP_MAIN) "1" else "2",
                        mobile_number_show_name = "${it.telNum}(${appText})",
                        receive_count = it.count?.toString(),
                    )
                }
            emit(Resource.Success(list))
        }
    }.catch { e ->
        e.printStackTrace()
        emit(Resource.Error(exception = e))
    }

    private suspend fun queryRemote(appType: String?, page: Int?, size: Int?) = flow {
        emit(Resource.Loading)
        val appTypeList = mutableListOf<ReportConfig>()
        configDao.queryAll().forEach {
            if (appType != ReportConfig.APP_MAIN && appType != ReportConfig.APP_SUB) {
                appTypeList.add(it)
            } else if (appType == it.appType) {
                appTypeList.add(it)
            }
        }
        YLog.i("UploadResultRepository", "query: appType = $appType, size = ${appTypeList.size}")
        val config = appTypeList.firstOrNull() ?: kotlin.run {
            emit(Resource.Success(null))
            return@flow
        }
        val userEntity = userDao.queryByPath(config.databasePath) ?: kotlin.run {
            emit(Resource.Success(null))
            return@flow
        }
        val entity = ReportInfoRequestEntity(
            uploader_name = userEntity.nickname,
            uploader_id = userEntity.username,
            uploader_id_alias = userEntity.alias,
            mobile_number = config.telNo,
            wechat_type = getType(config.appType),
            page = page,
            page_size = size,
            list_type = getType(appType),
        )
        val info = api.getUploadHistoryOfChatHistory(entity)
        emit(Resource.Success(info.getData()?.list))
    }.catch { e ->
        e.printStackTrace()
        emit(Resource.Error(exception = e))
    }
}