package com.yupao.wxcollect.ui.upload_node

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.yupao.scafold.baseui.LoadingView
import com.yupao.utils.system.window.ScreenTool
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.FragmentNodeListBinding
import com.yupao.wxcollect.service.procedure.task.ReportType
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @Date 2023/4/18/018 14:22
 * @Description
 */
@AndroidEntryPoint
class UploadNodeFragment: Fragment() {

    companion object {
        private const val TAG = "UploadNodeFragment"

        private const val KEY_APP_TYPE = "appType"

        fun newInstance(@ReportType reportType: String?): Fragment {
            val fragment = UploadNodeFragment()
            fragment.arguments = bundleOf(
                KEY_APP_TYPE to reportType
            )
            return fragment
        }
    }

    @ReportType
    private val appType by lazy {
        arguments?.getString(KEY_APP_TYPE)
    }

    private val viewModel by lazy {
        ViewModelProvider(requireActivity())[UploadNodeViewModel::class.java]
    }

    var binding: FragmentNodeListBinding? = null

    private val loadingView: LoadingView? by lazy { binding?.loading }

    private val recyclerView by lazy {
        binding?.list
    }

    private val layoutManager : LinearLayoutManager by lazy {
        LinearLayoutManager(context)
    }

    private val adapter by lazy { UploadNodeAdapter(appType, requireContext()) }

    private val filterPopWindow by lazy {
        FilterMenuPopWindow(requireContext(), ScreenTool.getScreenRealWidth(requireContext()) / 2)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_node_list, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
        hindLoading()
    }

    private fun initView() {
        recyclerView?.apply {
            layoutManager = <EMAIL>
            adapter = <EMAIL>
        }
        binding?.typeContainer?.setOnClickListener {
            filterPopWindow.showPopWindow(it, viewModel.filterTypeUIState.value, false, viewModel::updateFilterType)
        }
        binding?.statusContainer?.setOnClickListener {
            filterPopWindow.showPopWindow(it, viewModel.filterStatusUIState.value, true, viewModel::updateFilterStatus)
        }
    }

    private fun showLoading() {
        loadingView?.startLoading()
        loadingView?.visibility = View.VISIBLE
    }

    private fun hindLoading() {
        loadingView?.stopLoading()
        loadingView?.visibility = View.GONE
    }

    private fun initObserve() {
        lifecycleScope.launch {
            viewModel.dataFlow
                .onEach { data ->
                    binding?.emptyView?.root?.visibility = if (data.isEmpty()) View.VISIBLE else View.GONE
                    YLog.i(TAG, "initObserve: data: ${data.firstOrNull()?.msg}, size: ${data.size}")
                    adapter.submitList(data) {
                        // 有新消息时滑动到顶部
                        layoutManager.scrollToPositionWithOffset(0, 0)
                        YLog.i(TAG, "initObserve: scroll to top")
                    }
                }
                .catch { e ->
                    YLog.printException("UploadNodeActivity-${appType}", e)
                }
                .collect()
        }
        lifecycleScope.launch {
            viewModel.filterType.collectLatest {
                val text = if (it.size == 1) it.firstOrNull()?.text else "${it.size}个"
                binding?.type?.text = "类型: $text"
            }
        }
        lifecycleScope.launch {
            viewModel.filterStatus.collectLatest {
                binding?.status?.text = "状态: ${it?.text ?: "全部"}"
            }
        }
        binding?.failedTaskView?.initClick(requireActivity().supportFragmentManager)
        lifecycleScope.launch {
            viewModel.failedTaskFlow.collectLatest {
                binding?.failedTaskView?.updateFailedTask(it)
            }
        }
    }
}