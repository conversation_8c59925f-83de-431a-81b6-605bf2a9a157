package com.yupao.wxcollect.ui.main

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.yupao.execute.wxcollect.BuildConfig
import com.yupao.execute.wxcollect.R
import com.yupao.keepalive.KeepAliveManager
import com.yupao.utils.system.window.DensityUtils
import com.yupao.utils.system.window.ScreenTool
import com.yupao.wxcollect.ui.BaseActivity
import com.yupao.wxcollect.ui.IReportClientManager
import com.yupao.wxcollect.ui.config.view.ConfigActivity
import com.yupao.wxcollect.ui.setup.SetupActivity
import com.yupao.wxcollect.ui.task_manager.TabActivity
import com.yupao.wxcollect.ui.task_manager.TaskManagerFragment
import com.yupao.wxcollect.ui.upload_node.UploadNodeActivity
import com.yupao.ylog.YLog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        addItem(R.drawable.ic_modify_config, "上传配置") {
            if (IReportClientManager.instance.checkClientRegister()) {
                startActivity(Intent(this, ConfigActivity::class.java))
            } else {
                Toast.makeText(this, "服务正在启动，请重试", Toast.LENGTH_SHORT).show()
            }
        }
        addItem(R.drawable.ic_upload_node, "上传执行") {
            UploadNodeActivity.start(this)
        }
        addItem(R.drawable.ic_task_manager, "任务管理") {
            TabActivity.start(this@MainActivity, TaskManagerFragment::class.java)
        }
        addItem(R.drawable.ic_settup, "设置") {
            startActivity(Intent(this, SetupActivity::class.java))
        }
        findViewById<View>(R.id.root).setOnClickListener {
            performClickDebug()
        }
        PermissionHelp.request(this, lifecycleScope) {
            YLog.i("MainActivity", "onPermissionAllowed: $it")
            PermissionHelp.turnOnNotificationListener(this)
            PermissionHelp.checkNotification()
            KeepAliveManager.checkPermission(this)
        }
    }

    private fun addItem(icon: Int, text: String, onClick: () -> Unit) {
        val item = View.inflate(this, R.layout.item_main_card, null)
        item.findViewById<ImageView>(R.id.icon).setImageResource(icon)
        item.findViewById<TextView>(R.id.name).text = text
        item.setOnClickListener {
            onClick()
        }
        val screenWidth = ScreenTool.getScreenWidth(this)
        val itemSize = (screenWidth - DensityUtils.dp2px(this, 36 + 36 + 24f)) / 2
        val layoutParams = ViewGroup.MarginLayoutParams(itemSize, itemSize)
        val margin = DensityUtils.dp2px(this, 12f)
        layoutParams.setMargins(margin, margin, margin, margin)
        findViewById<ViewGroup>(R.id.container).addView(item, layoutParams)
    }

    private var clickCount = 0

    private var lastClickTime = 0L

    private fun performClickDebug() {
        val currTime = System.currentTimeMillis()
        val lastTime = lastClickTime
        lastClickTime = currTime
        if (currTime - lastTime > 500) {
            clickCount = 1
            return
        }
        if (clickCount > if(BuildConfig.DEBUG) 2 else 5 ) {
            clickCount = 0
            startActivity(Intent(this, DebugActivity::class.java))
        } else {
            clickCount++
        }
    }
}