package com.yupao.wxcollect.ui.setup

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.*
import androidx.fragment.app.DialogFragment
import com.yupao.scafold.basebinding.AnimConfig
import com.yupao.scafold.basebinding.DataBindingConfigV2
import com.yupao.scafold.binding.BindViewMangerV2
import com.yupao.execute.wxcollect.R
import com.yupao.execute.wxcollect.databinding.DialogfragmentUpdateProcessBinding

/**
 * <AUTHOR>
 * @Date 2023/5/19/019 19:27
 * @Description
 */
class ProcessDialog: DialogFragment() {

    private var _binding: DialogfragmentUpdateProcessBinding? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.apply {
            AnimConfig.initShowAnim(AnimConfig.ANIM_TYPE_CENTER, this)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = BindViewMangerV2.getViewDataBinding(
            viewLifecycleOwner,
            inflater,
            container,
            DataBindingConfigV2(R.layout.dialogfragment_update_process, 0, null)
        )
        return _binding?.root
    }

    fun setProcess(_process: Int) {
        val process = _process.coerceAtLeast(0).coerceAtMost(100)
        _binding?.tips?.text = "App版本升级中: ${process}%"
        _binding?.process?.progress = process
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        isCancelable = false
        _binding?.process?.max = 100
        _binding?.close?.setOnClickListener {
            dismissAllowingStateLoss()
        }
    }

    override fun onStart() {
        super.onStart()
        clearPadding()
    }

    /**
     * 去掉padding，修复无法全屏
     */
    private fun clearPadding() {
        dialog?.window?.let { window ->
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//            window.setWindowAnimations(R.style.AnimDownToTop)
            requireActivity().windowManager.defaultDisplay.getMetrics(DisplayMetrics())
            window.decorView.setPadding(0, 0, 0, 0)
            val params =
                if (window.attributes != null) window.attributes else WindowManager.LayoutParams()
            params.dimAmount = 0f
            params.gravity = Gravity.CENTER
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = params
        }
    }
}