<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.yupao.execute.wxcollect">

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />

    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />

    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- 省电优化 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- 锁屏通知 -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:networkSecurityConfig="@xml/network_security_config"
        android:icon="@mipmap/ic_logo"
        android:label="@string/application_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:largeHeap="true"
        android:name="com.yupao.wxcollect.App"
        tools:replace="android:label"
        tools:targetApi="31">
        <activity
            android:name="com.yupao.wxcollect.ui.main.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name="com.yupao.wxcollect.ui.main.DebugActivity"
            android:exported="false"
            />

        <activity android:name="com.yupao.wxcollect.ui.config.view.ConfigActivity"
            android:exported="false"
            />
        <activity android:name="com.yupao.wxcollect.ui.setup.SetupActivity"
            android:exported="false"
            />
        <activity android:name="com.yupao.wxcollect.ui.upload_node.UploadNodeActivity"
            android:exported="false"
            android:launchMode="singleTask"
            />
        <activity android:name="com.yupao.wxcollect.ui.upload_result.UploadResultActivity"
            android:exported="false"
            />
        <activity android:name="com.yupao.wxcollect.ui.task_manager.TabActivity"
            android:exported="false"
            />

        <!-- 主要进程名需要跟material-files中多个组件同步，避免跨进程 -->
        <service
            android:name="com.yupao.wxcollect.service.ReportService"
            android:exported="true"
            android:process=":report"
            />

        <!-- 跟ReportService同一个进程，这样可以通过桌面进程定时唤醒 -->
        <receiver
            android:name="com.yupao.wxcollect.widget.WxReportProvider"
            android:process=":report"
            android:exported="true">

            <intent-filter>
                <action android:name="action_led_on" />
                <action android:name="action_led_off" />
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/app_widget_provider_info" />
        </receiver>

        <!-- 注册辅助功能服务-->
        <service
            android:name="com.yupao.wxcollect.ui.setup.WechatAccountService"
            android:exported="true"
            android:label="微信账号自动切换"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            android:process=":report">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <!--       通过xml文件完成辅助功能相关配置，也可以在onServiceConnected中动态配置-->
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_config" />
        </service>

        <service android:name="com.yupao.wxcollect.service.net_monitor.NetMonitorService"
            android:permission="android.permission.BIND_VPN_SERVICE"
            android:exported="true"
            android:label="VPN服务"
            android:process=":report">
            <intent-filter>
                <action android:name="android.net.VpnService"/>
            </intent-filter>
        </service>

        <provider
            android:name="com.yupao.wxcollect.ui.setup.MyFileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="${applicationId}.firebaseinitprovider"
            tools:node="remove"
             />

        <receiver android:name="com.yupao.wxcollect.service.PackageInstalledReceiver"
            android:enabled="true"
            android:exported="true">

            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>

        </receiver>

        <service android:name="com.yupao.wxcollect.service.notice.NotifyService"
            android:label="@string/application_name"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:process=":report">

            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>

        </service>

        <!--xposed 配置开始--> <!--xposedmodule： 表示这是一个xposed模块-->
        <meta-data android:name="xposedmodule" android:value="true"/>
        <!--xposeddescription: 描述该模块的用途，可以引用string.xml中的字符串-->
        <meta-data android:name="xposeddescription" android:value="监控好友申请"/>
        <!--xposedminversion：要求支持的Xposed Framework最低版本 并不是指定82 就写82-->
        <meta-data android:name="xposedminversion" android:value="30"/>
        <!--xposed 配置结束-->

        <activity
            android:name="com.yupao.wxcollect.ui.wechat.WechatSelectActivity"
            android:theme="@style/Theme.Transparent"
            android:process=":report"
            />
        <receiver
            android:name="com.yupao.wxcollect.service.NetworkConnectionIntentReceiver"
            android:process=":report"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
