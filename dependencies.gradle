//这两个版本号是对应的，不要随意修改
def lifecycle_version = "2.5.1"
def coroutines_android_version = "1.6.1"
def doraemon_version = "3.4.0-alpha04"
def exoplayer = "2.16.1"
//def yupao_retrofit_version = "3.0.0"
def retrofit_version = "2.9.0"
def camerax = "1.1.0"
def work_manager_version = "2.7.1"
def activity_version = "1.5.1"
def appcompat_version = "1.5.1"
def fragment_version = "1.5.5"
def navigation_version = "2.2.0"
def data_store_version = "1.0.0"
def volcengine_version = "6.13.3"
def sensors_version = "6.2.0"

//msdk
def M_SDK_VERSION = "3.9.0"
def M_SDK_VERSION_FIX = ".2"
//pangle
def PANGLE_SDK_VERSION = "5.0.0.4"
def PANGLE_ADAPTER_VERSION = ".0"
//ks
def KS_SDK_VERSION = "3.3.34"
def KS_ADAPTER_VERSION = ".0"
//baidu
def BAIDU_SDK_VERSION = "9.251"
def BAIDU_ADAPTER_VERSION = ".0"

ext {
    dependencies = [
            //android
            "appcompat"                     : "androidx.appcompat:appcompat:$appcompat_version",
            "coreKtx"                       : 'androidx.core:core-ktx:1.7.0',
            "activity"                      : "androidx.activity:activity:$activity_version",
            "activityKtx"                   : "androidx.activity:activity-ktx:$activity_version",
            "fragment"                      : "androidx.fragment:fragment:$fragment_version",
            "fragmentKtx"                   : "androidx.fragment:fragment-ktx:$fragment_version",
            "kotlinstdLib"                  : "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version",
            "kotlinstdLibCommon"            : "org.jetbrains.kotlin:kotlin-stdlib-common:$kotlin_version",
            "kotlinKAER"                    : "org.jetbrains.kotlin:kotlin-android-extensions-runtime:$kotlin_version",
            "annotations"                   : "org.jetbrains:annotations:13.0",
            "navFragmentKtx"                : "androidx.navigation:navigation-fragment-ktx:$navigation_version",
            "navUIKtx"                      : "androidx.navigation:navigation-ui-ktx:$navigation_version",
            "navSafeArgs"                   : "androidx.navigation:navigation-safe-args-gradle-plugin:$navigation_version",
            //控件
            "material"                      : 'com.google.android.material:material:1.2.1',
            "constraintlayout"              : 'androidx.constraintlayout:constraintlayout:2.0.4',
            "recyclerview"                  : 'androidx.recyclerview:recyclerview:1.2.0',
            "cardview"                      : 'androidx.cardview:cardview:1.0.0',
            //router
            "arouterApi"                    : "com.alibaba:arouter-api:1.5.2",
            "arouterCompiler"               : "com.alibaba:arouter-compiler:1.5.2",
            //recylerview相关
            "baseQuickAdapter"              : 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.30',
            "recyclerViewDivider"           : 'com.yqritc:recyclerview-flexibledivider:1.4.0', //ui recyclerview 分割线
            //刷新库
            "refreshKernel"                 : 'com.scwang.smart:refresh-layout-kernel:2.0.3',
            "refreshMaterialHeader"         : 'com.scwang.smart:refresh-header-material:2.0.3',
            "refreshClassicsFooter"         : 'com.scwang.smart:refresh-footer-classics:2.0.3',
            "refreshClassicsHeader"         : 'com.scwang.smart:refresh-header-classics:2.0.3',
            //Gson 解析
            "gson"                          : 'com.google.code.gson:gson:2.9.0',
            //event bus
            "eventBus"                      : 'org.greenrobot:eventbus:3.2.0',
            //google flexbox
            "flexbox"                       : 'com.google.android:flexbox:2.0.1',
            //权限(郭霖)
            "permissionX"                   : 'com.guolindev.permissionx:permissionx:1.6.4',
            //阿里云oss-sdk
            "aliyunOSS"                     : 'com.aliyun.dpa:oss-android-sdk:2.9.3',
            "aliyunVideoUpload"             : 'com.aliyun.video.android:upload:1.6.2',
            //glide
            "glide"                         : 'com.github.bumptech.glide:glide:4.12.0',
            //magicIndicator，tab滑块
//            "magicIndicator"                : 'com.github.hackware1993:MagicIndicator:1.7.0',
            //loading库
            "aviLoadingLib"                 : 'com.wang.avi:library:2.1.3',
            //气泡
            "badgeView"                     : 'q.rorbin:badgeview:1.1.3',
            //圆形图片
            "circleImageView"               : 'de.hdodenhof:circleimageview:2.1.0',
            //文件下载
            "fileDownloader"                : 'com.liulishuo.filedownloader:library:1.7.2',
            //textbannerview
            "textBannerView"                : 'com.superluo:textbannerview:1.0.5',
            //瓦力打包
            "walle"                         : 'com.meituan.android.walle:library:1.1.6',
            //阿里热修复
            "aliHotFix"                     : 'com.aliyun.ams:alicloud-android-hotfix:3.3.3',

            "keyboardvisibilityevent"       : 'net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:2.1.0',
            //侧边字母
            "wavesidebar"                   : 'com.gjiazhe:wavesidebar:1.3',
            //字母排序
            "indexRecyclerView"             : 'com.github.jiang111:IndexRecyclerView:v1.1',
            //PickerView
            "pickerView"                    : 'com.contrarywind:Android-PickerView:4.1.8',
            "multidex"                      : 'androidx.multidex:multidex:2.0.0',
            "switchbutton"                  : 'ch.ielse:switchbutton:1.0.1',
            //okhttp
            "retrofit"                      : "com.squareup.retrofit2:retrofit:$retrofit_version",
            "retrofit_converter_gson"       : "com.squareup.retrofit2:converter-gson:$retrofit_version",
            "retrofit_rxjava"               : "com.squareup.retrofit2:adapter-rxjava2:$retrofit_version",
            "okhttp"                        : 'com.squareup.okhttp3:okhttp:4.9.0',
            "okio"                          : 'com.squareup.okio:okio:2.8.0',
            //mmkv
            "mmkv"                          : 'com.tencent:mmkv-static:1.2.7',
            "fontresize"                    : 'com.ljx.view:fontresize:1.0.1',
            "jiaozivideoplayer"             : 'cn.jzvd:jiaozivideoplayer:7.4.2',
            "fastjson"                      : 'com.alibaba:fastjson:1.2.31',
            //rx一套
            "rxandroid"                     : 'io.reactivex.rxjava2:rxandroid:2.0.1',
            "rxjava"                        : 'io.reactivex.rxjava2:rxjava:2.0.5',
            //room
            "roomRuntime"                   : 'androidx.room:room-runtime:2.4.0',
            "roomCompiler"                  : 'androidx.room:room-compiler:2.4.0',
            "roomKtx"                       : 'androidx.room:room-ktx:2.4.0',
            //databinding
            "databindingCompiler"           : 'com.android.databinding:compiler:3.1.4',
            "coroutines_core"               : "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines_android_version",
            "coroutines_android"            : "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_android_version",
            "coroutines_jdk8"               : "org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:$coroutines_android_version",
            "coroutines_jdk7"               : "org.jetbrains.kotlinx:kotlinx-coroutines-jdk7:$coroutines_android_version",
            "coroutines_futures"            : "androidx.concurrent:concurrent-futures-ktx:1.1.0",
            //ViewModel
            "lifecycle_viewmodel_ktx"       : "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version",
            // LiveData
            "lifecycle_livedata_ktx"        : "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version",
            // Lifecycles only (without ViewModel or LiveData)
            "lifecycle_runtime_ktx"         : "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version",
            // Saved state module for ViewModel
            "lifecycle_viewmodel_savedstate": "androidx.lifecycle:lifecycle-viewmodel-savedstate:$lifecycle_version",
            // Annotation processor
            "lifecycle_compiler"            : "androidx.lifecycle:lifecycle-compiler:$lifecycle_version",
            // alternately - if using Java8, use the following instead of lifecycle-compiler
            "lifecycle_common_java8"        : "androidx.lifecycle:lifecycle-common-java8:$lifecycle_version",
            // optional - helpers for implementing LifecycleOwner in a Service
            "lifecycle_service"             : "androidx.lifecycle:lifecycle-service:$lifecycle_version",
            // optional - ProcessLifecycleOwner provides a lifecycle for the whole application process
            "lifecycle_process"             : "androidx.lifecycle:lifecycle-process:$lifecycle_version",
            // optional - ReactiveStreams support for LiveData
            "lifecycle_reactivestreams_ktx" : "androidx.lifecycle:lifecycle-reactivestreams-ktx:$lifecycle_version",
            //lifecycle-extensions
            "lifecycle_extensions"          : 'androidx.lifecycle:lifecycle-extensions:2.0.0',
            // doraemon
            "dokitx"                        : "io.github.didi.dokit:dokitx:$doraemon_version",
            "dokitx_ft"                     : "io.github.didi.dokit:dokitx-ft:$doraemon_version",
            "dokitx_mc"                     : "io.github.didi.dokit:dokitx-mc:$doraemon_version",
            "dokitx_weex"                   : "io.github.didi.dokit:dokitx-weex:$doraemon_version",
            "dokitx_no_op"                  : "io.github.didi.dokit:dokitx-no-op:$doraemon_version",
            //exoplayer
            "exoplayer_core"                : "com.google.android.exoplayer:exoplayer-core:$exoplayer",
            "exoplayer_ui"                  : "com.google.android.exoplayer:exoplayer-ui:$exoplayer",
            "exoplayer_dash"                : "com.google.android.exoplayer:exoplayer-dash:$exoplayer",
            "lottie"                        : "com.airbnb.android:lottie:4.0.0",
            //友盟统计SDK
            "umsdk_common"                  : "com.umeng.umsdk:common:9.5.2",
            "umsdk_asms"                    : "com.umeng.umsdk:asms:1.6.3",
            //错误分析升级为独立SDK，看crash数据请一定集成
            "umsdk_apm"                     : "com.umeng.umsdk:apm:1.7.0",
            "uverify"                       : 'com.umeng.umsdk:uverify:2.5.8',
            "uverify_main"                  : 'com.umeng.umsdk:uverify-main:2.1.4',
            "uverify_logger"                : 'com.umeng.umsdk:uverify-logger:2.1.4',
            "uverify_crashshield"           : 'com.umeng.umsdk:uverify-crashshield:2.1.4',
            //使用U-App中ABTest能力
//            "umsdk_abtest"                  : "com.umeng.umsdk:abtest:1.0.0",
            "jsoup"                         : "org.jsoup:jsoup:1.12.1",
            "zxing_core"                    : "com.google.zxing:core:3.3.3",
//            "tt_applog"                     : "com.bytedance.applog:RangersAppLog-Lite-cn:6.1.2",
//            "onekit"                        : "com.volcengine.onekit:onekit:0.0.1-rc.5",
            "wx_share"                      : "com.tencent.mm.opensdk:wechat-sdk-android:6.8.0",
            "photoview"                     : 'com.github.chrisbanes.photoview:library:1.2.4',
            "magicindicator"                : 'com.yupao.widget:magicindicator:1.0',
            "sweetalert"                    : 'com.github.f0ris.sweetalert:library:1.5.1',
            "legacy_support_v4"             : 'androidx.legacy:legacy-support-v4:1.0.0',
            "lifecycle_reactivestreams"     : "androidx.lifecycle:lifecycle-reactivestreams:$lifecycle_version",
            "kotlin_reflect"                : "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version",
//            android物理动画库
            "dynamicanimation"              : 'androidx.dynamicanimation:dynamicanimation:1.0.0',
            "rxpermissions2"                : 'com.tbruyelle.rxpermissions2:rxpermissions:0.9.5@aar',
            "reactive_streams"              : 'org.reactivestreams:reactive-streams:1.0.2',
            "tinypinyin"                    : 'com.github.promeg:tinypinyin:1.0.0',
            "bottom_navigation_bar"         : 'com.ashokvarma.android:bottom-navigation-bar:2.0.4',
            "wheel_picker"                  : 'com.github.gzu-liyujiang.AndroidPicker:WheelPicker:1.5.5',
            //camerax
            "camerax_core"                  : "androidx.camera:camera-core:$camerax",
            "camerax_camera2"               : "androidx.camera:camera-camera2:$camerax",
            "camerax_lifecycle"             : "androidx.camera:camera-lifecycle:$camerax",
            "camerax_video"                 : "androidx.camera:camera-video:$camerax",
            "camerax_view"                  : "androidx.camera:camera-view:$camerax",
            "camerax_extensions"            : "androidx.camera:camera-extensions:$camerax",
            //日历组件
            "hbCalendarView"                : "com.haibin:calendarview:3.7.1",

            //视频压缩ffmpeg命令行
            "rxffmpeg"                      : "com.github.hellomr3:rxffmpeg:5.0.0",
            "hilt"                          : "com.google.dagger:hilt-android:$hilt_version",
            "hiltHiltCompiler"              : "androidx.hilt:hilt-compiler:1.0.0",
            "hiltCompiler"                  : "com.google.dagger:hilt-android-compiler:$hilt_version",
            //utils dependencies
//            "ucrop"                         : "com.yupao.utils:ucrop:$UCROP_VERSION",
            "ucrop"                         : "com.github.hellomr3.PictureSelector:ucrop:$UCROP_VERSION",
            "utils_keyboard"                : "com.yupao.utils:utils_keyboard:$KEYBOARD_VERSION",
            "utils_xbus"                    : "com.yupao.utils:utils_xbus:$XBUS_VERSION",
            "utils_str"                     : "com.yupao.utils:utils_str:$STR_VERSION",
            "utils_lang"                    : "com.yupao.utils:utils_lang:$LANG_VERSION",
            "utils_log"                     : "com.yupao.utils:utils_log:$LOG_VERSION",
            "utils_datetime"                : "com.yupao.utils:utils_datetime:$DATATIME_VERSION",
            "utils_system"                  : "com.yupao.utils:utils_system:$SYSTEM_VERSION",
            "utils_view"                    : "com.yupao.utils:utils_view:$VIEW_VERSION",
            "utils_common"                  : "com.yupao.utils:utils_common:$COMMON_VERSION",
            "utils_qr"                      : "com.yupao.utils:utils_qr:$QR_VERSION",

            "utils_xcrash"                  : "com.yupao.utils:xcrash:$XCRASH_VERSION",
            "auto_pack"                     : "com.yupao.plugin:pack:$AUTO_PACK_VERSION",
            "mcImage"                       : "com.yupao.plugin:mcImage:$MC_IMAGE_VERSION",
            "storage"                       : "com.yupao.storage:storage:$STORAGE_VERSION",
            "sharePreference"               : "com.yupao.storage:sharePreference:$SHARE_PREFERENCE_VERSION",
            "buried_point"                  : "com.yupao.pointer:buried_point:$BURIED_POINT_VERSION",
            "abnormal"                      : "com.yupao.abnormal:abnormal:$ABNORMAL_VERSION",
            "data_protocol"                 : "com.yupao.data:data_protocol:$DATA_PROTOCOL_VERSION",
            "netlibrary"                    : "com.yupao.net:netlibrary:$NETLIBRARY_VERSION",
            "feature_statistics"            : "com.yupao.statistics:feature_statistics:$FEATURE_STATISTICS_VERSION",
            "wb_cloud_face"                 : "com.yupao.wb:wb_cloud_face:$WB_CLOUD_FACE_VERSION",
            "map"                           : "com.yupao.map:map:$MAP_VERSION",
            "sharelib"                      : "com.yupao.share:sharelib:$SHARELIB_VERSION",
            "pushlibrary"                   : "com.yupao.push:pushlibrary:$PUSHLIBRARY_VERSION",
            "picture_library"               : "com.yupao.picture:picture_library:$PICTURE_LIBRARY_VERSION",
            "feature_mvvm"                  : "com.yupao.scafold:feature_mvvm:$FEATURE_MVVM_VERSION",
            "feature_mvvm_protocol"         : "com.yupao.scafold:feature_mvvm_protocol:$FEATURE_MVVM_PROTOCOL_VERSION",
            "feature_mvvm_base"             : "com.yupao.scafold:feature_mvvm_base:$FEATURE_MVVM_BASE_VERSION",
            "phone_auth"                    : "com.yupao.auth:phone_auth:$PHONE_AUTH",

            //widget dependencies
            "widget_base"                   : "com.yupao.widget:widget_base:$WIDGET_BASE_VERSION",
            "widget_banner"                 : "com.yupao.widget:widget_banner:$WIDGET_BANNER_VERSION",
            "widget_font"                   : "com.yupao.widget:widget_font:$WIDGET_FONT_VERSION",
            "widget_guide"                  : "com.yupao.widget:widget_guide:$WIDGET_GUIDE_VERSION",
            "widget_image"                  : "com.yupao.widget:widget_image:$WIDGET_IMAGE_VERSION",
            "widget_pick"                   : "com.yupao.widget:widget_pick:$WIDGET_PICK_VERSION",
            "widget_recyclerview"           : "com.yupao.widget:widget_recyclerview:$WIDGET_RECYCLERVIEW_VERSION",
            "widget_text"                   : "com.yupao.widget:widget_text:$WIDGET_TEXT_VERSION",
            "widget_view"                   : "com.yupao.widget:widget_view:$WIDGET_VIEW_VERSION",
            "bridge_web"                    : "com.yupao.bridge_webview:bridge_webview:$BRIDGE_WEB_VERSION",

            //work-manager
            "hiltWork"                      : "androidx.hilt:hilt-work:1.0.0",
            "workManager"                   : "androidx.work:work-runtime-ktx:$work_manager_version",

            //apng
            "apng"                          : "com.github.penfeizhou.android.animation:apng:2.21.0",
            "apng_glide_plugin"             : "com.github.penfeizhou.android.animation:glide-plugin:2.21.0",
            "jdk8_support_dex"              : "com.android.tools:desugar_jdk_libs:1.1.6",
            // 火山引擎埋点
            "volcengine"                    : "com.bytedance.applog:RangersAppLog-All-cn:$volcengine_version",
            // 火山引擎埋点scheme
            "volcengine_scheme"             : "com.bytedance.applog:RangersAppLog-All-scheme:$volcengine_version",
//            "sensors_data"                    : "com.sensorsdata.analytics.android:SensorsAnalyticsSDK:$sensors_version",

            //im
            "im_sdk"                        : "io.hyphenate:hyphenate-chat:3.9.8",
            "trtc_sdk"                      : "com.tencent.liteav:LiteAVSDK_TRTC:9.9.0.11821",

            //DataStore
            "datastore_preferences"         : "androidx.datastore:datastore-preferences:$data_store_version",
            "datastore_datastore"           : "androidx.datastore:datastore:$data_store_version",

            "volcengine"                    : "com.bytedance.applog:RangersAppLog-All-cn:$volcengine_version",
            "volcengine_scheme"             : "com.bytedance.applog:RangersAppLog-All-scheme:$volcengine_version",
//            "sensors"                       : "com.sensorsdata.analytics.android:SensorsAnalyticsSDK:$sensors_version",
            "leakcanary"                    : "com.squareup.leakcanary:leakcanary-android-noauto:3.1.1",
            "leakcanary_release"            : "com.squareup.leakcanary:leakcanary-android-noauto-release:3.1.1",

            //GroMore_sdk
            "gromore"                       : "com.gromore.cn:gromore-sdk:${M_SDK_VERSION}${M_SDK_VERSION_FIX}",
            "gromore_test"                  : "com.gromore.cn:gromore-test-tools:${M_SDK_VERSION}${M_SDK_VERSION_FIX}",
            "pangle_adapter"                : "com.gromore.cn:pangle-adapter:${PANGLE_SDK_VERSION}${PANGLE_ADAPTER_VERSION}",
            "ks_adapter"                    : "com.gromore.cn:ks-adapter:${KS_SDK_VERSION}${KS_ADAPTER_VERSION}",
            "baidu_adapter"                 : "com.gromore.cn:baidu-adapter:${BAIDU_SDK_VERSION}${BAIDU_ADAPTER_VERSION}",
            // anr监控
            "yanr"                          : "com.yupao.yapm:yanr:1.7.4",
            "yapm"                          : "com.yupao.yapm:yapm:1.7.6",
            "ylog"                          : "com.yupao.yapm:ylog:0.8",
            "asr"                           : "com.yupao.asr:asr:1.3.3",
            "so_load"                       : "com.yupao.so_load:so_load:1.2.0",
            "x5_web"                        : "com.yupao.widget:widget_x5:1.0.2"
    ]
}