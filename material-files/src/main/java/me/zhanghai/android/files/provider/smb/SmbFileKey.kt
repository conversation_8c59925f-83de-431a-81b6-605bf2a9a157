/*
 * Copyright (c) 2020 <PERSON> <<EMAIL>>
 * All Rights Reserved.
 */

package me.zhanghai.android.files.provider.smb

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import me.zhanghai.android.files.util.hash

@Parcelize
internal class SmbFileKey(
    private val path: SmbPath,
    private val fileId: Long
) : Parcelable {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) {
            return true
        }
        if (javaClass != other?.javaClass) {
            return false
        }
        other as SmbFile<PERSON>ey
        return if (fileId != 0L || other.fileId != 0L) {
            path.authority == other.path.authority
                && path.sharePath!!.name == other.path.sharePath!!.name
                && fileId == other.fileId
        } else {
            path == other.path
        }
    }

    override fun hashCode(): Int {
        return if (fileId != 0L) {
            hash(path.authority, path.sharePath!!.name, fileId)
        } else {
            path.hashCode()
        }
    }
}
