/*
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 * All Rights Reserved.
 */

package me.zhanghai.android.files.provider.common

import java8.nio.file.AccessMode

class AccessModes(val read: <PERSON><PERSON><PERSON>, val write: <PERSON><PERSON><PERSON>, val execute: <PERSON><PERSON><PERSON>)

fun Array<out AccessMode>.toAccessModes(): AccessModes {
    var read = false
    var write = false
    var execute = false
    for (mode in this) {
        when (mode) {
            AccessMode.READ -> read = true
            AccessMode.WRITE -> write = true
            AccessMode.EXECUTE -> execute = true
            else -> throw UnsupportedOperationException(mode.toString())
        }
    }
    return AccessModes(read, write, execute)
}
