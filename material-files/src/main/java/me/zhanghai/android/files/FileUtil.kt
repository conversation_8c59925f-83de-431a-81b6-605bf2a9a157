package me.zhanghai.android.files

import android.content.Context
import android.util.Log
import androidx.annotation.Keep
import com.google.android.gms.common.util.CollectionUtils
import java8.nio.file.FileVisitResult
import java8.nio.file.Files
import java8.nio.file.Path
import java8.nio.file.SimpleFileVisitor
import java8.nio.file.attribute.BasicFileAttributes
import me.zhanghai.android.files.filejob.FileJobService
import me.zhanghai.android.files.provider.common.PosixFileModeBit
import me.zhanghai.android.files.provider.linux.LinuxFileSystemProvider
import java.io.IOException

/**
 * <AUTHOR>
 * @Date 2023/4/4/004 10:07
 * @Description
 */
object FileUtil {

    fun copy(
        context: Context,
        fromFirst: String,
        fromMore: Array<String>,
        toFirst: String,
        toMore: Array<String>,
        callback: ((<PERSON>olean) -> Unit)? = null
    ) {
        val wx: Path = LinuxFileSystemProvider.fileSystem.getPath(
            first = fromFirst,
            more = *fromMore
        )
        val toWx: Path = LinuxFileSystemProvider.fileSystem.getPath(
            first = toFirst,
            more = *toMore
        )
        val job = FileJobService.copy(CollectionUtils.listOf(wx), toWx, context, true)
        job.callback = callback
    }

    fun delete(
        context: Context,
        pathList: List<String>?,
        callback: ((Boolean) -> Unit)? = null
    ) {
        if (pathList.isNullOrEmpty()) {
            callback?.invoke(false)
            return
        }
        val result = pathList.map {
            LinuxFileSystemProvider.fileSystem.getPath(
                first = "/",
                more = arrayOf(it)
            )
        }
        val job = FileJobService.delete(result, context)
        job.callback = callback
    }

    fun setMode(
        context: Context?,
        first: String,
        more: Array<String>,
        callback: ((Boolean) -> Unit)? = null,
    ) {
        val toWx: Path = LinuxFileSystemProvider.fileSystem.getPath(
            first = first,
            more = *more
        )
        val job = FileJobService.setMode(
            path = toWx,
            mode = setOf(
                PosixFileModeBit.OWNER_READ,
                PosixFileModeBit.OWNER_WRITE,
                PosixFileModeBit.OWNER_EXECUTE,
                PosixFileModeBit.GROUP_READ,
                PosixFileModeBit.GROUP_WRITE,
                PosixFileModeBit.GROUP_EXECUTE,
                PosixFileModeBit.OTHERS_READ,
                PosixFileModeBit.OTHERS_WRITE,
                PosixFileModeBit.OTHERS_EXECUTE,
            ),
            recursive = true,
            uppercaseX = true,
            context = context ?: return
        )
        job.callback = callback
    }

    fun isFileExisted(filePath: String?): Boolean {
        if (filePath.isNullOrEmpty()) {
            return false
        }
        val source = if(filePath.startsWith("/")) filePath.substring(1) else filePath
        val path: Path = LinuxFileSystemProvider.fileSystem.getPath(
            first = "/",
            more = arrayOf(source)
        )
        Log.d("FileUtil", "isFileExisted: path = $path")
        return Files.exists(path)
    }

    /**
     * 获取某个目录下所有的一级子目录
     */
    fun getChildFilePath(filePath: String?): List<String> {
        if (filePath.isNullOrEmpty()) {
            return listOf()
        }
        Log.d("FileUtil", "getChildFilePath: filePath = $filePath")
        val source = if(filePath.startsWith("/")) filePath.substring(1) else filePath
        val path: Path = LinuxFileSystemProvider.fileSystem.getPath(
            first = "/",
            more = arrayOf(source)
        )
        val list = mutableListOf<FileEntity>()
        val startIndex = path.toString().length
        Files.walkFileTree(path, object : SimpleFileVisitor<Path>() {
            override fun preVisitDirectory(
                dir: Path?,
                attrs: BasicFileAttributes?
            ): FileVisitResult {
                val dirPath = dir?.toString()
                val index = dirPath?.indexOf("/", startIndex) ?: -1
                // 最多访问一层
                return if (index > 0) {
                    if (dirPath != null) {
                        val lastModifyTime = attrs?.lastModifiedTime()?.toMillis()
                        val entity = FileEntity(dirPath, lastModifyTime)
                        if (!list.contains(entity)) {
                            list.add(entity)
                        }
                    }
                    FileVisitResult.SKIP_SUBTREE
                } else {
                    FileVisitResult.CONTINUE
                }
            }

            override fun visitFile(file: Path?, attrs: BasicFileAttributes?): FileVisitResult {
                return super.visitFile(file, attrs)
            }

            override fun visitFileFailed(file: Path?, exc: IOException?): FileVisitResult {
                exc?.printStackTrace()
                return super.visitFileFailed(file, exc)
            }
        })
        // 按修改时间逆序
        val result = list.sortedBy {
            -1 * (it.lastModifyTime ?: 0)
        }.mapNotNull {
            it.dirPath
        }
        Log.d("FileUtil", "$filePath child file list: $result")
        return result
    }
}

@Keep
private data class FileEntity(
    val dirPath: String?,
    val lastModifyTime: Long?,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FileEntity

        if (dirPath != other.dirPath) return false

        return true
    }

    override fun hashCode(): Int {
        return dirPath?.hashCode() ?: 0
    }
}