/*
 * Copyright (c) 2019 <PERSON> <<EMAIL>>
 * All Rights Reserved.
 */

package me.zhanghai.android.files.filejob

import android.util.Log
import me.zhanghai.android.files.util.showToast
import java.io.IOException
import java.io.InterruptedIOException
import java.util.Random

abstract class FileJob {
    val id = Random().nextInt()

    internal lateinit var service: FileJobService
        private set

    var callback: ((Boolean) -> Unit)? = null

    fun runOn(service: FileJobService) {
        this.service = service
        try {
            run()
            Log.i("TAG", "FileJob runOn: $callback")
            callback?.invoke(true)
            // TODO: Toast
        } catch (e: InterruptedIOException) {
            // TODO
//            e.printStackTrace()
            callback?.invoke(false)
        } catch (e: Exception) {
//            e.printStackTrace()
            service.showToast(e.toString())
            callback?.invoke(false)
        } finally {
            service.notificationManager.cancel(id)
        }
    }

    @Throws(IOException::class)
    protected abstract fun run()
}
