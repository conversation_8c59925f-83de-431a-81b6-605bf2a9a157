<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 Hai Zhang <<EMAIL>>
  ~ All Rights Reserved.
  -->

<resources>
    <string name="file_type_name_unknown">File</string>
    <string name="file_type_name_apk">Android package</string>
    <string name="file_type_name_archive">%1$s archive</string>
    <string name="file_type_name_audio">%1$s audio</string>
    <string name="file_type_name_calendar">%1$s calendar</string>
    <string name="file_type_name_certificate">%1$s certificate</string>
    <string name="file_type_name_code">%1$s document</string>
    <string name="file_type_name_contact">Electronic business card</string>
    <string name="file_type_name_directory">Folder</string>
    <string name="file_type_name_document">%1$s document</string>
    <string name="file_type_name_ebook">%1$s e-book</string>
    <string name="file_type_name_email">%1$s email message</string>
    <string name="file_type_name_font">%1$s font</string>
    <string name="file_type_name_generic">%1$s file</string>
    <string name="file_type_name_image">%1$s image</string>
    <string name="file_type_name_pdf">PDF document</string>
    <string name="file_type_name_presentation">%1$s presentation</string>
    <string name="file_type_name_spreadsheet">%1$s spreadsheet</string>
    <string name="file_type_name_text">%1$s document</string>
    <string name="file_type_name_text_plain">Plain text document</string>
    <string name="file_type_name_video">%1$s video</string>
    <string name="file_type_name_word">Word document</string>
    <string name="file_type_name_powerpoint">PowerPoint presentation</string>
    <string name="file_type_name_excel">Excel spreadsheet</string>
    <string name="file_type_name_posix_character_device">Character device</string>
    <string name="file_type_name_posix_block_device">Block device</string>
    <string name="file_type_name_posix_fifo">Pipe</string>
    <string name="file_type_name_posix_symbolic_link">Link</string>
    <string name="file_type_name_posix_socket">Socket</string>
    <string name="file_type_name_posix_symbolic_link_broken">Link (broken)</string>
</resources>
