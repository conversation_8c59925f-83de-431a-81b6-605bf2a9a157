<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceCategory android:title="@string/settings_interface_title">

        <me.zhanghai.android.files.theme.custom.ThemeColorPreference
            android:key="@string/pref_key_theme_color"
            android:title="@string/settings_theme_color_title"
            android:defaultValue="@string/pref_default_value_theme_color"
            android:dependency="@string/pref_key_material_design_3"
            android:summary="@string/settings_theme_color_summary" />

        <SwitchPreferenceCompat
            android:key="@string/pref_key_material_design_3"
            android:title="@string/settings_material_design_3_title"
            android:defaultValue="@bool/pref_default_value_material_design_3"
            android:disableDependentsState="true" />

        <rikka.preference.SimpleMenuPreference
            android:key="@string/pref_key_night_mode"
            android:title="@string/settings_night_mode_title"
            android:defaultValue="@string/pref_default_value_night_mode"
            android:entries="@array/settings_night_mode_entries"
            android:entryValues="@array/pref_entry_values_night_mode"
            app:useSimpleSummaryProvider="true" />

        <SwitchPreferenceCompat
            android:key="@string/pref_key_black_night_mode"
            android:title="@string/settings_black_night_mode"
            android:defaultValue="@bool/pref_default_value_black_night_mode" />

        <SwitchPreferenceCompat
            android:key="@string/pref_key_file_list_animation"
            android:title="@string/settings_file_list_animation_title"
            android:defaultValue="@bool/pref_default_value_file_list_animation" />

        <rikka.preference.SimpleMenuPreference
            android:key="@string/pref_key_file_name_ellipsize"
            android:title="@string/settings_file_name_ellipsize_title"
            android:defaultValue="@string/pref_default_value_file_name_ellipsize"
            android:entries="@array/settings_file_name_ellipsize_entries"
            android:entryValues="@array/pref_entry_values_file_name_ellipsize"
            app:useSimpleSummaryProvider="true" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/settings_behavior_title">

        <me.zhanghai.android.files.settings.DefaultDirectoryPreference
            android:key="@string/pref_key_file_list_default_directory"
            android:title="@string/settings_default_directory_title"
            app:useSimpleSummaryProvider="true" />

        <me.zhanghai.android.files.settings.StoragesPreference
            android:title="@string/settings_storages_title"
            android:summary="@string/settings_storages_summary_empty" />

        <me.zhanghai.android.files.settings.StandardDirectoriesPreference
            android:title="@string/settings_standard_directories_title"
            android:summary="@string/settings_standard_directories_summary_empty" />

        <me.zhanghai.android.files.settings.BookmarkDirectoriesPreference
            android:title="@string/settings_bookmark_directories_title"
            android:summary="@string/settings_bookmark_directories_summary_empty" />

        <me.zhanghai.android.files.settings.RootStrategyPreference
            android:key="@string/pref_key_root_strategy"
            android:title="@string/settings_root_strategy_title"
            android:defaultValue="@string/pref_default_value_root_strategy"
            android:entries="@array/settings_root_strategy_entries"
            android:entryValues="@array/pref_entry_values_root_strategy"
            app:useSimpleSummaryProvider="true" />

        <me.zhanghai.android.files.settings.CharsetPreference
            android:key="@string/pref_key_archive_file_name_encoding"
            android:title="@string/settings_archive_file_name_encoding_title"
            android:defaultValue="@string/pref_default_value_archive_file_name_encoding"
            app:useSimpleSummaryProvider="true" />

        <rikka.preference.SimpleMenuPreference
            android:key="@string/pref_key_open_apk_default_action"
            android:title="@string/settings_open_apk_default_action_title"
            android:defaultValue="@string/pref_default_value_open_apk_default_action"
            android:entries="@array/settings_open_apk_default_action_entries"
            android:entryValues="@array/pref_entry_values_open_apk_default_action"
            app:useSimpleSummaryProvider="true" />

        <SwitchPreferenceCompat
            android:key="@string/pref_key_show_pdf_thumbnail_pre_28"
            android:title="@string/settings_show_pdf_thumbnail_pre_28_title"
            android:defaultValue="@bool/pref_default_value_show_pdf_thumbnail_pre_28"
            android:summary="@string/settings_show_pdf_thumbnail_pre_28_summary"
            app:isPreferenceVisible="@bool/pref_visible_show_pdf_thumbnail_pre_28" />

        <SwitchPreferenceCompat
            android:key="@string/pref_key_read_remote_files_for_thumbnail"
            android:title="@string/settings_read_remote_files_for_thumbnail_title"
            android:defaultValue="@bool/pref_default_value_read_remote_files_for_thumbnail" />
    </PreferenceCategory>
</PreferenceScreen>
