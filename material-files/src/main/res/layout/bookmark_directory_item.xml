<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<me.zhanghai.android.foregroundcompat.ForegroundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/two_line_list_item_height"
    android:background="?android:colorBackground"
    android:foreground="?selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="@dimen/icon_size"
        android:layout_height="@dimen/icon_size"
        android:layout_marginStart="@dimen/screen_edge_margin"
        android:layout_marginEnd="@dimen/content_start_from_screen_edge_margin_minus_24dp"
        android:scaleType="centerCrop"
        android:src="@drawable/directory_icon_white_24dp"
        app:tint="?colorControlNormal"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Lint says android:ellipsize="middle" and android:maxLines="1" crashes. -->
        <TextView
            android:id="@+id/nameText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?textAppearanceListItem" />

        <TextView
            android:id="@+id/pathText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?textAppearanceListItemSecondary"
            android:textColor="?android:textColorSecondary" />
    </LinearLayout>

    <ImageView
        android:id="@+id/dragHandleView"
        android:layout_width="@dimen/touch_target_size"
        android:layout_height="@dimen/touch_target_size"
        android:layout_marginStart="@dimen/content_start_from_screen_edge_margin_minus_36dp_considering_at_least_8dp"
        android:layout_marginEnd="@dimen/screen_edge_margin_minus_12dp_at_least_8dp"
        android:padding="@dimen/touch_target_icon_padding"
        android:src="@drawable/drag_handle_icon_white_24dp"
        app:tint="?colorControlNormal" />
</me.zhanghai.android.foregroundcompat.ForegroundLinearLayout>
