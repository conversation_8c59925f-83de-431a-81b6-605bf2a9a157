<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<me.zhanghai.android.foregroundcompat.ForegroundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:paddingStart="12dp"
    android:paddingEnd="4dp"
    android:foreground="@drawable/breadcrumb_item_foreground"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:duplicateParentState="true"
        android:ellipsize="end"
        android:maxLines="1"
        android:textAppearance="@style/TextAppearance.Design.Tab"
        app:textAllCaps="false" />

    <ImageView
        android:id="@+id/arrowImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-4dp"
        android:duplicateParentState="true"
        android:src="@drawable/arrow_end_icon_white_24dp" />
</me.zhanghai.android.foregroundcompat.ForegroundLinearLayout>
