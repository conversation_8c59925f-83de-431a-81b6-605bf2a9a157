<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/abc_dialog_title_divider_material">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="vertical"
        android:scrollIndicators="top|bottom">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="?dialogPreferredPadding"
            android:paddingEnd="?dialogPreferredPadding"
            android:paddingTop="4dp"
            android:clipToPadding="false"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/seLinuxContextEdit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:importantForAutofill="no"
                    android:inputType="text">
                    <requestFocus />
                </com.google.android.material.textfield.TextInputEditText>
            </com.google.android.material.textfield.TextInputLayout>

            <CheckBox
                android:id="@+id/recursiveCheck"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="-7dp"
                android:layout_marginTop="8dp"
                android:paddingStart="@dimen/dialog_padding_minus_7dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="@string/file_properties_permissions_recursive"
                android:textAppearance="?textAppearanceListItem" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>
