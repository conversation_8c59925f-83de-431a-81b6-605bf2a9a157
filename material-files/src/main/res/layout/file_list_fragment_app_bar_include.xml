<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- app:statusBarBackground is broken when scrolling. -->
    <me.zhanghai.android.files.ui.CoordinatorAppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?colorAppBarSurface"
        android:theme="?actionBarTheme"
        app:liftOnScroll="true"
        app:liftOnScrollTargetViewId="@id/recyclerView">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_scrollFlags="@integer/file_list_toolbar_scroll_flags">

            <me.zhanghai.android.files.ui.CrossfadeSubtitleToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?actionBarSize"
                android:paddingStart="@dimen/file_list_toolbar_padding_start"
                android:paddingEnd="@dimen/file_list_toolbar_padding_end_with_overflow"
                app:navigationIcon="@drawable/menu_icon_control_normal_24dp"
                app:navigationContentDescription="@string/open_navigation_drawer"
                app:popupTheme="?actionBarPopupTheme"
                app:subtitleTextAppearance="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Small" />

            <me.zhanghai.android.files.ui.OverlayToolbar
                android:id="@+id/overlayToolbar"
                android:layout_width="match_parent"
                android:layout_height="?actionBarSize"
                android:paddingStart="@dimen/file_list_toolbar_padding_start"
                android:paddingEnd="@dimen/file_list_toolbar_padding_end_with_overflow"
                android:background="?colorAppBarSurface"
                android:visibility="gone"
                app:navigationIcon="@drawable/close_icon_control_normal_24dp"
                app:popupTheme="?actionBarPopupTheme" />
        </FrameLayout>

        <me.zhanghai.android.files.filelist.BreadcrumbLayout
            android:id="@+id/breadcrumbLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/content_start_margin_minus_12dp"
            android:paddingEnd="@dimen/screen_edge_margin_minus_12dp" />
    </me.zhanghai.android.files.ui.CoordinatorAppBarLayout>
</merge>
