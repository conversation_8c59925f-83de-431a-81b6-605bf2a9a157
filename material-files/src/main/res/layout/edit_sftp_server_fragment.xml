<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2021 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <me.zhanghai.android.files.ui.CoordinatorAppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?colorAppBarSurface"
        android:theme="?actionBarTheme"
        app:liftOnScroll="true"
        app:liftOnScrollTargetViewId="@id/scrollView">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            app:popupTheme="?actionBarPopupTheme" />
    </me.zhanghai.android.files.ui.CoordinatorAppBarLayout>

    <me.zhanghai.android.files.ui.CoordinatorScrollingLinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <ProgressBar
                android:id="@+id/progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="vertical"
                android:scrollIndicators="bottom">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/screen_edge_margin"
                    android:paddingEnd="@dimen/screen_edge_margin"
                    android:paddingTop="@dimen/screen_edge_margin"
                    android:paddingBottom="@dimen/screen_edge_margin_minus_16dp"
                    android:animateLayoutChanges="true"
                    android:orientation="vertical">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/hostLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_host"
                        app:errorEnabled="true">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/hostEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textUri">
                            <requestFocus />
                        </com.google.android.material.textfield.TextInputEditText>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/portLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_port"
                        app:errorEnabled="true"
                        app:expandedHintEnabled="false"
                        app:placeholderText="@string/storage_edit_sftp_server_port_placeholder">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/portEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_path"
                        app:errorEnabled="true"
                        app:expandedHintEnabled="false"
                        app:placeholderText="@string/storage_edit_sftp_server_path_placeholder">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/pathEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textUri" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/nameLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_name"
                        app:errorEnabled="true"
                        app:expandedHintEnabled="false"
                        app:placeholderText="@string/storage_edit_sftp_server_name_placeholder">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/nameEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:importantForAutofill="no"
                            android:inputType="text" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/authenticationTypeLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_authentication_type"
                        app:errorEnabled="true"
                        app:expandedHintEnabled="false"
                        style="?textInputOutlinedExposedDropdownMenuStyle">

                        <AutoCompleteTextView
                            android:id="@+id/authenticationTypeEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/usernameLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_username"
                        app:errorEnabled="true">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/usernameEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/passwordLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/storage_edit_sftp_server_password"
                        app:endIconMode="password_toggle"
                        app:errorEnabled="true">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/passwordEdit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPassword" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <LinearLayout
                        android:id="@+id/publicKeyAuthenticationLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/privateKeyLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/storage_edit_sftp_server_private_key"
                            app:endIconContentDescription="@string/storage_edit_sftp_server_private_key_open_file"
                            app:endIconDrawable="@drawable/file_icon_white_24dp"
                            app:endIconMode="custom"
                            app:errorEnabled="true">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/privateKeyEdit"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textMultiLine" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/privateKeyPasswordLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/storage_edit_sftp_server_private_key_password"
                            app:endIconMode="password_toggle"
                            app:errorEnabled="true"
                            app:expandedHintEnabled="false"
                            app:placeholderText="@string/storage_edit_sftp_server_private_key_password_placeholder">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/privateKeyPasswordEdit"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPassword" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:paddingStart="@dimen/screen_edge_margin_minus_8dp"
            android:paddingEnd="@dimen/screen_edge_margin_minus_8dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/removeOrAddButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:text="@string/save"
                style="?buttonBarButtonStyle" />

            <Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@android:string/cancel"
                style="?buttonBarButtonStyle" />

            <Button
                android:id="@+id/saveOrConnectAndAddButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save"
                style="?buttonBarButtonStyle" />
        </LinearLayout>
    </me.zhanghai.android.files.ui.CoordinatorScrollingLinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
