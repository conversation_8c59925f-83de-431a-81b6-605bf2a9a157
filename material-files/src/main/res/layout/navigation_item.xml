<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<me.zhanghai.android.files.ui.CheckableForegroundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/itemLayout"
    android:layout_width="match_parent"
    android:layout_height="?listPreferredItemHeightSmall"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iconImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:id="@+id/textLayout"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:ellipsize="end"
            android:maxLines="1" />

        <me.zhanghai.android.files.ui.AutoGoneTextView
            android:id="@+id/subtitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:ellipsize="end"
            android:maxLines="1" />
    </LinearLayout>
</me.zhanghai.android.files.ui.CheckableForegroundLinearLayout>
