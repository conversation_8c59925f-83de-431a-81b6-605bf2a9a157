<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <me.zhanghai.android.files.ui.CoordinatorAppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?colorAppBarSurface"
        android:theme="?actionBarTheme"
        app:liftOnScroll="true"
        app:liftOnScrollTargetViewId="@id/recycler_view">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            app:popupTheme="?actionBarPopupTheme" />
    </me.zhanghai.android.files.ui.CoordinatorAppBarLayout>

    <me.zhanghai.android.files.ui.CoordinatorScrollingFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <fragment
            android:id="@+id/preferenceFragment"
            android:name="me.zhanghai.android.files.settings.StandardDirectoryListPreferenceFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </me.zhanghai.android.files.ui.CoordinatorScrollingFrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
