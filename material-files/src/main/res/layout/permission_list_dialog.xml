<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2021 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/abc_dialog_title_divider_material">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="?listPreferredItemHeight">

        <ProgressBar
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <TextView
            android:id="@+id/errorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:textAppearance="?textAppearanceListItem"
            android:visibility="gone" />

        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:text="@string/empty"
            android:textAppearance="?textAppearanceListItem"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:scrollIndicators="top|bottom"
            android:scrollbars="vertical" />
    </FrameLayout>
</FrameLayout>
