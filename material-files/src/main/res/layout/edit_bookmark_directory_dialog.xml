<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/abc_dialog_title_divider_material">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="vertical"
        android:scrollIndicators="top|bottom">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="?dialogPreferredPadding"
            android:paddingEnd="?dialogPreferredPadding"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/nameLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/navigation_edit_bookmark_directory_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/nameEdit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:importantForAutofill="no"
                    android:inputType="text">
                    <requestFocus />
                </com.google.android.material.textfield.TextInputEditText>
            </com.google.android.material.textfield.TextInputLayout>

            <me.zhanghai.android.files.ui.ReadOnlyTextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/navigation_edit_bookmark_directory_path">

                <me.zhanghai.android.files.ui.ReadOnlyTextInputEditText
                    android:id="@+id/pathText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </me.zhanghai.android.files.ui.ReadOnlyTextInputLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</FrameLayout>
