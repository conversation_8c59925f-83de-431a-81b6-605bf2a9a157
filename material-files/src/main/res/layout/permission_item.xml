<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2021 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<me.zhanghai.android.foregroundcompat.ForegroundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/itemLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?listPreferredItemHeightSmall"
    android:paddingStart="?dialogPreferredPadding"
    android:paddingEnd="?dialogPreferredPadding"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:foreground="?selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
        android:id="@+id/labelText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?textAppearanceListItemSecondary" />

    <TextView
        android:id="@+id/nameText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?textAppearanceListItemSecondary"
        android:textColor="?android:textColorSecondary" />

    <me.zhanghai.android.files.ui.AutoGoneTextView
        android:id="@+id/descriptionText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?textAppearanceListItemSecondary"
        android:textColor="?android:textColorSecondary" />
</me.zhanghai.android.foregroundcompat.ForegroundLinearLayout>
