<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?colorAppBarSurface">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:theme="?actionBarTheme"
            app:popupTheme="?actionBarPopupTheme" />
    </FrameLayout>
</FrameLayout>
