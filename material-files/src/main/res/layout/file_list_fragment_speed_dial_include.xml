<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.leinardi.android.speeddial.SpeedDialOverlayLayout
        android:id="@+id/speedDialOverlayLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="fill"
        android:fitsSystemWindows="true" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:elevation="@dimen/sd_open_elevation"
        android:fitsSystemWindows="true">

        <me.zhanghai.android.files.ui.ThemedSpeedDialView
            android:id="@+id/speedDialView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_margin="@dimen/screen_edge_margin_minus_16dp"
            app:sdMainFabAnimationRotateAngle="135"
            app:sdMainFabClosedSrc="@drawable/add_icon_white_24dp"
            app:sdOverlayLayout="@id/speedDialOverlayLayout" />
    </FrameLayout>
</merge>
