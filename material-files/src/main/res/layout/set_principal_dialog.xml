<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/abc_dialog_title_divider_material"
    android:orientation="vertical">

    <EditText
        android:id="@+id/filterEdit"
        android:layout_width="match_parent"
        android:layout_height="?listPreferredItemHeightSmall"
        android:paddingStart="?dialogPreferredPadding"
        android:paddingEnd="?dialogPreferredPadding"
        android:background="@null"
        android:drawablePadding="24dp"
        app:drawableStartCompat="@drawable/filter_icon_control_normal_24dp"
        android:hint="@string/file_properties_permissions_set_principal_filter_hint"
        android:importantForAutofill="no"
        android:inputType="text"
        android:textAppearance="?textAppearanceListItem" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:minHeight="?listPreferredItemHeight"
        android:layout_weight="1">

        <ProgressBar
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <TextView
            android:id="@+id/errorText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:textAppearance="?textAppearanceListItem"
            android:visibility="gone" />

        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:text="@string/empty"
            android:textAppearance="?textAppearanceListItem"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:scrollIndicators="top|bottom"
            android:scrollbars="vertical" />
    </FrameLayout>

    <CheckBox
        android:id="@+id/recursiveCheck"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="?listPreferredItemHeightSmall"
        android:layout_marginStart="@dimen/dialog_padding_minus_4dp"
        android:paddingStart="@dimen/dialog_padding_minus_4dp"
        android:paddingEnd="?dialogPreferredPadding"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text="@string/file_properties_permissions_recursive"
        android:textAppearance="?textAppearanceListItem" />
</LinearLayout>
