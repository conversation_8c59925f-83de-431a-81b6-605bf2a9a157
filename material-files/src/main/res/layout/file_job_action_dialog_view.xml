<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="?dialogPreferredPadding"
    android:paddingEnd="?dialogPreferredPadding"
    android:clipToPadding="false"
    android:orientation="vertical">

    <Button
        android:id="@+id/remountButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-8dp"
        android:layout_marginEnd="-8dp"
        style="?borderlessButtonStyle" />

    <Space
        android:id="@+id/allSpace"
        android:layout_width="0dp"
        android:layout_height="8dp" />

    <CheckBox
        android:id="@+id/allCheck"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="-7dp"
        android:paddingStart="@dimen/dialog_padding_minus_7dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text="@string/file_job_action_all"
        android:textAppearance="?textAppearanceListItem" />
</LinearLayout>
