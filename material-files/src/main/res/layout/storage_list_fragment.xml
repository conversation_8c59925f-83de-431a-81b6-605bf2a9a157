<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2020 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <me.zhanghai.android.files.ui.CoordinatorAppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?colorAppBarSurface"
        android:theme="?actionBarTheme"
        app:liftOnScroll="true"
        app:liftOnScrollTargetViewId="@id/recyclerView">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            app:popupTheme="?actionBarPopupTheme" />
    </me.zhanghai.android.files.ui.CoordinatorAppBarLayout>

    <me.zhanghai.android.files.ui.CoordinatorScrollingFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/storage_list_empty"
            android:textAppearance="?textAppearanceListItem"
            android:visibility="gone" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:scrollbars="vertical" />

        <!-- Additional layout to receive window insets as margin. -->
        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/screen_edge_margin"
                android:src="@drawable/add_icon_white_24dp"
                android:theme="?actionBarTheme" />
        </FrameLayout>
    </me.zhanghai.android.files.ui.CoordinatorScrollingFrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
