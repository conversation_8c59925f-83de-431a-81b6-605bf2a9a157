<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2019 <PERSON> <<EMAIL>>
  ~ All Rights Reserved.
  -->

<me.zhanghai.android.files.ui.CheckableForegroundLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/itemLayout"
    android:layout_width="match_parent"
    android:layout_height="?listPreferredItemHeight"
    android:paddingStart="?dialogPreferredPadding"
    android:paddingEnd="?dialogPreferredPadding"
    android:foreground="?selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iconImage"
        android:layout_width="@dimen/icon_size"
        android:layout_height="@dimen/icon_size"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:layout_marginStart="?dialogPreferredPadding"
        android:layout_marginEnd="?dialogPreferredPadding"
        android:orientation="vertical">

        <TextView
            android:id="@+id/principalText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?textAppearanceListItemSecondary" />

        <me.zhanghai.android.files.ui.AutoGoneTextView
            android:id="@+id/labelText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAppearance="?textAppearanceListItemSecondary"
            android:textColor="?android:textColorSecondary" />
    </LinearLayout>

    <RadioButton
        android:id="@+id/radio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="-4dp"
        android:clickable="false"
        android:duplicateParentState="true"
        android:focusable="false" />
</me.zhanghai.android.files.ui.CheckableForegroundLinearLayout>
