<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ Copyright (c) 2018 <PERSON> Zhang <<EMAIL>>
  ~ All Rights Reserved.
  -->

<notices>

    <notice>
        <name>AndroidSVG</name>
        <url>https://github.com/BigBadaboom/androidsvg</url>
        <copyright>Copyright 2013 <PERSON>, Cave Rock Software Ltd.</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>SubsamplingScaleImageView</name>
        <url>https://github.com/davemorrissey/subsampling-scale-image-view</url>
        <copyright>Copyright 2016 <PERSON></copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>FullDraggableDrawer</name>
        <url>https://github.com/PureWriter/FullDraggableDrawer</url>
        <copyright>Copyright 2021 Drakeet Xu</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>Stetho</name>
        <url>https://github.com/facebook/stetho</url>
        <copyright>Copyright 2015 Facebook Inc.</copyright>
        <license>BSD 3-Clause License</license>
    </notice>

    <notice>
        <name>PhotoView</name>
        <url>https://github.com/chrisbanes/PhotoView</url>
        <copyright>Copyright 2011, 2012 Chris Banes</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>libsu</name>
        <url>https://github.com/topjohnwu/libsu</url>
        <copyright>Copyright 2021 John "topjohnwu" Wu</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>Material Shadow 9-Patch</name>
        <url>https://github.com/h6ah4i/android-materialshadowninepatch</url>
        <copyright>Copyright 2015 Haruki Hasegawa</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>Advanced RecyclerView</name>
        <url>https://github.com/h6ah4i/android-advancedrecyclerview</url>
        <copyright>Copyright 2015 Haruki Hasegawa</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>SMBJ</name>
        <url>https://github.com/hierynomus/smbj</url>
        <copyright>Copyright 2016 SMBJ Contributors</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>ThreeTenABP</name>
        <url>https://github.com/JakeWharton/ThreeTenABP</url>
        <copyright>Copyright 2015 Jake Wharton</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>Floating Action Button Speed Dial</name>
        <url>https://github.com/leinardi/FloatingActionButtonSpeedDial</url>
        <copyright>Copyright 2018 Roberto Leinardi</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>SMBJ-RPC</name>
        <url>https://github.com/rapid7/smbj-rpc</url>
        <copyright>Copyright 2017 Rapid7 Inc.</copyright>
        <license>BSD 3-Clause License</license>
    </notice>

    <notice>
        <name>Apache Commons Net</name>
        <url>https://commons.apache.org/proper/commons-net/</url>
        <copyright>Copyright 2003 The Apache Software Foundation</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>Insetter</name>
        <url>https://github.com/chrisbanes/insetter</url>
        <copyright>Copyright 2019 Google LLC</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>RikkaX</name>
        <url>https://github.com/RikkaApps/RikkaX</url>
        <copyright>Copyright 2017 Rikka</copyright>
        <license>MIT License</license>
    </notice>

    <notice>
        <name>Shizuku-API</name>
        <url>https://github.com/RikkaApps/Shizuku-API</url>
        <copyright>Copyright 2021 RikkaW</copyright>
        <license>MIT License</license>
    </notice>

    <notice>
        <name>jcifs-ng</name>
        <url>https://github.com/AgNO3/jcifs-ng</url>
        <copyright>Copyright 2016 AgNO3 Gmbh &amp; Co. KG</copyright>
        <license>GNU Lesser General Public License 2.1</license>
    </notice>

    <notice>
        <name>Coil</name>
        <url>https://github.com/coil-kt/coil</url>
        <copyright>Copyright 2020 Coil Contributors</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>AppIconLoader</name>
        <url>https://github.com/zhanghai/AppIconLoader</url>
        <copyright>Copyright 2020 Google LLC</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>AndroidFastScroll</name>
        <url>https://github.com/zhanghai/AndroidFastScroll</url>
        <copyright>Copyright 2019 Google LLC</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>AndroidForegroundCompat</name>
        <url>https://github.com/google/AndroidForegroundCompat</url>
        <copyright>Copyright 2019 Google LLC</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>libselinux-android</name>
        <url>https://github.com/zhanghai/libselinux-android</url>
        <copyright>Copyright 2019 Hai Zhang</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>AndroidRetroFile</name>
        <url>https://github.com/zhanghai/AndroidRetroFile</url>
        <copyright>Copyright 2018 Hai Zhang</copyright>
        <license>GNU General Public License 2.0</license>
    </notice>

    <notice>
        <name>SystemUiHelper</name>
        <url>https://github.com/zhanghai/SystemUiHelper</url>
        <copyright>Copyright 2015 Hai Zhang</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>android-retrostreams</name>
        <url>https://github.com/retrostreams/android-retrostreams</url>
        <copyright>Copyright 1994 Oracle and/or its affiliates</copyright>
        <license>GNU General Public License 2.0</license>
    </notice>

    <notice>
        <name>Apache Commons Compress</name>
        <url>https://commons.apache.org/proper/commons-compress/</url>
        <copyright>Copyright 2002 The Apache Software Foundation</copyright>
        <license>Apache Software License 2.0</license>
    </notice>

    <notice>
        <name>Apache FtpServer</name>
        <url>https://mina.apache.org/ftpserver-project/index.html</url>
        <copyright>Copyright 2003 The Apache Software Foundation</copyright>
        <license>Apache Software License 2.0</license>
    </notice>
</notices>
