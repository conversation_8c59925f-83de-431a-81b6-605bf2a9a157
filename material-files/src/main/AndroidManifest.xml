<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"> <!-- <PERSON><PERSON><PERSON> requires API 23. -->
    <uses-sdk tools:overrideLibrary="rikka.shizuku.aidl,rikka.shizuku.api,rikka.shizuku.shared" />

    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="30" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />

<!--    <application
        android:allowBackup="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="true"
        android:icon="@mipmap/launcher_icon"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:requestRawExternalStorageAccess="true"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/launcher_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.MaterialFiles"
        tools:ignore="GoogleAppIndexingWarning,UnusedAttribute">-->
    <application>
        <!--
          ~ Using android:documentLaunchMode="always" gives a better result than
          ~ Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_MULTIPLE_TASK. Not sure why though.
        -->
        <activity
            android:name=".filelist.OpenFileActivity"
            android:documentLaunchMode="always"
            android:excludeFromRecents="true"
            android:exported="true"
            android:theme="@style/Theme.MaterialFiles.Translucent">
            <intent-filter>
                <action android:name="me.zhanghai.android.files.intent.action.OPEN_FILE" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="*/*" />
            </intent-filter>
        </activity>
        <activity
            android:name=".filelist.EditFileActivity"
            android:autoRemoveFromRecents="true"
            android:icon="@drawable/edit_icon"
            android:label="@string/file_edit_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".filelist.OpenFileAsDialogActivity"
            android:autoRemoveFromRecents="true"
            android:icon="@drawable/open_as_icon"
            android:label="@string/file_open_as_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".storage.AddStorageDialogActivity"
            android:label="@string/storage_add_storage_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".storage.EditDeviceStorageDialogActivity"
            android:label="@string/storage_edit_device_storage_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".storage.AddDocumentTreeActivity"
            android:label="@string/storage_add_document_tree_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".storage.EditDocumentTreeDialogActivity"
            android:label="@string/storage_edit_document_tree_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".storage.EditFtpServerActivity"
            android:label="@string/storage_edit_ftp_server_title_edit"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".storage.EditSftpServerActivity"
            android:label="@string/storage_edit_sftp_server_title_edit"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".storage.AddLanSmbServerActivity"
            android:label="@string/storage_add_lan_smb_server_title"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".storage.EditSmbServerActivity"
            android:label="@string/storage_edit_smb_server_title_edit"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".navigation.EditBookmarkDirectoryDialogActivity"
            android:label="@string/navigation_edit_bookmark_directory_title"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".ftpserver.FtpServerActivity"
            android:exported="true"
            android:label="@string/ftp_server_title"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MaterialFiles">
            <intent-filter>
                <action android:name="me.zhanghai.android.files.intent.action.MANAGE_FTP_SERVER" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".settings.SettingsActivity"
            android:exported="true"
            android:label="@string/settings_title"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MaterialFiles">
            <intent-filter>
                <action android:name="android.intent.action.APPLICATION_PREFERENCES" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".storage.StorageListActivity"
            android:label="@string/storage_list_title"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".settings.StandardDirectoryListActivity"
            android:label="@string/settings_standard_directory_list_title"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".settings.BookmarkDirectoryListActivity"
            android:label="@string/settings_bookmark_directory_list_title"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".about.AboutActivity"
            android:label="@string/about_title"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MaterialFiles" />
        <activity
            android:name=".filejob.FileJobActionDialogActivity"
            android:autoRemoveFromRecents="true"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".filejob.FileJobConflictDialogActivity"
            android:autoRemoveFromRecents="true"
            android:theme="@style/Theme.MaterialFiles.Translucent" />
        <activity
            android:name=".viewer.text.TextEditorActivity"
            android:exported="true"
            android:label="@string/text_editor_title"
            android:theme="@style/Theme.MaterialFiles">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="application/ecmascript" />
                <data android:mimeType="application/javascript" />
                <data android:mimeType="application/json" />
                <data android:mimeType="application/typescript" />
                <data android:mimeType="application/x-sh" />
                <data android:mimeType="application/x-shellscript" />
                <data android:mimeType="application/xml" />
                <data android:mimeType="text/*" />
            </intent-filter>
        </activity>
        <activity
            android:name=".viewer.image.ImageViewerActivity"
            android:exported="true"
            android:label="@string/image_viewer_title"
            android:theme="@style/Theme.MaterialFiles.Immersive">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="image/*" />
            </intent-filter>
        </activity>

        <service android:name=".filejob.FileJobService"
            android:process=":report"
            />
        <service android:name=".ftpserver.FtpServerService"
            android:process=":report"/>
        <service
            android:name=".ftpserver.FtpServerTileService"
            android:exported="true"
            android:process=":report"
            android:icon="@drawable/shared_directory_icon_white_24dp"
            android:label="@string/ftp_server_title"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>

            <meta-data
                android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                android:value="true" />
        </service>

        <provider
            android:name=".app.AppProvider"
            android:authorities="@string/app_provider_authority"
            android:process=":report"
            android:exported="false" />
        <provider
            android:name=".file.FileProvider"
            android:authorities="@string/file_provider_authority"
            android:process=":report"
            android:exported="false"
            android:grantUriPermissions="true" />

        <receiver android:name=".filejob.FileJobReceiver"
            android:process=":report"/>
        <receiver android:name=".ftpserver.FtpServerReceiver"
            android:process=":report"/>

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />
    </application>

</manifest>