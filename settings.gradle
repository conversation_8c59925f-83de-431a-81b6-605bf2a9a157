pluginManagement {
    repositories {
        maven {
            allowInsecureProtocol = true
            url "http://repository.vrtbbs.com/repository/maven-public/"
        }
        mavenCentral()
        gradlePluginPortal()
        google()
    }

    plugins {
        id 'com.android.application' version '7.2.1'
        id 'com.android.library' version '7.2.1'
        id 'org.jetbrains.kotlin.android' version '1.7.10'
        id 'com.google.dagger.hilt.android' version '2.44' apply false
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven {
            allowInsecureProtocol = true
            url "http://repository.vrtbbs.com/repository/maven-public/"
        }
        maven { url "https://jitpack.io" }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.aliyun.com/nexus/content/repositories/releases/'
        }
        mavenCentral()
        google()
        mavenCentral()
    }
}
rootProject.name = "WxCollect"
include ':app'
include ':material-files'

include ':share:cache'
include ':share:keepalive'
include ':share:point'

def localPropertiesFile = new File(rootProject.projectDir, 'local.properties')
if (localPropertiesFile.exists()) {
    def properties = new Properties()
    properties.load(new FileInputStream(localPropertiesFile))
    def shareDir = properties.getProperty('share.dir')
    println "share Dir: $shareDir"
    ext.shareDir = shareDir
} else {
    throw new GradleException("local.properties file not found")
}
println "shareDir: ${new File("${ext.shareDir}\\share\\keepalive").absolutePath}"
project(':share:cache').projectDir = new File("${ext.shareDir}\\share\\cache")
project(':share:keepalive').projectDir = new File("${ext.shareDir}\\share\\keepalive")
project(':share:point').projectDir = new File("${ext.shareDir}\\share\\point")